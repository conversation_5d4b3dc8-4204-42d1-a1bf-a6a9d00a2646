package ca.medit.learnerchart.security;

import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.repository.UserRepository;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Component
public class AsUtoridFilter extends OncePerRequestFilter {

    @Autowired
    private UserRepository userRepository;

    // Define role priority order (highest to lowest)

    private static final List<String> ROLE_PRIORITY = List.of(
            "System-Administrator",
            "Pseudo-Admin",
            "UME-Administrator",
            "Promotion-committee",
            "Learner-supporter",
            "Scholar",
            "Learner"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        //String asutorid = request.getParameter("asutorid");
        String utorid = request.getHeader("utorid");
        String isMemberOf = request.getHeader("isMemberOf");

        if (utorid != null && !utorid.isEmpty()) {
            if (isMemberOf == null || isMemberOf.trim().isEmpty()) {
                response.sendError(HttpStatus.FORBIDDEN.value(), "isMemberOf attribute is empty");
                return;
            }
            CustomUserDetails userDetails = findUserByUtorid(utorid);
            if (userDetails != null) {
                List<SimpleGrantedAuthority> authorities = parseAuthorities(isMemberOf);
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userDetails, null, authorities);
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
            } else {
                response.sendError(HttpStatus.UNAUTHORIZED.value(), "User not found for utorid: " + utorid);
                return;
            }
        }

        chain.doFilter(request, response);
    }

    private CustomUserDetails findUserByUtorid(String utorid) {
        User user = userRepository.findByUsername(utorid).orElseGet(() -> {
            User newUser = new User();
            newUser.setUsername(utorid);
            newUser.setUtorid(utorid);
            newUser.setFailedLoginAttempts(0);
            newUser.setEnabled(true);
            newUser.setOptlock(1L);
            newUser.setPassword("$2a$12$rPdd6RIowNpxpJFMVnSaJ.V3ZY/3ALIpvHj8sPokYzOo44MYjnGJe");
            // Set other default fields as needed
            return userRepository.save(newUser);
        });
        return new CustomUserDetails(user);
    }

    private List<SimpleGrantedAuthority> parseAuthorities(String isMemberOf) {

        // If isMemberOf is empty, return no authorities
        if (isMemberOf == null || isMemberOf.trim().isEmpty()) {
            return Collections.emptyList();
        }

        // Split the isMemberOf string by semicolons
        String[] groups = isMemberOf.split(";");

        // Find the highest role according to priority
        String highestRole = null;
        int highestPriority = Integer.MAX_VALUE;


        for (String group : groups) {
            // Extract the cn value (e.g., System-Administrator)
            String cn = extractCn(group);
            if (cn != null) {
                int priority = ROLE_PRIORITY.indexOf(cn);
                if (priority >= 0 && priority < highestPriority) {
                    highestRole = cn;
                    highestPriority = priority;
                }
            }
        }

        // If a role was found, map it to a Spring Security authority
        if (highestRole != null) {
            return List.of(new SimpleGrantedAuthority("ROLE_" + highestRole.toUpperCase().replace("-", "_")));
        }

        // No recognized roles found
        return Collections.emptyList();
    }

    private String extractCn(String group) {
        // Extract the cn value from the DN (e.g., cn=System-Administrator,ou=...)
        if (group == null || group.isEmpty()) {
            return null;
        }
        String[] parts = group.split(",");
        for (String part : parts) {
            if (part.startsWith("cn=")) {
                return part.substring(3).trim(); // Remove "cn=" prefix
            }
        }
        return null;
    }

}