package ca.medit.learnerchart.dto;

import org.joda.time.LocalDateTime;

public class AverageNumericScore {


    public long id;
    public String name;
    public double pointsAvailable;
    public double pointsEarned;
    public LocalDateTime date;

    public AverageNumericScore(Long id) {
        this.id = id.longValue();
    }

    /**
     * @return the id
     */
    public long getId() {
        return id;
    }

    /**
     * @param id
     *            the id to set
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name
     *            the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the date
     */
    public LocalDateTime getDate() {
        return date;
    }

    public double getPointsAvailable() {
        return pointsAvailable;
    }

    public void setPointsAvailable(double pointsAvailable) {
        this.pointsAvailable = pointsAvailable;
    }

    public double getPointsEarned() {
        return pointsEarned;
    }

    public void setPointsEarned(double pointsEarned) {
        this.pointsEarned = pointsEarned;
    }

    /**
     * @param date
     *            the date to set
     */
    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    /**
     * @return the averageScore
     */
    public double getAverageScore() {
        return (pointsEarned / pointsAvailable) * 100d;
    }

    public void add(AverageNumericScore score) {
        this.pointsAvailable += score.pointsAvailable;
        this.pointsEarned += score.pointsEarned;
    }

    @Override
    public int hashCode() {
        return Long.valueOf(this.id).hashCode();
    }

    @Override
    public boolean equals(Object rhs) {
        if (this == rhs) {
            return true;
        }
        if (!(rhs instanceof final AverageNumericScore we)) {
            return false;
        }

        return Long.valueOf(this.id).equals(we.getId());
    }

    @Override
    public String toString() {
        return "ExerciseAverageScore [id=" + id + ", name=" + name + ", pointsAvailable=" + pointsAvailable
                + ", pointsEarned=" + pointsEarned + ", date=" + date + "]";
    }


}
