package ca.medit.learnerchart.dto;

import org.joda.time.LocalDateTime;

import java.io.Serializable;

public class SubmissionFileInfo implements Serializable {


    private Long id;
    private Long fileLength;
    private String fileName;
    private LocalDateTime submissionDate;
    private Long submissionId;

    public SubmissionFileInfo() {
    }

    public SubmissionFileInfo(Long id, Long fileLength, String fileName, LocalDateTime submissionDate,
                              Long submissionId) {
        this.id = id;
        this.fileLength = fileLength;
        this.fileName = fileName;
        this.submissionDate = submissionDate;
        this.submissionId = submissionId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFileLength() {
        return fileLength;
    }

    public void setFileLength(Long fileLength) {
        this.fileLength = fileLength;
    }
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDateTime getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(LocalDateTime submissionDate) {
        this.submissionDate = submissionDate;
    }

    public Long getSubmissionId() {
        return submissionId;
    }

    public void setSubmissionId(Long submissionId) {
        this.submissionId = submissionId;
    }


}
