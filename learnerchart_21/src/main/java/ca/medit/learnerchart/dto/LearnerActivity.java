package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.domain.ActivityScoreEnum;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Score;

import java.io.Serializable;

import org.joda.time.LocalDateTime;

public class LearnerActivity implements Serializable {


    protected Long scoreId;
    protected ActivityScoreEnum scoreEnum;
    protected Long assessmentId;
    protected Long remoteId;
    protected String assessmentName;
    protected String rawScore;
    protected double pointsAvailable;
    protected double pointsEarned;
    protected int activityCount;
    protected int questionCount;
    protected LocalDateTime date;
    protected String assessmentType;
    protected String parentName;
    protected String parentRawScore;
    protected ActivityScoreEnum parentScoreEnum;
    protected String reference;
    protected String courseCode;
    protected String supervisorFirstName;
    protected String supervisorLastName;
    protected int academicYear;
    protected int programYear;
    protected FeedSourceTypeEnum source;

    public LearnerActivity() {
    }

    public LearnerActivity(Long id) {
        this.assessmentId = id;
    }

    /**
     * Create a final score value for Timeline
     *
     * @param score
     * @return the score
     */
    public static LearnerActivity finalScoreForRawValue(Score score) {
        if(score == null) {
            throw new IllegalArgumentException("Score must not be null");
        }
        LearnerActivity activity = null;
        if ("FINAL_SCORE_XID".equals(score.getCplanXid())) {
            activity = new LearnerActivity();
            activity.setScoreEnum(ActivityScoreEnum.getEnumFromScore(score));
            activity.setRawScore(score.getRawValue());
        }
        return activity;
    }

    /**
     * @return the assessmentId
     */
    public Long getAssessmentId() {
        return assessmentId;
    }

    /**
     * @param assessmentId
     *            the assessmentId to set
     */
    public void setAssessmentId(Long assessmentId) {
        this.assessmentId = assessmentId;
    }

    public Long getRemoteId() {
        return remoteId;
    }

    public void setRemoteId(Long remoteId) {
        this.remoteId = remoteId;
    }

    /**
     * @return the assessmentName
     */
    public String getAssessmentName() {
        return assessmentName;
    }

    /**
     * @param assessmentName
     *            the assessmentName to set
     */
    public void setAssessmentName(String assessmentName) {
        this.assessmentName = assessmentName;
    }

    /**
     * @return the rawScore
     */
    public String getRawScore() {
        return rawScore;
    }

    /**
     * @param rawScore
     *            the rawScore to set
     */
    public void setRawScore(String rawScore) {
        this.rawScore = rawScore;
    }

    /**
     * @return the pointsAvailable
     */
    public double getPointsAvailable() {
        return pointsAvailable;
    }

    /**
     * @param pointsAvailable
     *            the pointsAvailable to set
     */
    public void setPointsAvailable(double pointsAvailable) {
        this.pointsAvailable = pointsAvailable;
    }

    /**
     * @return the pointsEarned
     */
    public double getPointsEarned() {
        return pointsEarned;
    }

    /**
     * @param pointsEarned
     *            the pointsEarned to set
     */
    public void setPointsEarned(double pointsEarned) {
        this.pointsEarned = pointsEarned;
    }

    /**
     * @return the activityCount
     */
    public int getActivityCount() {
        return activityCount;
    }

    /**
     * @param activityCount
     *            the activityCount to set
     */
    public void setActivityCount(int activityCount) {
        this.activityCount = activityCount;
    }

    /**
     * @return the questionsCount
     */
    public int getQuestionCount() {
        return questionCount;
    }

    /**
     * @param questionsCount
     *            the questionsCount to set
     */
    public void setQuestionCount(int questionsCount) {
        this.questionCount = questionsCount;
    }

    /**
     * @return the date
     */
    public LocalDateTime getDate() {
        return date;
    }

    /**
     * @param date
     *            the date to set
     */
    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public int getAcademicYear() {
        return academicYear;
    }

    public void setAcademicYear(int academicYear) {
        this.academicYear = academicYear;
    }

    public int getProgramYear() {
        return programYear;
    }

    public void setProgramYear(int programYear) {
        this.programYear = programYear;
    }

    /**
     *
     * @return
     */
    public String getAssessmentType() {
        return assessmentType;
    }

    /**
     *
     * @param assessmentType
     */
    public void setAssessmentType(String assessmentType) {
        this.assessmentType = assessmentType;
    }

    public ActivityScoreEnum getScoreEnum() {
        return scoreEnum;
    }

    public void setScoreEnum(ActivityScoreEnum scoreEnum) {
        this.scoreEnum = scoreEnum;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getParentRawScore() {
        return parentRawScore;
    }

    public void setParentRawScore(String parentRawScore) {
        this.parentRawScore = parentRawScore;
    }

    public ActivityScoreEnum getParentScoreEnum() {
        return parentScoreEnum;
    }

    public void setParentScoreEnum(ActivityScoreEnum parentScoreEnum) {
        this.parentScoreEnum = parentScoreEnum;
    }

    public Long getScoreId() {
        return scoreId;
    }

    public void setScoreId(Long scoreId) {
        this.scoreId = scoreId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getSupervisorFirstName() {
        return supervisorFirstName;
    }

    public void setSupervisorFirstName(String supervisorFirstName) {
        this.supervisorFirstName = supervisorFirstName;
    }

    public String getSupervisorLastName() {
        return supervisorLastName;
    }

    public void setSupervisorLastName(String supervisorLastName) {
        this.supervisorLastName = supervisorLastName;
    }

    public FeedSourceTypeEnum getSource() {
        return source;
    }

    public void setSource(FeedSourceTypeEnum source) {
        this.source = source;
    }


}
