package ca.medit.learnerchart.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class UserResponse {
    private Data data;

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private int id;
        private String type;
        private Attributes attributes;
        private Relationships relationships;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Attributes getAttributes() {
            return attributes;
        }

        public void setAttributes(Attributes attributes) {
            this.attributes = attributes;
        }

        public Relationships getRelationships() {
            return relationships;
        }

        public void setRelationships(Relationships relationships) {
            this.relationships = relationships;
        }

        public static class Attributes {
            private String firstName;
            private String lastName;
            private String email;
            private String utorid;
            private boolean canAddSupportingDocument;
            private boolean canAddFocusedLearningPlan;
            private List<Integer> lastLogin;

            private Alerts alerts;

            public String getFirstName() {
                return firstName;
            }

            public void setFirstName(String firstName) {
                this.firstName = firstName;
            }

            public String getLastName() {
                return lastName;
            }

            public void setLastName(String lastName) {
                this.lastName = lastName;
            }

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }

            public String getUtorid() {
                return utorid;
            }

            public void setUtorid(String utorid) {
                this.utorid = utorid;
            }

            public boolean isCanAddSupportingDocument() {
                return canAddSupportingDocument;
            }

            public void setCanAddSupportingDocument(boolean canAddSupportingDocument) {
                this.canAddSupportingDocument = canAddSupportingDocument;
            }

            public boolean isCanAddFocusedLearningPlan() {
                return canAddFocusedLearningPlan;
            }

            public void setCanAddFocusedLearningPlan(boolean canAddFocusedLearningPlan) {
                this.canAddFocusedLearningPlan = canAddFocusedLearningPlan;
            }

            public List<Integer> getLastLogin() {
                return lastLogin;
            }

            public void setLastLogin(List<Integer> lastLogin) {
                this.lastLogin = lastLogin;
            }

            private List<Integer> convertToList(LocalDateTime dateTime) {
                List<Integer> dateTimeList = new ArrayList<>();
                dateTimeList.add(dateTime.getYear());
                dateTimeList.add(dateTime.getMonthValue());
                dateTimeList.add(dateTime.getDayOfMonth());
                dateTimeList.add(dateTime.getHour());
                dateTimeList.add(dateTime.getMinute());
                dateTimeList.add(dateTime.getSecond());
                dateTimeList.add(dateTime.getNano());
                return dateTimeList;
            }


            public Alerts getAlerts() {
                return alerts;
            }

            public void setAlerts(Alerts alerts) {
                this.alerts = alerts;
            }

            public static class Alerts {
                private List<Object> data;

                public List<Object> getData() {
                    return data;
                }

                public void setData(List<Object> data) {
                    this.data = data;
                }
            }
        }

        public static class Relationships {
            private Groups groups;

            public Groups getGroups() {
                return groups;
            }

            public void setGroups(Groups groups) {
                this.groups = groups;
            }

            public static class Groups {
                private List<GroupItem> data;

                public List<GroupItem> getData() {
                    return data;
                }

                public void setData(List<GroupItem> data) {
                    this.data = data;
                }

                public static class GroupItem {
                    private int id;
                    private String type;
                    private Attributes attributes;

                    public int getId() {
                        return id;
                    }

                    public void setId(int id) {
                        this.id = id;
                    }

                    public String getType() {
                        return type;
                    }

                    public void setType(String type) {
                        this.type = type;
                    }

                    public Attributes getAttributes() {
                        return attributes;
                    }

                    public void setAttributes(Attributes attributes) {
                        this.attributes = attributes;
                    }

                    public static class Attributes {
                        private String name;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }
                    }
                }
            }
        }
    }
}
