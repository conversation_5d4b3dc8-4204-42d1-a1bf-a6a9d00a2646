package ca.medit.learnerchart.dto;

import java.time.LocalDateTime;

public class CanMedsExerciseItem {

    private Long scoreId;
    private float pointsEarned;
    private float pointsAvailable;
    private String rawScore;
    private int numberOfCorrects;
    private int numberOfItems;

    private Long cplanXid;
    private String cplanLabel;
    private String cplanValue;
    private String frameworkDef;

    private Long assessmentId;
    private Long remoteId;
    private String assessmentName;
    private LocalDateTime assessmentDueDate;
    private String assessmentType;
    private String reference;
    private String courseCode;
    private String supervisorFirstName;
    private String supervisorLastName;
    private int academicYear;
    private int programYear;

    public CanMedsExerciseItem() {
    }

    public CanMedsExerciseItem(long assessmentId) {
        this.assessmentId = assessmentId;
    }

    public Long getScoreId() {
        return scoreId;
    }

    public void setScoreId(Long scoreId) {
        this.scoreId = scoreId;
    }

    /**
     * @return the pointsEarned
     */
    public float getPointsEarned() {
        return pointsEarned;
    }

    /**
     * @param pointsEarned the pointsEarned to set
     */
    public void setPointsEarned(float pointsEarned) {
        this.pointsEarned = pointsEarned;
    }

    /**
     * @return the pointsAvailable
     */
    public float getPointsAvailable() {
        return pointsAvailable;
    }

    /**
     * @param pointsAvailable the pointsAvailable to set
     */
    public void setPointsAvailable(float pointsAvailable) {
        this.pointsAvailable = pointsAvailable;
    }

    public String getRawScore() {
        return rawScore;
    }

    public void setRawScore(String rawScore) {
        this.rawScore = rawScore;
    }

    /**
     * @return the numberOfCorrects
     */
    public int getNumberOfCorrects() {
        return numberOfCorrects;
    }

    /**
     * @param numberOfCorrects the numberOfCorrects to set
     */
    public void setNumberOfCorrects(int numberOfCorrects) {
        this.numberOfCorrects = numberOfCorrects;
    }

    /**
     * @return the numberOfItems
     */
    public int getNumberOfItems() {
        return numberOfItems;
    }

    /**
     * @param numberOfItems the numberOfItems to set
     */
    public void setNumberOfItems(int numberOfItems) {
        this.numberOfItems = numberOfItems;
    }

    /**
     * @return the cplanXid
     */
    public Long getCplanXid() {
        return cplanXid;
    }

    /**
     * @param cplanXid the cplanXid to set
     */
    public void setCplanXid(Long cplanXid) {
        this.cplanXid = cplanXid;
    }

    /**
     * @return the cplanLabel
     */
    public String getCplanLabel() {
        return cplanLabel;
    }

    /**
     * @param cplanLabel the cplanLabel to set
     */
    public void setCplanLabel(String cplanLabel) {
        this.cplanLabel = cplanLabel;
    }

    /**
     * @return the cplanValue
     */
    public String getCplanValue() {
        return cplanValue;
    }

    /**
     * @param cplanValue the cplanValue to set
     */
    public void setCplanValue(String cplanValue) {
        this.cplanValue = cplanValue;
    }

    /**
     * @return the frameworkDef
     */
    public String getFrameworkDef() {
        return frameworkDef;
    }

    /**
     * @param frameworkDef the frameworkDef to set
     */
    public void setFrameworkDef(String frameworkDef) {
        this.frameworkDef = frameworkDef;
    }

    /**
     * @return the assessmentId
     */
    public Long getAssessmentId() {
        return assessmentId;
    }

    /**
     * @param assessmentId the assessmentId to set
     */
    public void setAssessmentId(Long assessmentId) {
        this.assessmentId = assessmentId;
    }

    public Long getRemoteId() {
        return remoteId;
    }

    public void setRemoteId(Long remoteId) {
        this.remoteId = remoteId;
    }

    /**
     * @return the assessmentName
     */
    public String getAssessmentName() {
        return assessmentName;
    }

    /**
     * @param assessmentName the assessmentName to set
     */
    public void setAssessmentName(String assessmentName) {
        this.assessmentName = assessmentName;
    }

    /**
     * @return the assessmentDueDate
     */
    public LocalDateTime getAssessmentDueDate() {
        return assessmentDueDate;
    }

    /**
     * @param assessmentDueDate the assessmentDueDate to set
     */
    public void setAssessmentDueDate(LocalDateTime assessmentDueDate) {
        this.assessmentDueDate = assessmentDueDate;
    }

    /**
     * @return the assessmentType
     */
    public String getAssessmentType() {
        return assessmentType;
    }

    /**
     * @param assessmentType the assessmentType to set
     */
    public void setAssessmentType(String assessmentType) {
        this.assessmentType = assessmentType;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getSupervisorFirstName() {
        return supervisorFirstName;
    }

    public void setSupervisorFirstName(String supervisorFirstName) {
        this.supervisorFirstName = supervisorFirstName;
    }

    public String getSupervisorLastName() {
        return supervisorLastName;
    }

    public void setSupervisorLastName(String supervisorLastName) {
        this.supervisorLastName = supervisorLastName;
    }

    public int getAcademicYear() {
        return academicYear;
    }

    public void setAcademicYear(int academicYear) {
        this.academicYear = academicYear;
    }

    public int getProgramYear() {
        return programYear;
    }

    public void setProgramYear(int programYear) {
        this.programYear = programYear;
    }

    @Override
    public String toString() {
        return "CanMedsExerciseItem [scoreId=" + scoreId + ", pointsEarned=" + pointsEarned + ", pointsAvailable="
                + pointsAvailable + ", rawScore=" + rawScore + ", numberOfCorrects=" + numberOfCorrects
                + ", numberOfItems=" + numberOfItems + ", cplanXid=" + cplanXid + ", cplanLabel=" + cplanLabel
                + ", cplanValue=" + cplanValue + ", frameworkDef=" + frameworkDef + ", assessmentId=" + assessmentId
                + ", remoteId=" + remoteId + ", assessmentName=" + assessmentName + ", assessmentDueDate="
                + assessmentDueDate + ", assessmentType=" + assessmentType + ", reference=" + reference
                + ", courseCode=" + courseCode + ", supervisorFirstName=" + supervisorFirstName
                + ", supervisorLastName=" + supervisorLastName + "]";
    }

    public boolean isTotalScore() {
        return cplanXid == null && rawScore != null;
    }
}
