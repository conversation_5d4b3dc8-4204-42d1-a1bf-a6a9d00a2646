package ca.medit.learnerchart.dto;

public class GroupDTO {

    private long id;
    private String name;

    public long getId() {
        return id;
    }

    public GroupDTO setId(long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public GroupDTO setName(String name) {
        this.name = name;
        return this;
    }

    @Override
    public String toString() {
        return "GroupDTO [id=" + id + ", name=" + name + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (int) (id ^ (id >>> 32));
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        GroupDTO other = (GroupDTO) obj;
        return id == other.id;
    }

}
