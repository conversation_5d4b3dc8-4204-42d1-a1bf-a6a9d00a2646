package ca.medit.learnerchart.dto;

import java.util.*;

public class TimelineActivityMap {

    private final Map<Long, TimelineActivity> activityMap = new HashMap<Long, TimelineActivity>();
    private final Map<Long, Set<CanMedsRole>> canMedsRolesMap = new HashMap<Long, Set<CanMedsRole>>();

    public TimelineActivityMap() {
    }

    public void add(TimelineActivity e) {
        Set<CanMedsRole> canMedsRoles =  canMedsRolesMap.get(e.getAssessmentId());
        if (canMedsRoles == null) {
            canMedsRoles = new HashSet<CanMedsRole>();
            canMedsRolesMap.put(e.getAssessmentId(), canMedsRoles);
        }
        canMedsRoles.addAll(e.getCanMedsRoles());
        if ("TOTAL_SCORE_XID".equals(e.getCplanXId())
                || "FINAL_SCORE_XID".equals(e.getCplanXId())
                || "ABSENTEE_XID".equals(e.getCplanXId())) {
            if (!activityMap.containsKey(e.getAssessmentId())) {
                activityMap.put(e.getAssessmentId(), e);
            }
        }
    }

    public List<TimelineActivity> values() {
        if(activityMap.isEmpty()) {
            return Collections.emptyList();
        }
        SortedSet<TimelineActivity> activitySet = new TreeSet<TimelineActivity>();
        activitySet.addAll(activityMap.values());
        for (TimelineActivity activity : activitySet) {
            Set<CanMedsRole> canMedsRoles =  canMedsRolesMap.get(activity.getAssessmentId());
            if (canMedsRoles != null) {
                activity.setCanMedsRoles(canMedsRoles);
            }
        }
        return new ArrayList<TimelineActivity>(activitySet);
    }

}
