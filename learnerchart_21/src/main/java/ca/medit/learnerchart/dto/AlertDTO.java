package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.domain.AlertType;
import ca.medit.learnerchart.domain.AssessmentMode;
import org.joda.time.LocalDateTime;


public class AlertDTO {


    private long id;
    private long referenceId;
    private Integer yearId;
    private String type;
    private String title;
    private AssessmentMode assessmentMode;
    private AlertType alertType;
    private String externalLink;
    private LocalDateTime dateTime;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(long referenceId) {
        this.referenceId = referenceId;
    }

    public Integer getYearId() {
        return yearId;
    }

    public void setYearId(Integer yearId) {
        this.yearId = yearId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public AssessmentMode getAssessmentMode() {
        return assessmentMode;
    }

    public void setAssessmentMode(AssessmentMode assessmentMode) {
        this.assessmentMode = assessmentMode;
    }

    public AlertType getAlertType() {
        return alertType;
    }

    public void setAlertType(AlertType alertType) {
        this.alertType = alertType;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

    @Override
    public String toString() {
        return "AlertDTO [id=" + id + ", referenceId=" + referenceId + ", type=" + type + ", title=" + title
                + ", assessmentMode=" + assessmentMode + ", alertType=" + alertType + ", externalLink=" + externalLink
                + ", dateTime=" + dateTime + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (int) (id ^ (id >>> 32));
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        AlertDTO other = (AlertDTO) obj;
        return id == other.id;
    }


}
