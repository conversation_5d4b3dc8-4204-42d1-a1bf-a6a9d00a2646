package ca.medit.learnerchart.dto;

import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.Level;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Log4j2
public class LearnerDTOMap {

   // private XLogger logger = XLoggerFactory.getXLogger(this.getClass());
    private final Map<Long, LearnerDTO> map = new LinkedHashMap<>();

    public LearnerDTO addRow(Object[] row) {
        Long id;
        // = castBigIntegerToLong((BigInteger) row[0]);
        if (row[0] instanceof BigInteger) {
            id = castBigIntegerToLong((BigInteger) row[0]);
        } else if (row[0] instanceof Long) {
            id = (Long) row[0];
        } else {
            throw new IllegalArgumentException("Unsupported type: " + row[0].getClass().getName());
        }
        LearnerDTO dto = map.get(id);
        if (dto == null) {
            dto = new LearnerDTO();
            dto.setAuthUser(createAuthUser(row[0], row[1], row[2], row[3], row[4]));
            dto.setCohortName((String) row[5]);
            dto.setPictureName((String) row[6]);
            dto.addGroup(createGroup(row[7], row[8]));

            map.put(id, dto);
        }
        dto.addScholar(createScholar(row));
        dto.addLearnerSupporter(createLearnerSupporter(row));
        return dto;
    }

    public static Long castBigIntegerToLong(BigInteger bigInteger) {
        if (bigInteger == null) {
            throw new IllegalArgumentException("BigInteger values is null");
        }
        return bigInteger.longValue();
    }

    private UserDTO createScholar(Object[] row) {
        UserDTO scholar = createAuthUser(row[9], row[10], row[11], row[12], row[13]);
        return scholar;
    }

    private UserDTO createLearnerSupporter(Object[] row) {
        UserDTO scholar = createAuthUser(row[14], row[15], row[16], row[17], row[18]);
        return scholar;
    }

    private GroupDTO createGroup(Object id, Object name) {
        GroupDTO group = null;
        try {
            if (id instanceof BigInteger) {
                group = new GroupDTO().setId(castBigIntegerToLong((BigInteger) id)).setName((String) name);
            } else if (id instanceof Long) {
                group = new GroupDTO().setId((Long) id).setName((String) name);
            }

        } catch (IllegalArgumentException e) {
            log.catching(Level.DEBUG, e);
            // skip it
        } catch (ClassCastException e) {
            log.catching(Level.DEBUG, e);
            // skip it
        }
        return group;
    }

    private UserDTO createAuthUser(Object... obj) {
        UserDTO au = null;
        try {
            UserDTO dto = new UserDTO();
            if(obj[0] != null) {
                if (obj[0] instanceof BigInteger) {
                    dto.setId(castBigIntegerToLong((BigInteger)obj[0]));
                } else if (obj[0] instanceof Long) {
                    dto.setId((Long) obj[0]);
                }
                dto.setUtorId((String) obj[1]);
                dto.setFirstName((String) obj[2]);
                dto.setLastName((String) obj[3]);
                dto.setEmail((String) obj[4]);
                au = dto;
            }
        } catch (ClassCastException e) {
            // skip
        } catch (IllegalArgumentException e) {
            // skip
        }
        return au;
    }

    public List<LearnerDTO> getLearners() {
        return new ArrayList<>(map.values());
    }

}
