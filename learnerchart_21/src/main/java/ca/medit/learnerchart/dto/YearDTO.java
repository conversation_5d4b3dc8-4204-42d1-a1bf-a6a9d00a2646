package ca.medit.learnerchart.dto;

public class YearDTO {


    private int academicYear;
    private int programYear;

    public YearDTO() {
    }

    public YearDTO(int academicYear, int programYear) {
        this.academicYear = academicYear;
        this.programYear = programYear;
    }

    public YearDTO(Integer yearId) {
        int digits = yearId.toString().length();
        if (digits == 5) {
            this.academicYear = Integer.parseInt(yearId.toString().substring(0, 4));
            this.programYear = Integer.parseInt(yearId.toString().substring(4));
        } else {
            throw new IllegalArgumentException("yearId has a wrong format");
        }
    }

    public int getYearId() {
        return Integer.parseInt(academicYear + "" + programYear);
    }

    public int getAcademicYear() {
        return academicYear;
    }

    public void setAcademicYear(int academicYear) {
        this.academicYear = academicYear;
    }

    public int getProgramYear() {
        return programYear;
    }

    public void setProgramYear(int programYear) {
        this.programYear = programYear;
    }


}
