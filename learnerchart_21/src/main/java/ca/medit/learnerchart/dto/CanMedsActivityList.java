package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.service.LearnerChartFilter;

import java.util.ArrayList;
import java.util.List;

public class CanMedsActivityList {
    private final List<CanMedsExerciseItem> items = new ArrayList<>();
    private LearnerChartFilter filter;
    private int activityCount;
    private int questionCount;

    public CanMedsActivityList(LearnerChartFilter filter) {
        this.filter = filter;
    }

    public CanMedsActivityList(CanMedsExerciseItem exerciseItem) {
        add(exerciseItem);
    }

    public LearnerChartFilter getFilter() {
        return filter;
    }

    public void setFilter(LearnerChartFilter filter) {
        this.filter = filter;
    }

    public boolean add(CanMedsExerciseItem item) {
        boolean result = false;
        items.add(item);
        activityCount++;
        questionCount += item.getNumberOfItems();
        return result;
    }

    public List<CanMedsExerciseItem> getItems() {
        return items;
    }

    public int size() {
        return items.size();
    }

    public int getActivityCount() {
        return activityCount;
    }

    public int getQuestionCount() {
        return questionCount;
    }

    public boolean isEmpty() {
        return items.isEmpty();
    }

//    public List<JsonType> toJsonTypeListNumeric() {
//        List<JsonType> activities = new ArrayList<>();
//        for (CanMedsExerciseItem exerciseItem : items) {
//
//            LearnerActivity activity = new LearnerActivity(exerciseItem.getAssessmentId());
//            activity.setRemoteId(exerciseItem.getRemoteId());
//            activity.setAssessmentName(exerciseItem.getAssessmentName());
//            activity.setDate(exerciseItem.getAssessmentDueDate());
//            activity.setAcademicYear(exerciseItem.getAcademicYear());
//            activity.setProgramYear(exerciseItem.getProgramYear());
//            activity.setPointsAvailable(exerciseItem.getPointsAvailable());
//            activity.setPointsEarned(exerciseItem.getPointsEarned());
//            activity.setQuestionCount(exerciseItem.getNumberOfItems());
//            activity.setScoreEnum(ActivityScoreEnum.NUMERIC);
//            activity.setAssessmentType(exerciseItem.getAssessmentType());
//
//            List<JsonType> activityJson = (List<JsonType>) new TransformerLearnerActivityJson(filter).toActivityJson(Arrays.asList(activity), null);
//            activities.addAll(activityJson);
//        }
//        return activities;
//    }

//    public List<JsonType> toJsonTypeListPassFail() {
//        List<JsonType> activities = new ArrayList<>();
//        for (CanMedsExerciseItem exerciseItem : items) {
//            LearnerActivity activity = new LearnerActivity(exerciseItem.getAssessmentId());
//            activity.setRemoteId(exerciseItem.getRemoteId());
//            activity.setAssessmentName(exerciseItem.getAssessmentName());
//            activity.setDate(exerciseItem.getAssessmentDueDate());
//            activity.setAcademicYear(exerciseItem.getAcademicYear());
//            activity.setProgramYear(exerciseItem.getProgramYear());
//            activity.setPointsAvailable(exerciseItem.getPointsAvailable());
//            activity.setPointsEarned(exerciseItem.getPointsEarned());
//            activity.setQuestionCount(exerciseItem.getNumberOfItems());
//            activity.setRawScore(exerciseItem.getRawScore());
//            activity.setAssessmentType(exerciseItem.getAssessmentType());
//            activity.setScoreId(exerciseItem.getScoreId());
//            activity.setReference(exerciseItem.getReference());
//            activity.setCourseCode(exerciseItem.getCourseCode());
//            activity.setSupervisorFirstName(exerciseItem.getSupervisorFirstName());
//            activity.setSupervisorLastName(exerciseItem.getSupervisorLastName());
//            JsonType activityJson = (JsonType) new TransformerAssignmentActivityJson(filter).toAssignmentActivityJsonType(activity);
//            activities.add(activityJson);
//        }
//        return activities;
//    }

}
