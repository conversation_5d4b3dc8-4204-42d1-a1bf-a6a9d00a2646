package ca.medit.learnerchart.dto;

import java.io.Serializable;

public class CanMedsRole implements Serializable, Comparable<CanMedsRole>{

    private static final long serialVersionUID = 1L;

    private long id;
    private String label;

    public CanMedsRole(long id, String label) {
        this.id = id;
        this.label = label;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((label == null) ? 0 : label.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        CanMedsRole other = (CanMedsRole) obj;
        if (label == null) {
            return other.label == null;
        } else return label.equals(other.label);
    }

    @Override
    public int compareTo(CanMedsRole o) {
        if(o == null || o.label == null) {
            return 1;
        }
        return label.compareTo(o.label);
    }

    @Override
    public String toString() {
        return "CanMedsRole [id=" + id + ", label=" + label + "]";
    }

}
