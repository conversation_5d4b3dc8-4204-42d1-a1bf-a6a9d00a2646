package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.domain.*;
import org.joda.time.LocalDateTime;

import java.io.Serializable;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Set;
import java.util.TreeSet;



public class TimelineActivity extends LearnerActivity implements Serializable, Comparable<TimelineActivity>{



    private String cplanXId;
    private Set<CanMedsRole> canMedsRoles = new TreeSet<>();

    private TimelineActivity(Long id, String assessmentName, LocalDateTime date) {
        this.assessmentId = id;
        this.assessmentName = assessmentName;
        this.date = date;
    }

    public TimelineActivity(Long id, String assessmentName, LocalDateTime date, ActivityScoreEnum scoreEnum) {
        this(id, assessmentName, date);
        this.scoreEnum = scoreEnum;
    }

    public String getCplanXId() {
        return cplanXId;
    }

    public void setCplanXId(String cplanXId) {
        this.cplanXId = cplanXId;
    }

    public Set<CanMedsRole> getCanMedsRoles() {
        return canMedsRoles;
    }

    public void setCanMedsRoles(Set<CanMedsRole> roles) {
        if (roles == null) {
            roles = new TreeSet<>();
        }
        this.canMedsRoles = roles;
    }

    public void addCanMedsRole(CanMedsRole role) {
        this.canMedsRoles.add(role);
    }

    public void addAllCanMedsRoles(Set<CanMedsRole> roles) {
        if (roles == null) {
            return;
        }
        this.canMedsRoles.addAll(roles);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (int) (assessmentId ^ (assessmentId >>> 32));
        result = prime * result + ((assessmentName == null) ? 0 : assessmentName.hashCode());
        result = prime * result + ((date == null) ? 0 : date.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TimelineActivity other = (TimelineActivity) obj;
        if (assessmentId != other.assessmentId)
            return false;
        if (assessmentName == null) {
            if (other.assessmentName != null)
                return false;
        } else if (!assessmentName.equals(other.assessmentName))
            return false;
        if (date == null) {
            return other.date == null;
        } else return date.equals(other.date);
    }

    @Override
    public int compareTo(TimelineActivity o) {
        int c = 0;
        if (o.getDate() == null) {
            throw new IllegalArgumentException("Learner activity to compare has null date");
        }
        c = date.compareTo(o.date);
        if (c == 0) {
            c = assessmentName.compareTo(o.assessmentName);
        }
        if (c == 0) {
            c = compareAssessmentId(o);
        }
        return c;
    }

    private int compareAssessmentId(TimelineActivity o) {
        if (o == null) {
            throw new IllegalArgumentException("Attempt to compare null activity");
        }
        return assessmentId.compareTo(o.assessmentId);
    }

    public static TimelineActivity mapRowToTimelineActivity(Object[] r, AssessmentMode mode) {
        TimelineActivity la = coreMapRowToTimelineActivity(r);
        mapScoreEnumToTimelineActivity(r, la, mode);
        mapParentScoreEnumToTimelineActivity(r, la, mode);
        return la;
    }

    private static void mapScoreEnumToTimelineActivity(Object[] r, TimelineActivity la, AssessmentMode mode) {
        ActivityScoreEnum scoreEnum = null;
        if (mode == AssessmentMode.WRITTEN) {
            if ("ABSENTEE_XID".equals(r[9])) {
                scoreEnum = ActivityScoreEnum.INCOMPLETE;
            } else if (AssessmentType.PROGRESS_TEST.name().equals(la.getAssessmentType())) {
                scoreEnum = ActivityScoreEnum.LABEL_NUMERIC;
                scoreEnum.setLabel(la.getRawScore());
            } else if (AssessmentType.EPA_SUMMARIES.name().equals(la.getAssessmentType())) {
                scoreEnum = ActivityScoreEnum.NUMERIC;
                // scoreEnum.setLabel(la.getRawScore());
            } else {
                scoreEnum = ActivityScoreEnum.NUMERIC;
            }
        } else {
            scoreEnum = ActivityScoreEnum.getEnumFromRawValue(la.getRawScore());
        }
        la.setScoreEnum(scoreEnum);
    }

    private static void mapParentScoreEnumToTimelineActivity(Object[] r, TimelineActivity la, AssessmentMode mode) {
        if (mode == AssessmentMode.PERFORMANCE_BASED) { // the only source that uses "parent" is OSCE
            ActivityScoreEnum scoreEnum = ActivityScoreEnum.getEnumFromRawValue(RowMapperUtils.nullSafeGetString(r[13]));
            la.setParentScoreEnum(scoreEnum);
        }
    }

    private static TimelineActivity coreMapRowToTimelineActivity(Object[] r) {
        TimelineActivity la = new TimelineActivity(RowMapperUtils.nullSafeBigIntegerAsLong(r[4]),
                RowMapperUtils.nullSafeGetString(r[6]), convertToLocalDateTime(r[8]));
        la.setRemoteId(RowMapperUtils.nullSafeBigIntegerAsLong(r[5]));
        la.setAssessmentType(RowMapperUtils.nullSafeGetString(r[7]));
        la.setPointsAvailable(RowMapperUtils.nullSafeGetDouble(r[1]));
        la.setPointsEarned(RowMapperUtils.nullSafeGetDouble(r[2]));
        la.setQuestionCount(RowMapperUtils.nullSafeGetInt(r[3]));
        la.setCplanXId(RowMapperUtils.nullSafeGetString(r[9]));
        la.setParentName(RowMapperUtils.nullSafeGetString(r[12]));
        if (r[10] != null && r[11] != null) {
            la.addCanMedsRole(new CanMedsRole(RowMapperUtils.nullSafeBigIntegerAsLong(r[10]),
                    RowMapperUtils.nullSafeGetString(r[11])));
        }
        la.setScoreId(RowMapperUtils.nullSafeBigIntegerAsLong(r[14]));
        la.setRawScore(RowMapperUtils.nullSafeGetString(r[0]));
        la.setReference(RowMapperUtils.nullSafeGetString(r[15]));
        la.setCourseCode(RowMapperUtils.nullSafeGetString(r[16]));
        la.setSupervisorFirstName(RowMapperUtils.nullSafeGetString(r[17]));
        la.setSupervisorLastName(RowMapperUtils.nullSafeGetString(r[18]));
        la.setAcademicYear(RowMapperUtils.nullSafeGetInt(r[19]));
        la.setProgramYear(RowMapperUtils.nullSafeGetInt(r[20]));
        la.setSource(FeedSourceTypeEnum.nullSafeValueOf(RowMapperUtils.nullSafeGetString(r[21])));
        return la;
    }

    @Override
    public String toString() {
        return "LearnerActivity [scoreEnum=" + scoreEnum + ", assessmentId=" + assessmentId + ", assessmentName="
                + assessmentName + ", pointsAvailable=" + pointsAvailable + ", pointsEarned=" + pointsEarned
                + ", activityCount=" + activityCount + ", questionCount=" + questionCount + ", date=" + date
                + ", assessmentType=" + assessmentType + ", cplanXId=" + cplanXId + ", canMedsRoles=" + canMedsRoles
                + "]";
    }

    /**
     *
     * @param o
     * @return
     */
    public static LocalDateTime convertToLocalDateTime(Object o) {
        LocalDateTime localDateTime = null;
        if (o instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) o;
        } else if (o instanceof Timestamp) {
            localDateTime = new LocalDateTime(o);
        } else if (o instanceof String) {
            try {
                localDateTime = new LocalDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse((String)o));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return localDateTime;
    }


}
