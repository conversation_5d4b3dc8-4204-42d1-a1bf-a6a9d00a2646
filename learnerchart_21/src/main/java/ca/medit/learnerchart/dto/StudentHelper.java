package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.User;

import java.util.ArrayList;
import java.util.List;

public class StudentHelper {

    public static List<User> fromStudentReportData(
            StudentReport.ReportData reportData) {
        List<User> students = new ArrayList<User>();
        String cohortName = reportData.className;
        for (StudentReport.StudentData studentData : reportData.students) {
            User student = new User();
            student.setUtorid(studentData.utorId);
            student.setUsername(studentData.utorId);
            student.setFirstName(studentData.firstName);
            student.setLastName(studentData.lastName);
            student.setEmail(studentData.email);
          //  student.setProfilePhoto(studentData.picture);
            student.setCohortName(cohortName);
            student.setStudentNumber(studentNumberConverter(studentData.studentId));
            student.setYearOfStudy(Integer.parseInt(studentData.yearOfStudy));
            students.add(student);
        }
        return students;
    }

    private static Long studentNumberConverter(String studentId) {
        Long longStudentNumber = null;
        if (!studentId.trim().isEmpty() && !studentId.equals(null)) {
            try {
                longStudentNumber = Long.parseLong(studentId);
            } catch (NumberFormatException nfe) {
                throw new MedITException("Could not read student id: " + studentId
                        + " as a valid student number", nfe);
            }
        }
        return longStudentNumber;
    }
}
