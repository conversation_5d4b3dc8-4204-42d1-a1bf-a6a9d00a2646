package ca.medit.learnerchart.dto;

import java.lang.reflect.Field;

public class PseudoFeedback {


    public static final String FEEDBACK_1 = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis eu lectus quis augue euismod tincidunt interdum malesuada ex. Nullam vel convallis felis. Nunc nec mi eget erat vulputate dignissim ut non ipsum. Cras rutrum sed tellus at imperdiet. In hac habitasse platea dictumst. Aliquam nisl diam, vestibulum et mauris vitae, feugiat pellentesque libero. Mauris ut sagittis sem. Morbi eleifend orci eget lorem consequat suscipit. Mauris quam arcu, porta sed justo sit amet, iaculis eleifend eros. Aliquam ultrices nisi sit amet urna sagittis faucibus. Nunc ultrices id lacus ac mattis. Curabitur eleifend, lorem eget consectetur vulputate, justo turpis tincidunt erat, sit amet tincidunt neque quam vitae lectus. Aliquam feugiat eget nisl malesuada fringilla. Pellentesque vitae nibh ut ante accumsan imperdiet. Fusce malesuada diam ut ante rutrum vulputate. In fringilla libero eu orci volutpat, et hendrerit quam faucibus.";

    public static final String FEEDBACK_2 = "Interdum et malesuada fames ac ante ipsum primis in faucibus. Suspendisse semper posuere convallis. Nam at magna augue. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum vel sapien consectetur, gravida nulla ut, mattis enim. Aliquam iaculis feugiat turpis et viverra. Proin vel sem vel turpis placerat viverra. Nullam pellentesque orci in sapien ultricies aliquam. Nullam sed massa et justo dapibus tempus. Nam hendrerit pulvinar fermentum. Nunc sed orci feugiat, semper felis quis, efficitur sapien. Proin sollicitudin a tellus quis malesuada. Cras nibh tellus, rhoncus vitae enim at, viverra rhoncus sapien.";

    public static final String FEEDBACK_3 = "Sed nisi nunc, ornare sagittis suscipit in, suscipit ut lacus. Donec semper velit sit amet vulputate tristique. Nullam eu ex urna. Suspendisse potenti. Donec blandit nisi nec semper aliquet. Mauris eu diam nec magna volutpat blandit at id ligula. Suspendisse at lorem diam. Fusce consectetur neque quis dui eleifend dignissim. Donec vestibulum odio vel augue pellentesque molestie. Quisque eget condimentum felis.";

    public static final String FEEDBACK_4 = "Aenean nec interdum velit, vel suscipit enim. Vivamus ultrices, dolor eu blandit vestibulum, justo quam vulputate justo, semper elementum tellus urna eget ante. Morbi mollis lorem et eros volutpat aliquam. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque scelerisque nibh et purus luctus consectetur. In aliquet ut turpis nec aliquet. Fusce eget pulvinar tortor, sit amet mollis sem. Duis nec turpis et odio sagittis auctor. Duis consequat sapien at nibh blandit, sed tempus enim accumsan. Quisque malesuada, massa eget aliquam aliquam, felis turpis pharetra sem, non malesuada sapien enim at tortor. Aenean ultrices justo est. In hac habitasse platea dictumst.";

    public static final String FEEDBACK_5 = "Ut in ullamcorper libero. Ut imperdiet orci neque, et viverra risus faucibus id. Vestibulum dapibus dignissim commodo. Aliquam elementum ultrices nisi in congue. Integer dui augue, egestas sed congue ut, mollis id sem. Phasellus vestibulum massa justo, id euismod erat auctor in. Nullam condimentum dapibus nunc id ullamcorper. Curabitur venenatis tellus at libero euismod pretium.";

    public static String getPseudoFeedback(long id) {
        String feedback = FEEDBACK_1;
        try {
            Field field = PseudoFeedback.class.getField("FEEDBACK_" + (id % 5 + 1));
            feedback = (String) field.get(null);
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {
        }
        return feedback;
    }


}
