package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.common.exception.MedITException;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;

public class AES {


    static String IV = "0hH8bXgtuw88DhhW";
    public static String encryptionKey = "jRfo7z7kAFNsdaQ7";

    public static String encryptString(String plainText, String encryptionKey) {
        byte[] cipher = encrypt(plainText, encryptionKey);
        return new String(Base64.encodeBase64(cipher));
    }

    public static String decryptString(String cipherText, String encryptionKey) {
        byte[] cipher = Base64.decodeBase64(cipherText);
        return decrypt(cipher, encryptionKey);
    }

    public static byte[] encrypt(String plainText, String encryptionKey) {
        Cipher cipher;
        byte[] bytes = null;
        try {
            cipher = Cipher.getInstance("AES/CBC/PKCS5Padding", "SunJCE");
            SecretKeySpec key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(IV.getBytes(StandardCharsets.UTF_8)));
            bytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (NoSuchProviderException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (NoSuchPaddingException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (InvalidAlgorithmParameterException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (IllegalBlockSizeException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (BadPaddingException e) {
            throw new MedITException(e.getMessage(), e);
        }
        return bytes;
    }

    public static String decrypt(byte[] cipherText, String encryptionKey) {
        String output = null;
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding", "SunJCE");
            SecretKeySpec key = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(IV.getBytes(StandardCharsets.UTF_8)));
            output = new String(cipher.doFinal(cipherText), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (NoSuchProviderException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (NoSuchPaddingException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (InvalidAlgorithmParameterException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (IllegalBlockSizeException e) {
            throw new MedITException(e.getMessage(), e);
        } catch (BadPaddingException e) {
            throw new MedITException(e.getMessage(), e);
        }
        return output;
    }


}
