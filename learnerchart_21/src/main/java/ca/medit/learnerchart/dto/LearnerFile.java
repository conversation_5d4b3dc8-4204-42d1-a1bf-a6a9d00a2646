package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.entity.File;
import ca.medit.learnerchart.entity.User;

import java.io.InputStream;
import java.io.Serializable;

public class LearnerFile implements Serializable {


    private String fileName;
    private long fileSize;
    private InputStream inputStream;
    private User learner;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public InputStream getInputStream() {
        return inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    public User getLearner() {
        return learner;
    }

    public void setLearner(User learner) {
        this.learner = learner;
    }

    public File getFile() {
        File file = new File();
        file.setFileName(fileName);
        file.setFileSize(fileSize);
//		file.setCreatedOn(new LocalDateTime());
        return file;
    }


}
