package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.ActivityScoreEnum;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.entity.CplanXid;
import ca.medit.learnerchart.dto.EvaluationReport.Evaluation;

import java.util.*;

public class ScoreHelper {


    public static int MEDSIS_PASS_SCORE = 2;

    public static Set<Score> fromfromEvaluation(Evaluation evaluation, Assessment assessment) {
        if (evaluation == null) {
            throw new IllegalArgumentException("Evaluation is required");
        }

        Set<Score> scores = new HashSet<Score>();
        Map<String, List<Integer>> map = getXIDScoreMap(evaluation.scores);

        for (Iterator<Map.Entry<String, List<Integer>>> i = map.entrySet().iterator(); i.hasNext(); ) {
            Map.Entry<String, List<Integer>> entry = i.next();
            Score score = new Score();
            score.setAssessment(assessment);
            score.setNumberOfItems((float) entry.getValue().size());
            score.setSource(FeedSourceTypeEnum.MEDSIS);
            score.setRawValue(calculateScore(entry.getValue()));
          //  score.setCplanXidString(entry.getKey());
            score.setCplanXid(entry.getKey());
            score.setUtorid(evaluation.utorId);
            score.setOptlock(0L);
            scores.add(score);
        }

        Score finalScore = new Score();
        finalScore.setAssessment(assessment);
        finalScore.setNumberOfItems((float) evaluation.scores.size());
        finalScore.setSource(FeedSourceTypeEnum.MEDSIS);
        finalScore.setRawValue(calculateFinalScore(scores));
    //    finalScore.setCplanXidString(CplanXid.FINAL_SCORE_XID);
        finalScore.setCplanXid(CplanXid.FINAL_SCORE_XID);
        finalScore.setUtorid(evaluation.utorId);
        finalScore.setOptlock(0L);
        scores.add(finalScore);

        return scores;
    }

    public static Map<String, List<Integer>> getXIDScoreMap(List<Evaluation.Score> evaluationScores) {
        if (evaluationScores == null || evaluationScores.size() == 0) {
            throw new MedITException("Evaluation scores are required");
        }

        Map<String, List<Integer>> map = new HashMap<String, List<Integer>>();

        for (Evaluation.Score evaluationScore : evaluationScores) {
            for (Evaluation.XID xid : evaluationScore.xids) {
                List<Integer> scores = map.get(xid.xid);
                if (scores == null) {
                    scores = new ArrayList<Integer>();
                    map.put(xid.xid, scores);
                }
                if(evaluationScore.questionScore.matches("\\d+")) {
                    try {
                        scores.add(Integer.parseInt(evaluationScore.questionScore));
                    } catch (NumberFormatException e) {
                        throw new MedITException("Question Score is not a number. Question ID = " + evaluationScore.questionId +
                                " Question Score = " + evaluationScore.questionScore, e);
                    }
                }
            }
        }
        return map;
    }

    public static String calculateScore(List<Integer> scores) {
        String grade = ActivityScoreEnum.MEET_REQUIREMENTS.name();
        for (int score : scores) {
            if (score <= MEDSIS_PASS_SCORE) {
                grade = ActivityScoreEnum.NOT_MEET_REQUIREMENTS.name();
                break;
            }
        }
        return grade;
    }

    public static String calculateFinalScore(Set<Score> scores) {
        String grade = ActivityScoreEnum.MEET_REQUIREMENTS.name();
        for (Score score : scores) {
            if (ActivityScoreEnum.NOT_MEET_REQUIREMENTS.name().equals(score.getRawValue())) {
                grade = ActivityScoreEnum.NOT_MEET_REQUIREMENTS.name();
                break;
            }
        }
        return grade;
    }
}
