package ca.medit.learnerchart.dto;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

public class LearnerDTO {

    private UserDTO authUser;
    private String cohortName;
    private String pictureName;
    private final Set<UserDTO> scholars = new LinkedHashSet<>();
    private final Set<UserDTO> learnerSupporters = new LinkedHashSet<>();
    private final Set<GroupDTO> groups = new LinkedHashSet<>();
    private List<AlertDTO> alerts = new ArrayList<AlertDTO>();

    public Long getId() {
        return authUser.getId();
    }

    public String getFirstName() {
        return authUser.getFirstName();
    }

    public String getLastName() {
        return authUser.getLastName();
    }

    public String getUtorId() {
        return authUser.getUtorId();
    }

    public String getEmail() {
        return authUser.getEmail();
    }

    public UserDTO getUser() {
        return authUser;
    }

    public void setAuthUser(UserDTO authUser) {
        this.authUser = authUser;
    }

    public String getCohortName() {
        return cohortName;
    }

    public void setCohortName(String cohortName) {
        this.cohortName = cohortName;
    }

    public String getPictureName() {
        return pictureName;
    }

    public void setPictureName(String pictureName) {
        this.pictureName = pictureName;
    }

    public List<UserDTO> getScholars() {
        return new ArrayList<UserDTO>(scholars);
    }

    public void addScholar(UserDTO scholar) {
        if (scholar != null) {
            scholars.add(scholar);
        }
    }

    public List<UserDTO> getLearnerSupporters() {
        return new ArrayList<UserDTO>(learnerSupporters);
    }

    public LearnerDTO addLearnerSupporter(UserDTO learnerSupporter) {
        if (learnerSupporter != null) {
            learnerSupporters.add(learnerSupporter);
        }
        return this;
    }

    public List<GroupDTO> getGroups() {
        return new ArrayList<GroupDTO>(groups);
    }

    public LearnerDTO addGroup(GroupDTO group) {
        if (group != null) {
            groups.add(group);
        }
        return this;
    }

    public void addLearnerDTO(LearnerDTO dto) {
        scholars.addAll(dto.scholars);
        learnerSupporters.addAll(dto.learnerSupporters);
    }

    public List<AlertDTO> getAlerts() {
        return alerts;
    }

    public void setAlerts(List<AlertDTO> alerts) {
        this.alerts = alerts;
    }


}
