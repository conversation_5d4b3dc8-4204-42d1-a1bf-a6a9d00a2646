package ca.medit.learnerchart.dto;

import java.io.Serializable;
import java.util.List;

public class EvaluationReport implements Serializable {


    public List<ReportData> reportData;

    public static class ReportData implements Serializable {

        public String formNO;
        public String formName;
        public List<Evaluation> evaluations;
        public List<EvaluationBase> canceledEvaluations;

    }

    public static class Evaluation extends EvaluationBase {

        public String submitDate;
        public String triggerDate;
        public String supervisorFirstName;
        public String supervisorLastName;
        public String supervisorCPSO;
        public String trainingSession;
        public String courseCode;
        public String academyCode;
        public String hospitalCode;
        public String utorId;
        public String yearOfStudy;
        public String studentId;
        public List<Score> scores;

        public static class Score  implements Serializable {

            public String questionId;
            public List<XID> xids;
            public String questionScore;

        }

        public static class XID  implements Serializable {
            public String xid;

            public XID() {
            }

            public XID(String xid) {
                this.xid = xid;
            }
        }

        @Override
        public String toString() {
            return "Evaluation [evalId=" + evalId + ", submitDate=" + submitDate + ", triggerDate=" + triggerDate
                    + ", supervisorFirstName=" + supervisorFirstName + ", supervisorLastName=" + supervisorLastName
                    + ", supervisorCPSO=" + supervisorCPSO + ", trainingSession=" + trainingSession + ", courseCode="
                    + courseCode + ", academyCode=" + academyCode + ", hospitalCode=" + hospitalCode + ", utorId="
                    + utorId + ", yearOfStudy=" + yearOfStudy + ", studentId=" + studentId + "]";
        }

    }

    public static class EvaluationBase implements Serializable {

        public String evalId;

        @Override
        public String toString() {
            return "EvaluationBase [evalId=" + evalId + "]";
        }

    }
}
