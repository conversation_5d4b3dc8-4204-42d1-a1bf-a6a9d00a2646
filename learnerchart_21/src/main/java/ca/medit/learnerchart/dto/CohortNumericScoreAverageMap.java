package ca.medit.learnerchart.dto;

import java.util.HashMap;
import java.util.Map;

public class CohortNumericScoreAverageMap {

    private final Map<Long, AverageNumericScore> map = new HashMap<>();

    public AverageNumericScore get(Long assessmentId) {
        return map.get(assessmentId);
    }

    public void add(AverageNumericScore score) {
        if(map.containsKey(score.getId())) {
            map.get(score.getId()).add(score);
        } else {
            map.put(score.getId(), score);
        }
    }

}
