package ca.medit.learnerchart.dto;

import ca.medit.learnerchart.common.exception.MedSisEvaluationImportException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.dto.EvaluationReport.Evaluation;
import ca.medit.learnerchart.dto.EvaluationReport.EvaluationBase;
//import org.joda.time.LocalDateTime;
import java.time.LocalDateTime;

import java.time.format.DateTimeFormatter;

public class AssessmentHelper {

    public static String MEDSIS_EVALUATION_DATE_FORMAT = "dd-MMM-yyyy";

    public static Assessment fromEvaluation(String formName, Evaluation evaluation) throws MedSisEvaluationImportException {

        if (formName == null) {
            throw new IllegalArgumentException("Form name is required");
        }
        if (evaluation == null) {
            throw new IllegalArgumentException("Evaluation object is required");
        }

        Assessment assessment = new Assessment();
        assessment.setSource(FeedSourceTypeEnum.MEDSIS);
        assessment.setName(formName);
        assessment.setAssessmentType(formName);
        assessment.setAssessmentMode(AssessmentMode.EVALUATION);
        assessment.setCourseCode(evaluation.courseCode);
        assessment.setSupervisorFirstName(evaluation.supervisorFirstName);
        assessment.setSupervisorLastName(evaluation.supervisorLastName);
        assessment.setOptlock(0L);
        if (evaluation.triggerDate == null || evaluation.triggerDate.isEmpty()) {
            throw new MedSisEvaluationImportException("Evaluation date is required", evaluation);
        }
        try {
          //  assessment.setDueDate(new LocalDateTime(new SimpleDateFormat(MEDSIS_EVALUATION_DATE_FORMAT).parse(evaluation.triggerDate)));

            assessment.setDueDate(
                    LocalDateTime.parse(
                            evaluation.triggerDate + " 00:00",
                            DateTimeFormatter.ofPattern(MEDSIS_EVALUATION_DATE_FORMAT + " HH:mm")
                    )
            );



        } catch (Exception e) {
            throw new MedSisEvaluationImportException("Trigger date has the wrong format from evaluation id = " + evaluation.evalId, evaluation, e);
        }

        if (evaluation.evalId == null || evaluation.evalId.isEmpty()) {
            throw new MedSisEvaluationImportException("Evaluation ID is required", evaluation);
        }
        try {
            assessment.setRemoteId(Long.parseLong(evaluation.evalId));
        } catch (NumberFormatException e) {
            throw new MedSisEvaluationImportException("Evaluation ID is not a number for evaluation id = " + evaluation.evalId, evaluation, e);
        }

        if (evaluation.yearOfStudy == null || evaluation.yearOfStudy.isEmpty()) {
            throw new MedSisEvaluationImportException("Year of study is required", evaluation);
        }
        try {
            assessment.setProgramYear(Integer.parseInt(evaluation.yearOfStudy));
        } catch (NumberFormatException e) {
            throw new MedSisEvaluationImportException("Year of study is not a number for evaluation id = " + evaluation.evalId, evaluation, e);
        }

        assessment.setAcademicYear(convertTrainingSessionToAcademicYear(evaluation));

        assessment.setScores(ScoreHelper.fromfromEvaluation(evaluation, assessment));
        return assessment;
    }

    private static int convertTrainingSessionToAcademicYear(Evaluation evaluation) throws MedSisEvaluationImportException {
        if (evaluation.trainingSession == null || evaluation.trainingSession.isEmpty()) {
            throw new MedSisEvaluationImportException("Training session is required", evaluation);
        }
        try {
            String[] pieces = evaluation.trainingSession.split("-");
            if(pieces.length != 2) {
                throw new MedSisEvaluationImportException("Evaluation id:" + evaluation.evalId + ". Invalid format for training session value: " + evaluation.trainingSession, evaluation);
            }
            return Integer.parseInt(pieces[0]);
        } catch (NumberFormatException e) {
            throw new MedSisEvaluationImportException("Training session cannot convert to a number for evaluation id = " + evaluation.evalId, evaluation, e);
        }
    }

    public static Assessment fromEvaluationBase(String formName, EvaluationBase evaluation) throws MedSisEvaluationImportException {

        if (formName == null) {
            throw new IllegalArgumentException("Form name is required for EvaluationBase");
        }
        if (evaluation == null) {
            throw new IllegalArgumentException("EvaluationBase object is required");
        }

        Assessment assessment = new Assessment();
        assessment.setSource(FeedSourceTypeEnum.MEDSIS);
        assessment.setName(formName);
        assessment.setAssessmentType(formName);
        assessment.setAssessmentMode(AssessmentMode.EVALUATION);
        assessment.setOptlock(0L);

        if (evaluation.evalId == null || evaluation.evalId.isEmpty()) {
            throw new MedSisEvaluationImportException("EvaluationBase ID is required", evaluation);
        }
        try {
            assessment.setRemoteId(Long.parseLong(evaluation.evalId));
        } catch (NumberFormatException e) {
            throw new MedSisEvaluationImportException("EvaluationBase ID is not a number for evaluation id = " + evaluation.evalId, evaluation, e);
        }
        return assessment;
    }
}
