package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.service.UserServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/profiles")
public class ProfileController {

    @Autowired
    private UserServiceImpl userService;

    @PostMapping
    public ResponseEntity<User> createProfile(@RequestBody User newUser) {
        try {
            User createdUser = userService.createUser(newUser);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (Exception ex) {
            throw new MedITException("Could not create the profile successfully.", ex);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<User> updateProfile(@PathVariable String id, @RequestBody User updatedUser) {
        try {
            Long userId = Long.valueOf(id);

            User existingUser = userService.getUser(userId);
            if (existingUser == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }

            // Update the existing user with the new values
            existingUser.setUsername(updatedUser.getUsername());
            existingUser.setFailedLoginAttempts(updatedUser.getFailedLoginAttempts());
            existingUser.setOptlock(updatedUser.getOptlock());
            existingUser.setEmail(updatedUser.getEmail());
            existingUser.setFirstName(updatedUser.getFirstName());
            existingUser.setLastName(updatedUser.getLastName());
            existingUser.setMiddleName(updatedUser.getMiddleName());
            existingUser.setYearOfStudy(updatedUser.getYearOfStudy());
            existingUser.setCohortName(updatedUser.getCohortName());
            existingUser.setEnabled(updatedUser.getEnabled());
            existingUser.setSalt(updatedUser.getSalt());
            existingUser.setPassword(updatedUser.getPassword());
            existingUser.setUpdatedon(LocalDateTime.now());
            existingUser.setProfilePhoto(updatedUser.getProfilePhoto());

            User savedUser = userService.updateUser(existingUser);
            return ResponseEntity.ok(savedUser);
        } catch (Exception ex) {
            throw new MedITException("Could not update the profile successfully.", ex);
        }
    }

    @PostMapping(value = "/{id}/picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadProfilePicture(@PathVariable String id, @RequestParam("file") MultipartFile file) {
        try {
            Long userId = Long.valueOf(id);

            User existingUser = userService.getUser(userId);
            if (existingUser == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }

            // Assuming that the User entity has a field 'picture' to store the image bytes
            existingUser.setProfilePhoto(file.getBytes());
            userService.updateUser(existingUser);

            return ResponseEntity.ok("Profile picture uploaded successfully");
        } catch (IOException e) {
            throw new MedITException("Could not upload the profile picture.", e);
        }
    }

    @GetMapping(value = "/{id}/picture", produces = MediaType.IMAGE_JPEG_VALUE)
    public ResponseEntity<byte[]> getProfilePicture(@PathVariable String id) {
        try {
            Long userId = Long.valueOf(id);

            User existingUser = userService.getUser(userId);
            if (existingUser == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }

            byte[] picture = existingUser.getProfilePhoto();
            if (picture == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Profile picture not found");
            }

            return ResponseEntity.ok(picture);
        } catch (Exception ex) {
            throw new MedITException("Could not retrieve the profile picture.", ex);
        }
    }
}
