package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.UserImage;
import ca.medit.learnerchart.service.LearnerJsonService;
import ca.medit.learnerchart.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/learners")
@Log4j2
public class LearnerController {

    @Autowired
    private LearnerJsonService learnerJsonSvc;

    @Autowired
    private UserService userSvc;


    @RequestMapping(value = "", method = RequestMethod.GET)
    public Object getLearners(HttpServletResponse resp) {
        Object json = null;

        try {
            json = learnerJsonSvc.getLearners();
        } catch (Exception e) {
            log.catching(e);
            //json = errorSvc.getErrorResponse(e);
            resp.setStatus(400);
        }

        return json;
    }


    @RequestMapping(value = "/{learnerId}/alerts", method = RequestMethod.GET)
    @Operation(summary = "Get a list of alerts by learnerId", description = "Fetch the list of alerts by Learner's UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of alerts could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No alerts could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of alerts for the given student")
    })
    public Object getLearnerAlerts(@PathVariable(value = "learnerId") String learnerId, HttpServletResponse resp) {
        Object json = null;

        try {
            json = learnerJsonSvc.getLearnerAlerts(learnerId);
        } catch (Exception e) {
            log.catching(e);
            //json = errorSvc.getErrorResponse(e);
            resp.setStatus(400);
        }

        return json;
    }

    @RequestMapping(value = "/{learnerId}/image", method = RequestMethod.GET)
    @Operation(summary = "Get the image file by learnerId", description = "Fetch the Image File by Learner's UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Image File could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No Image File could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the Image File for the given student")
    })
    public Object getLearnerPicture(@PathVariable(value = "learnerId") String learnerId, HttpServletResponse response) {
        Object json = null;

        try {
            UserImage userImage = userSvc.getLearnerPicture(learnerId);

            if (userImage != null) {
                response.setContentType("image/jpeg");
                response.setHeader("Content-Disposition", "attachment; filename=\"" + userImage.getImageName() + "\"");

                // Create an InputStream from the byte array
                InputStream inputStream = new ByteArrayInputStream(userImage.getImageData());
                // Copy from this input stream to output stream
                IOUtils.copy(inputStream, response.getOutputStream());
                response.flushBuffer();
            }
        } catch (Exception e) {
            log.catching(e);
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return json;
    }
}
