package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.service.AssessmentModeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/assessmentModes")
public class AssessmentModeController {

    private static final Logger logger = LogManager.getLogger(AssessmentModeController.class);

    @Autowired
    private AssessmentModeService activityTypeSvc;

    @RequestMapping(value = "", method = RequestMethod.GET)
    @Operation(summary = "Get a list of assessment-modes", description = "Fetch the list of assessment-modes available in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of assessment-modes could be returned successfully"),
            @ApiResponse(responseCode = "404", description = "No assessment-modes could be found"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of assessment-modes")
    })
    public Object getAssessmentModes(HttpServletResponse resp) {
        Object json = null;
        try {
            json = activityTypeSvc.getAssessmentModes();
        } catch (MedITException e) {
            logger.catching(e);
            resp.setStatus(500);
           // json = errorSvc.getErrorResponse(e);
        }
        return json;
    }

    @RequestMapping(value = "{key}", method = RequestMethod.GET)
    public Object getActivityType(@PathVariable("key") String key, HttpServletResponse resp) {
        Object json = null;
        try {
            json = activityTypeSvc.getAssessmentMode(key);
        } catch (MedITException e) {
            logger.catching(e);
            resp.setStatus(400);
           // json = errorSvc.getErrorResponse(e);
        }
        return json;
    }

}
