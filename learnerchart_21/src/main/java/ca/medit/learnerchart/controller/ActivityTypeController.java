package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.ActivityTypeJsonType;
import ca.medit.learnerchart.domain.AssessmentType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/activityTypes")
public class ActivityTypeController {

    private static final Logger logger = LogManager.getLogger(ActivityTypeController.class);

    @GetMapping("")
    @Operation(summary = "Get a list of activity types", description = "Fetch the list of activity types available in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of activity types could be returned successfully"),
            @ApiResponse(responseCode = "404", description = "No activity types could be found"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of activity types")
    })
    public Object getActivityTypes(HttpServletResponse resp) {

        Object json = null;
        List<ActivityTypeJsonType> types = new ArrayList<>();

        types.add(new ActivityTypeJsonType(AssessmentType.WEEKLY_EXERCISE.name(), AssessmentType.WEEKLY_EXERCISE.getLabel(), AssessmentType.WEEKLY_EXERCISE.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.MASTERY_EXERCISE.name(), AssessmentType.MASTERY_EXERCISE.getLabel(), AssessmentType.MASTERY_EXERCISE.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.ASSIGNMENT.name(), AssessmentType.ASSIGNMENT.getLabel(), AssessmentType.ASSIGNMENT.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.EVALUATION.name(), AssessmentType.EVALUATION.getLabel(), AssessmentType.EVALUATION.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.BELL_RINGER.name(), AssessmentType.BELL_RINGER.getLabel(), AssessmentType.BELL_RINGER.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.OSCE.name(), AssessmentType.OSCE.getLabel(), AssessmentType.OSCE.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.PROGRESS_TEST.name(), AssessmentType.PROGRESS_TEST.getLabel(), AssessmentType.PROGRESS_TEST.getOrder()));
        types.add(new ActivityTypeJsonType(AssessmentType.EPA_SUMMARIES.name(), AssessmentType.EPA_SUMMARIES.getLabel(), AssessmentType.EPA_SUMMARIES.getOrder()));
        json = types;

        return json;
    }

}
