package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.dto.AES;
import ca.medit.learnerchart.dto.AuthRequest;
import ca.medit.learnerchart.security.JwtUtil;
import ca.medit.learnerchart.service.AuthorizationExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/appl/api")
@Log4j2
public class JwtApiController {

    @Autowired
    private JwtUtil jwtUtil; // A helper class to validate and parse JWT

    @Autowired
    private AuthorizationExportService authExportSvc;

    @Value("${jwt.api.client.id}")
    private String clientId;

    @Value("${jwt.api.client.secret}")
    private String clientSecret;
    @PostMapping("/token")
    public Map<String, String> getToken(@RequestBody AuthRequest authRequest) {
        if (clientId.equals(authRequest.getClientId()) && clientSecret.equals(authRequest.getClientSecret())) {
            String token = jwtUtil.generateToken(authRequest.getClientId());
            return Map.of("token", token);
        } else {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid credentials");
        }
    }

    @GetMapping("/secure")
    public ResponseEntity<String> securedApi() {
        return ResponseEntity.ok("This is a secured JWT API.");
    }


    @GetMapping("/encrypted/authz")
    public Object getEncryptedAuthorizationJson(HttpServletResponse response) throws IOException {
        log.entry();
        Object body = null;
        try {
            JsonResponse json = authExportSvc.getAuthorizationJson();
            ObjectMapper mapper = new ObjectMapper();
            String responseJsonString = mapper.writeValueAsString(json);
            log.info("responseJsonString for /encrypted/authz:"+responseJsonString);
            body = AES.encryptString(responseJsonString, AES.encryptionKey);
        } catch (MedITException e) {
            log.catching(e);
            response.sendError(500, "Error getting authorizations");
        }
        log.exit();
        return body;
    }
}
