package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.DataReport;
import ca.medit.learnerchart.service.DataReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/appl/reports")
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
public class DataReportController {

    @Autowired
    private DataReportService dataReportService;

    @GetMapping("/data")
    public ResponseEntity<List<DataReport>> getDataReports(
            @RequestParam(required = true) Integer year,
            @RequestParam(required = true) String dataType) {

        List<DataReport> reports = dataReportService.getReportsByYearAndType(year, dataType);
        return ResponseEntity.ok(reports);
    }

    @GetMapping("/dashboarddata")
    public ResponseEntity<List<DataReport>> getDataReports(
            @RequestParam(required = true) Integer year,
            @RequestParam(required = true) Integer programYear,
            @RequestParam(required = true) String dataType) {

        List<DataReport> reports = dataReportService.getReportsByProgramYearAndType(year, programYear, dataType);
        return ResponseEntity.ok(reports);
    }

}
