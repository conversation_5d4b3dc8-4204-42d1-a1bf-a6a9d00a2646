package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.service.ProgramYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/appl/years")
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
public class YearController {

    private static final Logger logger = LogManager.getLogger(ActivityTypeController.class);

    @Autowired
    private ProgramYearService activityTypeSvc;



    @RequestMapping(value = "", method = RequestMethod.GET)
    @Operation(summary = "Get a list of years", description = "Fetch the list of years available in the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of years could be returned successfully"),
            @ApiResponse(responseCode = "404", description = "No years could be found"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of years")
    })
    public Object getProgramYears(HttpServletResponse resp) {
        Object json = null;
        try {
            json = activityTypeSvc.getProgramYears();
        } catch (MedITException e) {
            logger.catching(e);
          //  json = errorSvc.getErrorResponse(e);
            resp.setStatus(400);
        }
        return json;
    }

    @RequestMapping(value = "/acadYear", method = RequestMethod.GET)
    @Operation(summary = "Get current academic year", description = "Fetch the current academic year")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "The current academic year could be returned successfully"),
            @ApiResponse(responseCode = "404", description = "current academic year could not be found"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the current academic year")
    })
    public Object getCurrentAcadYear(HttpServletResponse resp) {

        try {
            LocalDate currentDate = LocalDate.now();
            int year = currentDate.getMonthValue() >= 7 ? currentDate.getYear() : currentDate.getYear() - 1;
            String acadYear = year + "-" + String.format("%02d", (year + 1) % 100);

            return ResponseEntity.ok(acadYear);
        } catch (Exception e) {
            logger.catching(e);
            resp.setStatus(400);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @RequestMapping(value = "/{learnerId}", method = RequestMethod.GET)
    @Operation(summary = "Get a list of years by UTOR ID", description = "Fetch the list of years available for the given student by UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of years could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No years could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of years for the given student")
    })
    public Object getLearnerYearList(@Parameter(description = "UTOR ID of the student to retrieve the list of years", required = true)
                                         @PathVariable(value = "learnerId") String learnerId, HttpServletResponse response) {

        Object json = null;
        logger.entry();
        try {
            json = activityTypeSvc.getLearnerYearList(learnerId);
        } catch (MedITException dse) {
            logger.catching(Level.ERROR, dse);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        logger.exit();
        return json;
    }

}
