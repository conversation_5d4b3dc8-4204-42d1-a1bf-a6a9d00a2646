package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.service.UserServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.time.LocalDateTime;
import java.util.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@Log4j2
public class UserController {


    @Autowired
    private UserServiceImpl userService;

    @GetMapping("/users")
    public List<User> getAllUsers() {
        try {
            return userService.getAllUsers();
        }catch(Exception ex){
            throw new MedITException("Could not retrieve the list of users successfully.");
        }
    }

    @RequestMapping(value = "/appl/users", method = RequestMethod.GET)
    @ResponseBody
    public Object getCurrentUser(HttpServletResponse resp) {
        log.entry();
        Object json = null;

        try {
            json = userService.getCurrentUser();
            log.debug("JSON: " + json);
        } catch (MedITException e) {
            log.catching(e);
          //  json = errorSvc.getErrorResponse(e);
            resp.setStatus(400);
        }

        log.exit();
        return json;
    }


    @PostMapping("/user")
    public User createUser(@RequestBody User newUser) {
        try {
            User createdUser = userService.createUser(newUser);
            return createdUser;
        } catch (Exception ex) {
            throw new MedITException("Could not create the user successfully.", ex);
        }
    }

    @PutMapping("/user/{id}")
    public User updateUser(@PathVariable String id, @RequestBody User updatedUser) {
        try {
            Long userId = Long.valueOf(id);

            User existingUser = userService.getUser(userId);
            if (existingUser == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }

            // Update the existing user with the new values
            existingUser.setUsername(updatedUser.getUsername());
            existingUser.setFailedLoginAttempts(updatedUser.getFailedLoginAttempts());
            existingUser.setOptlock(updatedUser.getOptlock());
            existingUser.setEmail(updatedUser.getEmail());
            existingUser.setFirstName(updatedUser.getFirstName());
            existingUser.setLastName(updatedUser.getLastName());
            existingUser.setMiddleName(updatedUser.getMiddleName());
            existingUser.setProfilePhoto(updatedUser.getProfilePhoto());
            existingUser.setYearOfStudy(updatedUser.getYearOfStudy());
            existingUser.setCohortName(updatedUser.getCohortName());
            existingUser.setEnabled(updatedUser.getEnabled());
            existingUser.setSalt(updatedUser.getSalt());
            existingUser.setPassword(updatedUser.getPassword());
            existingUser.setUpdatedon(LocalDateTime.now());

            User savedUser = userService.updateUser(existingUser);
            return savedUser;
        } catch (Exception ex) {
            throw new MedITException("Could not update the user successfully.", ex);
        }
    }

    @PostMapping("/disableuser/{id}")
    public User disableUser(@PathVariable String id) {
        try {
            Long userId = Long.valueOf(id);

            User existingUser = userService.getUser(userId);
            if (existingUser == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }

            // Enable the deleted flag on the existing user
            existingUser.setDeleted(true);
            existingUser.setUpdatedon(LocalDateTime.now());

            User savedUser = userService.updateUser(existingUser);
            return savedUser;
        } catch (Exception ex) {
            throw new MedITException("Could not update the user successfully.", ex);
        }
    }

    @DeleteMapping("/user/{id}")
    public void deleteUser(@PathVariable String id) {
        try {

            Long userId = Long.valueOf(id);

            User user = userService.getUser(userId);
            if(user == null) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND);
            }
            else{
                userService.deleteUser(user);
            }
        }catch(Exception ex){
            throw new MedITException("Could not delete the new user successfully.");
        }
    }

    // Other CRUD operations and business logic methods can be added similarly
}




