package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.service.AssessmentService;
import ca.medit.learnerchart.service.MedSISTaskService;
import ca.medit.learnerchart.service.UserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://learnerchartfeeder.qa.medicine.utoronto.ca","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@Log4j2
public class AdminController {

    @Autowired
    private MedSISTaskService medSISTaskService;

    @Autowired
    private UserService userSvc;

    @Value("${medsis.file.base}")
    private String SAVE_DIR;

    @Value("${medsis.file.student}")
    private String FILENAME;

    @Autowired
    private AssessmentService assessmentService;

    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        model.addAttribute("currentPage", "dashboard");
        List<Number> chartData = Arrays.asList(53, 150, 92, 47, 5);
        List<String> chartLabels = Arrays.asList("Assignments", "Evaluations", "Examsoft", "Performance Based/ OSCE", "Progress Tests");

        model.addAttribute("chartData", chartData);
        model.addAttribute("chartLabels", chartLabels);
        // Add other attributes
        return "dashboard";
    }

    @GetMapping("/dashboard2")
    public String dashboard2(Model model) {
        // Add other attributes
        // Sample data - replace with your actual data source
        List<Number> chartData = Arrays.asList(150, 53, 92, 47, 5);
        List<String> chartLabels = Arrays.asList("Assignments", "Evaluations", "Examsoft", "Performance Based/ OSCE", "Progress Tests");

        model.addAttribute("chartData", chartData);
        model.addAttribute("chartLabels", chartLabels);

        return "dashboard2";
    }

    @GetMapping("/userss")
    public String users(Model model) {
        model.addAttribute("currentPage", "users");
        // Add user list
        return "users";
    }

    @GetMapping("/jsonUpload")
    public String jsonUpload(Model model) {
        model.addAttribute("jsonUpload", "jsonUpload");
        // Add user list
        return "jsonUpload";
    }

    @GetMapping("/index")
    public String index() {
        return "index";
    }

    @GetMapping("/importStudentData")
    public String importStudentData(Model model) {

        medSISTaskService.runStudentDataImport();
        model.addAttribute("currentPage", "settings");
        model.addAttribute("msgToBeRendered", "Imported the student data.");

        return "settings";
    }
    @PostMapping("/appl/admin/importStudentData")
    @ResponseBody
    public ResponseEntity<?> importStudentDataFile(@RequestParam("feedFile") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Please select a file to upload"
            ));
        }

        try {
            // Process the file
            medSISTaskService.runStudentDataImportFile(file);

            // Return a direct response, not a redirect
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully imported student data from " + file.getOriginalFilename()
            ));
        } catch (Exception e) {
            log.error("Error importing student data", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                    "success", false,
                    "message", "Failed to import file: " + e.getMessage()
            ));
        }
    }
    @PostMapping("/uploadFeedData")
    public String uploadFeedData(@RequestParam("feedFile") MultipartFile file,
                                 @RequestParam("uploadType") String uploadType,
                                 Model model) {
        // Check if the uploaded file is empty
        if (file.isEmpty()) {
            model.addAttribute("error", "No file selected. Please upload a valid file.");
            return "error"; // Replace with your error view
        }

        // Check if the selected upload type is valid
        if (!"student".equals(uploadType)) {
            model.addAttribute("error", "Invalid upload type. Only 'Student Data' is supported.");
            return "error"; // Replace with your error view
        }

        try {
            // Ensure the save directory exists
            File directory = new File(SAVE_DIR);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // Save the uploaded file
            Path savePath = new File(directory, FILENAME).toPath();
            Files.copy(file.getInputStream(), savePath, StandardCopyOption.REPLACE_EXISTING);

            model.addAttribute("message", "File uploaded successfully.");
        } catch (IOException e) {
            e.printStackTrace();
            model.addAttribute("error", "Failed to upload the file: " + e.getMessage());
            return "error"; // Replace with your error view
        }

        return "dashboard"; // Replace with your success view
    }

    @GetMapping("/importStudentEvaluationData")
    public String importStudentEvaluationData(Model model) {

        medSISTaskService.runEvaluationDataImport();
        model.addAttribute("currentPage", "settings");
        model.addAttribute("msgToBeRendered", "Imported the evaluation data.");

        return "settings";
    }

    @PostMapping("/appl/admin/importEvaluationData")
    @ResponseBody
    public ResponseEntity<?> importStudentEvaluationDataFile(@RequestParam("feedFile") MultipartFile file, Model model) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Please select a file to upload"
            ));
        }

        try {
            // Process the file
            medSISTaskService.runEvaluationDataImportFile(file);

            // Add success message
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully imported evaluation data from " + file.getOriginalFilename()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                    "success", false,
                    "message", "Failed to import file: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/importImageData")
    public String importStudentImageImport(Model model) {

        medSISTaskService.runStudentImageImport();
        model.addAttribute("currentPage", "settings");
        model.addAttribute("msgToBeRendered", "Imported the image data.");

        return "settings";
    }

    @GetMapping("/appl/access-denied")
    public String renderAccessDeniedError(Model model) {

        medSISTaskService.runStudentImageImport();
        model.addAttribute("currentPage", "access-denied");
        model.addAttribute("msgToBeRendered", "Access Denied");
        model.addAttribute("msgToBeRenderedInDetails", "403 - You do not have access to this page. Please contact your system administrator if you believe this is an error. If you are a student, please contact the Student Affairs Office for assistance. If you are a faculty member, please contact the Faculty Affairs Office for assistance. If you are a staff member, please contact the IT Help Desk for assistance. Thank you for your understanding.");

        return "access-denied";
    }


    @PostMapping("/appl/admin/importImageData")
    @ResponseBody
    public ResponseEntity<?> importStudentImageImportFile(@RequestParam("feedFile") MultipartFile file, Model model) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Please select a file to upload"
            ));
        }

        try {
            // Process the file
            medSISTaskService.runStudentImageImportFile(file);

            // Add success message
            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully imported student-image data from " + file.getOriginalFilename()
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                    "success", false,
                    "message", "Failed to import file: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/settings")
    public String settings(Model model) {
        model.addAttribute("currentPage", "settings");
        String MEDSIS_EVALUATION_DATE_FORMAT = "dd-MMM-yyyy";


        try {
            Assessment assessment = new Assessment();
            assessment.setAssessmentType("Clinical Assessment");
            assessment.setDueDate(LocalDateTime.now());

          //  assessment.setDueDate(new LocalDateTime(new SimpleDateFormat(MEDSIS_EVALUATION_DATE_FORMAT).parse("26-Sep-2024")));

            assessment.setName("Clinical Assessment");
            assessment.setProgramYear(3);
            assessment.setRemoteId(28017096L);
            assessment.setCourseCode("ANS 310Y");
            assessment.setSupervisorFirstName("John");
            assessment.setSupervisorLastName("Doe");
            assessment.setAcademicYear(2024);
            assessment.setSource(FeedSourceTypeEnum.MEDSIS);
            assessment.setAssessmentMode(AssessmentMode.EVALUATION);
            assessment.setUpdatedon(LocalDateTime.now());
            assessment.setCreatedon(LocalDateTime.now());
            assessment.setOptlock(0L);
            Set<Score> scores = new HashSet<>();

            //  size = 7
            Score score = new Score();
            score.setCplanXid("COMM");
            score.setAssessment(assessment);
            score.setNumberOfItems(3.0f);
            score.setRawValue("MEET_REQUIREMENTS");
            score.setUtorid("braytess");
            score.setSource(FeedSourceTypeEnum.MEDSIS);
            scores.add(score);

            Score score2 = new Score();
            score2.setCplanXid("ADV");
            score2.setNumberOfItems(2.0f);
            score2.setRawValue("MEET_REQUIREMENTS");
            score2.setUtorid("braytess");
            score2.setSource(FeedSourceTypeEnum.MEDSIS);
            score2.setAssessment(assessment);
            scores.add(score2);

            Score score3 = new Score();
            score3.setCplanXid("FINAL_SCORE_XID");
            score3.setNumberOfItems(11.0f);
            score3.setRawValue("MEET_REQUIREMENTS");
            score3.setUtorid("braytess");
            score3.setSource(FeedSourceTypeEnum.MEDSIS);
            score3.setAssessment(assessment);
            scores.add(score3);

            assessment.setScores(scores);

            assessmentService.saveAssessment(assessment);

           // userSvc.createUser(user);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error creating user", e);
        }
        // Add user list
        return "settings";
    }

}
