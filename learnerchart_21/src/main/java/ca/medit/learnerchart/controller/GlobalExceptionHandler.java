package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;

@ControllerAdvice
@RequestMapping("/error")
@Log4j2
public class GlobalExceptionHandler {

    @ExceptionHandler(MedITException.class)
    public ResponseEntity handleCustomException(final MedITException ex){
        log.error("Error/Exception:", ex);

        return new ResponseEntity<>("We could not process this request. Please contact Med IT Helpdesk with the error message/code. The error message/code: "+ex.getMessage(), HttpStatus.OK);

    }

}
