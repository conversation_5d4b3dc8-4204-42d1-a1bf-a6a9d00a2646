package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.service.CanMedsRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/canMedsRoles")
public class CanMedsRoleController {

    private static final Logger logger = LogManager.getLogger(CanMedsRoleController.class);

    @Autowired
    private CanMedsRoleService canMedsRoleService;

    @GetMapping("")
    @Operation(summary = "Get a list of CanMEDS roles", description = "Fetch the list of CanMEDS roles available in the system (Medical Expert, Communicator, Collaborator, Leader, Health Advocate, Scholar and Professional)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of CanMEDS roles could be returned successfully"),
            @ApiResponse(responseCode = "404", description = "No CanMEDS roles could be found"),
            @ApiResponse(responseCode = "500", description = "Error occurred while fetching the list of CanMEDS roles")
    })
    public Object getCanMedsRoles(HttpServletResponse resp) {
        JsonResponse json = new JsonResponse();
        try {
            json = canMedsRoleService.getCanMedsRoles();
        } catch (Exception e) {
            logger.error("Error fetching CanMeds Roles", e);
            resp.setStatus(500);
           // json = "An error occurred";
        }
        return json;
    }
}
