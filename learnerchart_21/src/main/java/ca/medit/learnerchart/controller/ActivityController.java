package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.dto.LearnerFile;
import ca.medit.learnerchart.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/activities")
@Log4j2
public class ActivityController {

    @Autowired
    private ActivityTimelineJsonService activityJsonSvc;

    @Autowired
    private AssessmentModeService assessmentModeSvc;

    @Autowired
    private LearnerChartFilterService filterSvc;

    @Autowired
    private ActivityService activitySvc;



    @RequestMapping(value = "mode/{mode}/year/{yearId}/learner/{utorId}", method = RequestMethod.GET)
    @Operation(summary = "Get a list of activities by assessment-mode, year and UTOR ID", description = "Fetch the list of available activities for the given student, assessment-mode and year")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of activities could be returned successfully for the given student, assessment-mode and year"),
            @ApiResponse(responseCode = "404", description = "No activities could be found for the given student, assessment-mode and year"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of activities for the given student, assessment-mode and year")
    })
    public Object getActivities(@Parameter(description = "1 of the 4 available assessment modes (WRITTEN, PERFORMANCE_BASED, EVALUATION, ASSIGNMENT)", required = true)  @PathVariable("mode") String assessmentModeKey,
                                @Parameter(description = "Year ID of the student", required = true) @PathVariable("yearId") Integer yearId,
                                @Parameter(description = "UTOR ID of the student", required = true) @PathVariable("utorId") String utorId, HttpServletResponse resp) {
        Object json = null;

        try {
            AssessmentMode mode = assessmentModeSvc.getModeForKey(assessmentModeKey);

            LearnerChartFilter filter = filterSvc.getFilter(yearId, utorId, mode);
            json = activityJsonSvc.getActivitiesForTimeLine(filter);

        } catch (Exception e) {
         //   logger.catching(e);
            throw new MedITException(e.getMessage(), e);
           // resp.setStatus(500);
        }

        return json;
    }

    @RequestMapping(value = "/{activityId}/mode/ASSIGNMENT/learner/{utorId}", method = RequestMethod.GET)
    public Object getAssignmentActivityDetail(@PathVariable("activityId") Long activityId,
                                              @PathVariable("utorId") String utorId, HttpServletResponse resp) {
        Object json = null;
        try {
            json = activityJsonSvc.getAssignmentActivityDetail(activityId, utorId);
        } catch (Exception e) {
            log.catching(e);
           // json = errorSvc.getErrorResponse(e);
            throw new MedITException(e.getMessage(), e);

            //resp.setStatus(500);
        }
        return json;
    }

    @RequestMapping(value = "/osce/{scoreId}/file", method = RequestMethod.GET)
    public Object getOsceFile(@PathVariable(value = "scoreId") Long scoreId, HttpServletResponse response) {
        Object json = null;
        log.entry();
        try {
            LearnerFile file = activitySvc.getOsceFile(scoreId);
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getFileName() + "\"");
            IOUtils.copy(file.getInputStream(), response.getOutputStream());
            response.flushBuffer();
//        } catch (Exception ade) {
//            logger.catching(Level.ERROR, ade);
//            json = errorSvc.getErrorResponse(new DCException("Access denied", ade));
//            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
//        } catch (DCServiceException dse) {
//            logger.catching(Level.ERROR, dse);
//            json = errorSvc.getErrorResponse(dse);
//            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        log.exit();
        return json;
    }


}
