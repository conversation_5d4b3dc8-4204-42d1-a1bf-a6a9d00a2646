package ca.medit.learnerchart.controller;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.*;

@Controller
@Log4j2
public class ShibbolethAttributesController {

    @GetMapping("/appl/shibboleth-attributes")
    public String listSamlAttributes(HttpServletRequest request, Model model) {
        // Retrieve attributes stored by the filter
        Map<String, String> allRequestAttributes = (Map<String, String>) request.getAttribute("allRequestAttributes");
        if (allRequestAttributes == null) {
            allRequestAttributes = new HashMap<>();
        }

        // Collect debug information
        List<String> debugInfo = new ArrayList<>();
        debugInfo.add("=== All Request Attributes ===");
        Enumeration<String> attributeNames = request.getAttributeNames();
        while (attributeNames != null && attributeNames.hasMoreElements()) {
            String attrName = attributeNames.nextElement();
            Object attrValue = request.getAttribute(attrName);
            String value = attrValue != null ? attrValue.toString() : "null";
            allRequestAttributes.putIfAbsent(attrName, value);
            debugInfo.add("Attribute: " + attrName + " = " + value);
        }

        // Collect all headers
        debugInfo.add("=== All HTTP Headers ===");
        Map<String, String> allHeaders = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames != null &&  headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            allHeaders.put(headerName, headerValue);
            debugInfo.add("Header: " + headerName + " = " + headerValue);
        }

        // Combine with filter debug info
        List<String> filterDebugInfo = (List<String>) request.getAttribute("filterDebugInfo");
        if (filterDebugInfo != null) {
            debugInfo.addAll(0, filterDebugInfo); // Prepend filter debug info
        }

        // Add attributes, headers, and debug info to the model
        model.addAttribute("samlAttributes", allRequestAttributes);
        model.addAttribute("headers", allHeaders);
        model.addAttribute("debugInfo", debugInfo);

        return "shibboleth-attributes";
    }
}