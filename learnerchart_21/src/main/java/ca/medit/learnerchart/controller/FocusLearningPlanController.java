package ca.medit.learnerchart.controller;


import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.service.ArtifactJsonService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@Log4j2
@RequestMapping("/appl/artifacts/learningPlan")
public class FocusLearningPlanController {

    @Autowired
    protected ArtifactJsonService jsonSvc;

    @RequestMapping(value = "file/learner/{utorId}", method = RequestMethod.GET)
    public Object getFileArtifactsForLearner(@PathVariable(value = "utorId") String utorId, HttpServletResponse response) {
        Object json = null;
        log.entry();
        try {
            json = jsonSvc.getFocusedLearningPlansForLearner(utorId);

        } catch (Exception e) {
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        log.exit();
        return json;
    }


}
