package ca.medit.learnerchart.controller;


import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.service.ArtifactJsonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin(origins = {"http://localhost:4200","https://kind-sky-048ac441e.6.azurestaticapps.net"}, allowCredentials = "true")
@RequestMapping("/appl/artifacts")
@Log4j2
public class ArtifactController {


    @Autowired
    protected ArtifactJsonService jsonSvc;

    @RequestMapping(value = "text/learner/{utorId}", method = RequestMethod.GET)
    @Operation(summary = "Get a list of text artifacts by learnerId", description = "Fetch the list of text artifacts by Learner's UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of text artifacts could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No text artifacts could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of text artifacts for the given student")
    })
    public Object getTextArtifactsForLearner(@PathVariable(value = "utorId") String utorId, HttpServletResponse resp) {
        Object json = null;

        try {
            json = jsonSvc.getTextArtifactsForLearner(utorId);
        } catch (Exception e) {
            log.catching(e);
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return json;
    }

    @RequestMapping(value = "file/learner/{utorId}", method = RequestMethod.GET)
    @Operation(summary = "Get a list of file artifacts by learnerId", description = "Fetch the list of file artifacts by Learner's UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of file artifacts could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No file artifacts could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of file artifacts for the given student")
    })
    public Object getFileArtifactsForLearner(@PathVariable(value = "utorId") String utorId, HttpServletResponse resp) {
        Object json = null;

        try {
            json = jsonSvc.getFileArtifactsForLearner(utorId);
        } catch (Exception e) {
            log.catching(e);
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return json;
    }

    @RequestMapping(value = "link/learner/{utorId}", method = RequestMethod.GET)
    @Operation(summary = "Get a list of link artifacts by learnerId", description = "Fetch the list of link artifacts by Learner's UTOR ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of link artifacts could be returned successfully for the given student"),
            @ApiResponse(responseCode = "404", description = "No link artifacts could be found for the given student"),
            @ApiResponse(responseCode = "400", description = "Error occurred while fetching the list of link artifacts for the given student")
    })
    public Object getLinkArtifactsForLearner(@PathVariable(value = "utorId") String utorId, HttpServletResponse resp) {
        Object json = null;

        try {
            json = jsonSvc.getLinkArtifactsForLearner(utorId);
        } catch (Exception e) {
            log.catching(e);
            log.catching(Level.ERROR, e);
            json = new MedITException("Error getting file", e);
            resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }

        return json;
    }
}
