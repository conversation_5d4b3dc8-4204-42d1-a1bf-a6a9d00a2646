package ca.medit.learnerchart.domain;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum AssessmentType {


    WEEKLY_EXERCISE("Weekly feedback quiz", AssessmentMode.WRITTEN, 1L),
    /**/
    MASTERY_EXERCISE("Mastery exercise", AssessmentMode.WRITTEN, 2L),
    /**/
    OSCE("OSCE", "OSCE", AssessmentMode.PERFORMANCE_BASED, 6L),
    /**/
    BELL_RINGER("Anatomy bell ringer", AssessmentMode.WRITTEN, 5L),
    /**/
    PROGRESS_TEST("Progress test", AssessmentMode.WRITTEN, 7L),
    /**/
    EVALUATION("Evaluation", AssessmentMode.EVALUATION, 4L),
    /**/
    PORTFOLIO("Portfolio", AssessmentMode.ASSIGNMENT, 3L),
    /**/
    REFLECTION("Reflections", AssessmentMode.ASSIGNMENT, 3L),
    /**/
    ASSIGNMENT("Assignment", AssessmentMode.ASSIGNMENT, 3L),
    /**/
    CLINICAL_BASED_ASSESSMENT("Other clinical based assessment", AssessmentMode.PERFORMANCE_BASED, 6L),
    /**/
    EPA_SUMMARIES("EPA Summary", AssessmentMode.WRITTEN, 8L);

    private final String label;
    private String labelPlural;
    private final AssessmentMode mode;
    private Long order;

    AssessmentType(String label, AssessmentMode mode) {
        this(label, label + "s", mode);
    }

    AssessmentType(String label, String labelPlural, AssessmentMode mode) {
        this.label = label;
        this.labelPlural = labelPlural;
        this.mode = mode;
    }

    AssessmentType(String label, String labelPlural, AssessmentMode mode, Long order) {
        this.label = label;
        this.labelPlural = labelPlural;
        this.mode = mode;
        this.order = order;
    }

    AssessmentType(String label, AssessmentMode mode, Long order) {
        this.label = label;
        this.mode = mode;
        this.order = order;
    }


    /**
     * @return the label
     */
    public String getLabel() {
        return label;
    }

    public String getLabelPlural() {
        return labelPlural;
    }

    public AssessmentMode getMode() {
        return mode;
    }

    public Long getOrder() {
        return order;
    }

    public static List<String> getAssessmentTypesByMode(AssessmentMode assessmentMode) {
        List<String> assessmentTypes = new ArrayList<String>();
        for (AssessmentType assessmentType : AssessmentType.values()) {
            if (assessmentType.getMode().equals(assessmentMode)) {
                assessmentTypes.add(assessmentType.name());
            }
        }
        return assessmentTypes;
    }

    public static List<AssessmentType> getExamSoftTypes(AssessmentMode written) {
        return Arrays.asList(WEEKLY_EXERCISE, MASTERY_EXERCISE, BELL_RINGER, PROGRESS_TEST, EPA_SUMMARIES, CLINICAL_BASED_ASSESSMENT);
    }

    public static List<AssessmentType> getPerformanceBaseTypes(AssessmentMode performaceBased) {
        return List.of(OSCE);
    }

}
