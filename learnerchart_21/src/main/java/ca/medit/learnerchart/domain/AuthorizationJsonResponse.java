package ca.medit.learnerchart.domain;

import java.io.Serializable;
import java.util.*;

public class AuthorizationJsonResponse extends JsonResponse implements Serializable {

    public AuthorizationJsonResponse() {
        data = new AuthorizationJsonData();
    }

    public class AuthorizationJsonData implements Serializable {
        private static final long serialVersionUID = 1L;
        private final Map<String, SupervisorJson> supervisorMap = new LinkedHashMap<>();
        private final Set<String> administrators = new LinkedHashSet<>();

        public Collection<SupervisorJson> getSupervisors() {
            return supervisorMap.values();
        }

        public List<UserJson> getAdministrators() {
            List<UserJson> list = new ArrayList<>();
            for (String utorid : administrators) {
                list.add(new UserJson(utorid));
            }
            return list;
        }

        public void addSupervisorForLearner(String utorId, String learnerId) {
            if (!supervisorMap.containsKey(utorId)) {
                supervisorMap.put(utorId, new SupervisorJson(utorId, learnerId));
            } else {
                supervisorMap.get(utorId).addLearner(learnerId);
            }
        }

        public void addAdministrator(String utorId) {
            administrators.add(utorId);
        }

        @Override
        public String toString() {
            return "AuthorizationJsonData [supervisorMap=" + supervisorMap + ", administrators=" + administrators + "]";
        }

    }

    public class UserJson implements Serializable {
        private static final long serialVersionUID = 1L;
        private final String utorid;

        public UserJson(String learnerId) {
            this.utorid = learnerId;
        }

        public String getUtorid() {
            return utorid;
        }

        @Override
        public String toString() {
            return "UserJson [utorid=" + utorid + "]";
        }
    }

    public class SupervisorJson implements Serializable {
        private static final long serialVersionUID = 1L;
        private String utorid;
        private final Set<String> learners = new LinkedHashSet<>();

        public SupervisorJson(String supervisorId, String learnerId) {
            this.utorid = supervisorId;
            learners.add(learnerId);
        }

        public void addLearner(String learnerId) {
            learners.add(learnerId);
        }

        public String getUtorid() {
            return utorid;
        }

        public void setUtorid(String utorid) {
            this.utorid = utorid;
        }

        public List<UserJson> getLearners() {
            List<UserJson> list = new ArrayList<>();
            for (String utorId : learners) {
                list.add(new UserJson(utorId));
            }
            return list;
        }

        @Override
        public String toString() {
            return "SupervisorJson [utorid=" + utorid + ", learners=" + learners + "]";
        }
    }

    @Override
    public AuthorizationJsonData getData() {
        return (AuthorizationJsonData)data;
    }

    public AuthorizationJsonResponse addSupervisorForLearner(String utorId, String learnerId) {
        getData().addSupervisorForLearner(utorId, learnerId);
        return this;
    }

    public AuthorizationJsonResponse addAdministrator(String utorId) {
        getData().addAdministrator(utorId);
        return this;
    }

}
