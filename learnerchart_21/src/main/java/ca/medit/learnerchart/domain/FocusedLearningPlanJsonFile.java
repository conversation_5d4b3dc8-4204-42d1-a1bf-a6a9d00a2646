package ca.medit.learnerchart.domain;

public class FocusedLearningPlanJsonFile extends JsonFileArtifact{


    private Integer programYear;
    private Integer academicYear;
    private LearningActivityTypeEnum activityType;

    public Integer getProgramYear() {
        return programYear;
    }

    public void setProgramYear(Integer programYear) {
        this.programYear = programYear;
    }

    public Integer getAcademicYear() {
        return academicYear;
    }

    public void setAcademicYear(Integer academicYear) {
        this.academicYear = academicYear;
    }

    public LearningActivityTypeEnum getActivityType() {
        return activityType;
    }

    public void setActivityType(LearningActivityTypeEnum activityType) {
        this.activityType = activityType;
    }


}
