package ca.medit.learnerchart.domain;

import ca.medit.learnerchart.entity.CplanXid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.Iterator;
import java.util.Set;

public class JsonArtifact {


    protected Long id;
    @NotNull
    @NotEmpty
    protected String name;
    protected String[] xids;

    public JsonArtifact() {
    }

    public JsonArtifact(Long id) {
        this.id = id;
    }

    public JsonArtifact(String name) {
        this.name = name;
    }

    public JsonArtifact(String[] xids) {
        this.xids = xids;
    }

    public JsonArtifact(String name, String[] xids) {
        this.name = name;
        this.xids = xids;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String[] getXids() {
        return xids;
    }

    public void setXids(String[] xids) {
        this.xids = xids;
    }

    public void setXids(Set<CplanXid> cplanXids) {
        if (cplanXids == null) return;
        int i = 0;
        xids = new String[cplanXids.size()];
        Iterator<CplanXid> iterator = cplanXids.iterator();
        while (iterator.hasNext()) {
            CplanXid cplanXid = iterator.next();
            xids[i++] = cplanXid.getValue();
        }
    }


}
