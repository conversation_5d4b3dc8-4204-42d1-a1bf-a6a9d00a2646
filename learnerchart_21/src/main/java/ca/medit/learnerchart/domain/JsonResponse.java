package ca.medit.learnerchart.domain;

import java.io.Serializable;

public class JsonResponse implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -759594665885097090L;

    protected Object data;

    public JsonResponse() {
    }

    public JsonResponse(String string) {
        this.data = string;
    }

    /**
     * @return the data
     */
    public Object getData() {
        return data;
    }

    /**
     * @param data
     *            the data to set
     */
    public JsonResponse setData(Object data) {
        this.data = data;
        return this;
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "JsonResponse [data=" + data + "]";
    }

}
