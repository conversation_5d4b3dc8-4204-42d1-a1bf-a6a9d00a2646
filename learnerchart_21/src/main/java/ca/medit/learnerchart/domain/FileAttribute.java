package ca.medit.learnerchart.domain;

import java.time.LocalDateTime;

public class FileAttribute implements Attribute{


    private LocalDateTime dateCreated;
    private String fileName;
    private Long fileSize;
    private String fileType;
    private String fileLink;
    /**
     * @return the dateCreated
     */
    public LocalDateTime getDateCreated() {
        return dateCreated;
    }
    /**
     * @return the fileName
     */
    public String getFileName() {
        return fileName;
    }
    /**
     * @return the fileSize
     */
    public Long getFileSize() {
        return fileSize;
    }
    /**
     * @return the fileType
     */
    public String getFileType() {
        return fileType;
    }
    /**
     * @return the fileLink
     */
    public String getFileLink() {
        return fileLink;
    }
    /**
     * @param dateCreated the dateCreated to set
     */
    public void setDateCreated(LocalDateTime dateCreated) {
        this.dateCreated = dateCreated;
    }
    /**
     * @param fileName the fileName to set
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    /**
     * @param fileSize the fileSize to set
     */
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    /**
     * @param fileType the fileType to set
     */
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    /**
     * @param fileLink the fileLink to set
     */
    public void setFileLink(String fileLink) {
        this.fileLink = fileLink;
    }


}
