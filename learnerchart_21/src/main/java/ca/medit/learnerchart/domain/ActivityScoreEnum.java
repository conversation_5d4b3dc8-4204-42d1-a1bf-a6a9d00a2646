package ca.medit.learnerchart.domain;

import ca.medit.learnerchart.entity.Score;

import java.util.Arrays;
import java.util.List;

public enum ActivityScoreEnum {

    MEET_REQUIREMENTS("Satisfactory"), NOT_MEET_REQUIREMENTS(
            "Needs attention"), UNABLE_TO_ASSESS(
            "Unable to assess"), NUMERIC("Percent value"), INCOMPLETE("Not captured"), LABEL_NUMERIC("Label numeric");

    private String label;

    ActivityScoreEnum(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public static List<ActivityScoreEnum> getPassFailValues() {
        return Arrays.asList(MEET_REQUIREMENTS, NOT_MEET_REQUIREMENTS, UNABLE_TO_ASSESS);
    }

    public static ActivityScoreEnum getEnumFromScore(Score score) {
        ActivityScoreEnum e = null;
        if (score != null ) {
            e = getEnumFromRawValue(score.getRawValue());
        }
        return e;
    }

    public static ActivityScoreEnum getEnumFromRawValue(String rawValue) {
        ActivityScoreEnum e = null;
        if (rawValue != null) {
            for (ActivityScoreEnum ase : ActivityScoreEnum.values()) {
                if (rawValue.equalsIgnoreCase(ase.name())) {
                    e = ase;
                    break;
                }
            }
        }
        return e;
    }

//	public static ActivityScoreEnum forCplanXidStringAndMode(String cplanXidString, AssessmentMode mode) {
//		ActivityScoreEnum e = null;
//		if(mode == AssessmentMode.WRITTEN) {
//			if(isExamSoftAbsenteeScore(cplanXidString)) {
//				e = INCOMPLETE;
//			} else if (isExamSoftTotalScore(cplanXidString)) {
//				e = NUMERIC;
//			}
//		}
//		return e;
//	}
//
//	private static boolean isExamSoftAbsenteeScore(String xidString) {
//		if (xidString == null || xidString.isEmpty()
//				|| !xidString.equals(CplanXid.ABSENTEE_XID)) {
//			return false;
//		}
//		return true;
//	}
//
//	private static boolean isExamSoftTotalScore(String xidString) {
//		if (xidString == null || xidString.isEmpty()
//				|| !xidString.equals(CplanXid.TOTAL_SCORE_XID)) {
//			return false;
//		}
//		return true;
//	}

//	private static LearnerActivity getExamSoftTotalScore(Score score) {
//		LearnerActivity learnerExercise = new LearnerActivity(score.getAssessment());
//		learnerExercise.setPointsAvailable(score.getPointsAvailable());
//		learnerExercise.setScoreEnum(ActivityScoreEnum.NUMERIC);
//		learnerExercise.setPointsEarned(score.getPointsEarned());
//		return learnerExercise;
//	}
//
//	private static LearnerActivity getExamSoftAbsenteeScore(Score score) {
//		LearnerActivity learnerExercise = new LearnerActivity(score.getAssessment());
//		learnerExercise.setScoreEnum(ActivityScoreEnum.INCOMPLETE);
//		return learnerExercise;
//	}


}
