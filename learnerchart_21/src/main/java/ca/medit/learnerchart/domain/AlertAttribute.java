package ca.medit.learnerchart.domain;

import org.joda.time.LocalDate;

public class AlertAttribute  implements Attribute{


    private long referenceId;
    private String type;
    private String title;
    private String externalLink;
    private LocalDate date;
    private String buttonLabel = "View results";
    private boolean scholarVisible;
    private JsonResponse year;

    public long getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(long referenceId) {
        this.referenceId = referenceId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getButtonLabel() {
        return buttonLabel;
    }

    public void setButtonLabel(String buttonLabel) {
        this.buttonLabel = buttonLabel;
    }

    public boolean isScholarVisible() {
        return scholarVisible;
    }

    public void setScholarVisible(boolean scholarVisible) {
        this.scholarVisible = scholarVisible;
    }

    public JsonResponse getYear() {
        return year;
    }

    public void setYear(JsonResponse year) {
        this.year = year;
    }


}
