package ca.medit.learnerchart.domain;

import ca.medit.learnerchart.entity.TextArtifact;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

public class JsonTextArtifact extends JsonArtifact{


    @NotNull(message="Text is required")
    @NotEmpty(message="Text is required")
    private String text;

    public JsonTextArtifact() {
    }

    public JsonTextArtifact(String name, String text) {
        super(name);
        this.text = text;
    }

    public JsonTextArtifact(String name, String text, String[] xids) {
        super(name, xids);
        this.text = text;
    }

    public JsonTextArtifact(TextArtifact artifact) {
        this.name = artifact.getName();
        this.text = artifact.getText();
        //setXids(artifact.getCplanXids());
    }
    /**
     * @return the text
     */
    public String getText() {
        return text;
    }
    /**
     * @param text the text to set
     */
    public void setText(String text) {
        this.text = text;
    }


}
