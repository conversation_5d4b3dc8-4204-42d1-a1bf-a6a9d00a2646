package ca.medit.learnerchart.domain;

import java.util.ArrayList;
import java.util.List;

public class ActivityTimelineJsonData {

    private List<JsonType> activities = new ArrayList<>();
    private List<JsonType> activityTypes = new ArrayList<>();

    public ActivityTimelineJsonData() {
    }

    public List<JsonType> getActivities() {
        return activities;
    }

    public ActivityTimelineJsonData setActivities(List<JsonType> activities) {
        this.activities = activities;
        return this;
    }

    public List<JsonType> getActivityTypes() {
        return activityTypes;
    }

    public ActivityTimelineJsonData setActivityTypes(List<JsonType> activityTypes) {
        this.activityTypes = activityTypes;
        return this;
    }

}
