package ca.medit.learnerchart.domain;

public class ActivityRelationship implements Relationship{

    private JsonResponse activityType;
    private JsonResponse cohortAverage;
    /**
     * @return the activityType
     */
    public JsonResponse getActivityType() {
        return activityType;
    }
    /**
     * @param activityType the activityType to set
     */
    public void setActivityType(JsonResponse activityType) {
        this.activityType = activityType;
    }
    /**
     * @return the cohortAverage
     */
    public JsonResponse getCohortAverage() {
        return cohortAverage;
    }
    /**
     * @param cohortAverage the cohortAverage to set
     */
    public void setCohortAverage(JsonResponse cohortAverage) {
        this.cohortAverage = cohortAverage;
    }
    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "ActivityRelationship [activityType=" + activityType
                + ", cohortAverage=" + cohortAverage + "]";
    }

}
