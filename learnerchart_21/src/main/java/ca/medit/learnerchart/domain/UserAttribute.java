package ca.medit.learnerchart.domain;

import org.joda.time.LocalDateTime;

public class UserAttribute implements Attribute{

    private String firstName;
    private String lastName;
    private String email;
    private String utorid;
    private boolean canAddSupportingDocument = true;
    private boolean canAddFocusedLearningPlan;
    private LocalDateTime lastLogin;
    private JsonResponse alerts;

    public String getFirstName() {
        return firstName;
    }

    public UserAttribute setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public String getLastName() {
        return lastName;
    }

    public UserAttribute setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public UserAttribute setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getUtorid() {
        return utorid;
    }

    public UserAttribute setUtorid(String utorid) {
        this.utorid = utorid;
        return this;
    }

    public LocalDateTime getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(LocalDateTime lastLogin) {
        this.lastLogin = lastLogin;
    }

    public boolean isCanAddSupportingDocument() {
        return canAddSupportingDocument;
    }

    public void setCanAddSupportingDocument(boolean canAddSupportingDocument) {
        this.canAddSupportingDocument = canAddSupportingDocument;
    }

    public boolean isCanAddFocusedLearningPlan() {
        return canAddFocusedLearningPlan;
    }

    public void setCanAddFocusedLearningPlan(boolean canAddFocusedLearningPlan) {
        this.canAddFocusedLearningPlan = canAddFocusedLearningPlan;
    }

    public JsonResponse getAlerts() {
        return alerts;
    }

    public void setAlerts(JsonResponse alerts) {
        this.alerts = alerts;
    }

    @Override
    public String toString() {
        return "UserAttribute [utorid=" + utorid + "]";
    }

}
