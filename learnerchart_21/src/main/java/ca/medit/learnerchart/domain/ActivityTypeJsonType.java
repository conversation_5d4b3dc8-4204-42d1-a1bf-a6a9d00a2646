package ca.medit.learnerchart.domain;

public class ActivityTypeJsonType extends JsonType implements Comparable<ActivityTypeJsonType> {
    private static final long serialVersionUID = 1L;
    private String value;
    private String label;
    private Long order = 0L;
    private ActivityTypeAttribute attributes = new ActivityTypeAttribute();
    private final ActivityTypeRelationship relationships = new ActivityTypeRelationship();

    public ActivityTypeJsonType(String value) {
        super(value, JsonType.TYPE_ACTIVITY_TYPE);
        if(value == null) {
            throw new IllegalArgumentException("Value is required");
        }
        this.value = value;
        createLabel();
        createOrder();
        attributes = new ActivityTypeAttribute().setName(label).setNamePlural(label + "s").setOrder(order);
    }

    public ActivityTypeJsonType(String value, String label, long order) {

        super(value, JsonType.TYPE_ACTIVITY_TYPE);
        attributes = new ActivityTypeAttribute().setName(label).setNamePlural(label + "s").setOrder(order);
    }

    @Override
    public ActivityTypeAttribute getAttributes() {
        return attributes;
    }

    @Override
    public ActivityTypeRelationship getRelationships() {
        return relationships;
    }


    public ActivityTypeJsonType setMode(AssessmentMode mode) {
        relationships.setMode(new JsonResponse().setData(new JsonType(mode.name(), JsonType.TYPE_ASSESSMENT_MODE)));
        // attempt to set order again
        if (this.order == 0L) {
            try {
                AssessmentType assessmentType = AssessmentType.valueOf(mode.name());
                this.order = assessmentType.getOrder();
                attributes = new ActivityTypeAttribute().setName(label).setNamePlural(label + "s").setOrder(order);
            } catch (IllegalArgumentException e) {

            }
        }
        return this;
    }

    private void createLabel() {
        this.label = value;
        try {
            this.label = AssessmentType.valueOf(label).getLabel();
        } catch (IllegalArgumentException e) {
            // no match for label
        }
    }

    private void createOrder() {
        if (this.order == 0L) {
            String id = value.replaceAll(" ", "_").toUpperCase();
            try {
                AssessmentType assessmentType = AssessmentType.valueOf(id);
                this.order = assessmentType.getOrder();
            } catch (IllegalArgumentException e) {

            }
        }
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        //result = prime * result + ((value == null) ? 0 : value.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (!super.equals(obj))
            return false;
        return getClass() == obj.getClass();
//		ActivityTypeJsonType other = (ActivityTypeJsonType) obj;
//		if (value == null) {
//			if (other.value != null)
//				return false;
//		} else if (!value.equals(other.value))
//			return false;
    }

    @Override
    public int compareTo(ActivityTypeJsonType o) {
        if (o != null) {
            return order.compareTo(o.order);
        }
        return -1;
    }
}
