package ca.medit.learnerchart.domain;

public class ActivityScoreAttribute implements Attribute {


    private String label;
    private double pointsAvailable;
    private double pointsEarned;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public double getPointsAvailable() {
        return pointsAvailable;
    }

    public void setPointsAvailable(double pointsAvailable) {
        this.pointsAvailable = pointsAvailable;
    }

    public double getPointsEarned() {
        return pointsEarned;
    }

    public void setPointsEarned(double pointsEarned) {
        this.pointsEarned = pointsEarned;
    }


}
