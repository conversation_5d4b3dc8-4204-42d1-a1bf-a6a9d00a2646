package ca.medit.learnerchart.domain;

public class RowMapperUtils {

    public static Long nullSafeBigIntegerAsLong(Object object) {
        if(object == null) {
            return null;
        }
      //  return ((BigInteger) object).longValue();
        return (Long) object;
    }

    public static int nullSafeGetInt(Object o) {
        int i = 0;
        try {
            if (o instanceof Integer) {
                i = ((Integer) o).intValue();
            } else if (o instanceof Double) {
                i = ((Double) o).intValue();
            }
        } catch (RuntimeException e) {
            // ignore
        }
        return i;
    }

    public static String nullSafeGetString(Object o) {
        if (o == null) return null;
        return String.valueOf(o);
    }

    public static float nullSafeGetFloat(Object o) {
        float f = 0;
        try {
            if (o != null) {
                f = ((Double) o).floatValue();
            }
        } catch (RuntimeException e) {
            // ignore
        }
        return f;
    }

    public static double nullSafeGetDouble(Object o) {
        double d = 0;
        try {
            if (o != null) {
                d = (Double) o;
            }
        } catch (RuntimeException e) {
            // ignore
        }
        return d;
    }


}
