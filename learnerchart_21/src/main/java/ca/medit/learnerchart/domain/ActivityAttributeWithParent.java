package ca.medit.learnerchart.domain;

public class ActivityAttributeWithParent  extends ActivityAttribute{

    public class ActivityParent {
        private String name;
        private JsonResponse score;
        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public JsonResponse getScore() {
            return score;
        }
        public void setScore(JsonResponse score) {
            this.score = score;
        }
    }

    private ActivityParent parent;

    public ActivityParent getParent() {
        return parent;
    }

    public ActivityAttributeWithParent setParentName(String parentName) {
        if(parent == null) {
            this.parent = new ActivityParent();
        }
        parent.setName(parentName);
        return this;
    }

    public ActivityAttributeWithParent setParentScore(ActivityScoreEnum score) {
        if(parent == null) {
            this.parent = new ActivityParent();
        }
        JsonResponse finalScore = new JsonResponse();

        JsonType finalScoreData = new JsonType(score.name(),
                JsonType.TYPE_ACTIVITY_SCORE);

        ActivityScoreAttribute finalScoreAttribute = new ActivityScoreAttribute();
        finalScoreAttribute.setLabel(score.getLabel());

        finalScoreData.setAttributes(finalScoreAttribute);
        finalScore.setData(finalScoreData);
        parent.setScore(finalScore);
        return this;
    }

}
