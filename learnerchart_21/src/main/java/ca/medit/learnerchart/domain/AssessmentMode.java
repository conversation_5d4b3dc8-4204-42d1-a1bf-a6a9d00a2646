package ca.medit.learnerchart.domain;

public enum AssessmentMode {
    /**
     *
     */
    WRITTEN("Written assessment", "Weekly feedback exercises, mastery exercises, anatomy and progress tests"),
    /**
     *
     */
    PERFORMANCE_BASED("Performance based assessment", "OSCE exams and other clinical based assessments"),
    /**
     *
     */
    EVALUATION("Evaluation form", "Evaluation forms are any form where a tutor or peer or anyone evaluates a student using a scale and/or narrative feedback"),
    /**
     *
     */
    ASSIGNMENT("Assignment", "Assignments includes Clinical Case reports, Portfolio Course assignments, Clinical Learning Experience (CLE) reflections and tutorial presentations");

    private final String label;
    private final String helpText;

    AssessmentMode(String label, String helpText) {
        this.label = label;
        this.helpText = helpText;
    }

    public String getLabel() {
        return this.label;
    }

    public String getLabelPlural() {
        return this.label + "s";
    }

    public String getHelpText() {
        return this.helpText;
    }
}
