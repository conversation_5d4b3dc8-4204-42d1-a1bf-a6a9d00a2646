package ca.medit.learnerchart.domain;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;


public class DataReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("assessmentId")
    private Long assessmentId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("assessmentType")
    private String assessmentType;

    @JsonProperty("scoreId")
    private Long scoreId;

    @JsonProperty("utorid")
    private String utorid;

    @JsonProperty("pointsAvailable")
    private Double pointsAvailable;

    @JsonProperty("pointsEarned")
    private Double pointsEarned;

    @JsonProperty("rawValue")
    private String rawValue;

    @JsonProperty("cplanXid")
    private String cplanXid;

    @JsonProperty("numberOfCorrect")
    private Integer numberOfCorrect;

    @JsonProperty("numberOfItems")
    private Integer numberOfItems;

    @JsonProperty("parentRawValue")
    private String parentRawValue;

    @JsonProperty("parentName")
    private String parentName;

    @JsonProperty("studentNumber")
    private Long studentNumber;

    @JsonProperty("reference")
    private String reference;

    // Default constructor
    public DataReport() {
    }

    // Constructor with all fields
    public DataReport(Long assessmentId, String name, String assessmentType, Long scoreId,
                      String utorid, Double pointsAvailable, Double pointsEarned, String rawValue,
                      String cplanXid, Integer numberOfCorrect, Integer numberOfItems,
                      String parentRawValue, String parentName, Long studentNumber, String reference) {
        this.assessmentId = assessmentId;
        this.name = name;
        this.assessmentType = assessmentType;
        this.scoreId = scoreId;
        this.utorid = utorid;
        this.pointsAvailable = pointsAvailable;
        this.pointsEarned = pointsEarned;
        this.rawValue = rawValue;
        this.cplanXid = cplanXid;
        this.numberOfCorrect = numberOfCorrect;
        this.numberOfItems = numberOfItems;
        this.parentRawValue = parentRawValue;
        this.parentName = parentName;
        this.studentNumber = studentNumber;
        this.reference = reference;
    }

    // Getters and Setters
    public Long getAssessmentId() {
        return assessmentId;
    }

    public void setAssessmentId(Long assessmentId) {
        this.assessmentId = assessmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAssessmentType() {
        return assessmentType;
    }

    public void setAssessmentType(String assessmentType) {
        this.assessmentType = assessmentType;
    }

    public Long getScoreId() {
        return scoreId;
    }

    public void setScoreId(Long scoreId) {
        this.scoreId = scoreId;
    }

    public String getUtorid() {
        return utorid;
    }

    public void setUtorid(String utorid) {
        this.utorid = utorid;
    }

    public Double getPointsAvailable() {
        return pointsAvailable;
    }

    public void setPointsAvailable(Double pointsAvailable) {
        this.pointsAvailable = pointsAvailable;
    }

    public Double getPointsEarned() {
        return pointsEarned;
    }

    public void setPointsEarned(Double pointsEarned) {
        this.pointsEarned = pointsEarned;
    }

    public String getRawValue() {
        return rawValue;
    }

    public void setRawValue(String rawValue) {
        this.rawValue = rawValue;
    }

    public String getCplanXid() {
        return cplanXid;
    }

    public void setCplanXid(String cplanXid) {
        this.cplanXid = cplanXid;
    }

    public Integer getNumberOfCorrect() {
        return numberOfCorrect;
    }

    public void setNumberOfCorrect(Integer numberOfCorrect) {
        this.numberOfCorrect = numberOfCorrect;
    }

    public Integer getNumberOfItems() {
        return numberOfItems;
    }

    public void setNumberOfItems(Integer numberOfItems) {
        this.numberOfItems = numberOfItems;
    }

    public String getParentRawValue() {
        return parentRawValue;
    }

    public void setParentRawValue(String parentRawValue) {
        this.parentRawValue = parentRawValue;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public Long getStudentNumber() {
        return studentNumber;
    }

    public void setStudentNumber(Long studentNumber) {
        this.studentNumber = studentNumber;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    @Override
    public String toString() {
        return "DataReport{" +
                "assessmentId=" + assessmentId +
                ", name='" + name + '\'' +
                ", assessmentType='" + assessmentType + '\'' +
                ", scoreId=" + scoreId +
                ", utorid='" + utorid + '\'' +
                ", pointsAvailable=" + pointsAvailable +
                ", pointsEarned=" + pointsEarned +
                ", rawValue='" + rawValue + '\'' +
                ", cplanXid='" + cplanXid + '\'' +
                ", numberOfCorrect=" + numberOfCorrect +
                ", numberOfItems=" + numberOfItems +
                ", parentRawValue='" + parentRawValue + '\'' +
                ", parentName='" + parentName + '\'' +
                ", studentNumber='" + studentNumber + '\'' +
                ", reference='" + reference + '\'' +
                '}';
    }
}