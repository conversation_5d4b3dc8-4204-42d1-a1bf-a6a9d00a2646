package ca.medit.learnerchart.domain;

import ca.medit.learnerchart.dto.LearnerFile;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

public class JsonFileArtifact extends JsonArtifact  {


    @NotNull(message="LearnerFile is required")
    @NotEmpty(message="LearnerFile is required")
    private LearnerFile learnerFile;

    public JsonFileArtifact() {
    }

    public JsonFileArtifact(LearnerFile learnerFile) {
        this.learnerFile = learnerFile;
    }

    public JsonFileArtifact(LearnerFile learnerFile, String[] xids) {
        super(xids);
        this.learnerFile = learnerFile;
    }

    public LearnerFile getLearnerFile() {
        return learnerFile;
    }

    public void setLearnerFile(LearnerFile learnerFile) {
        this.learnerFile = learnerFile;
    }


}
