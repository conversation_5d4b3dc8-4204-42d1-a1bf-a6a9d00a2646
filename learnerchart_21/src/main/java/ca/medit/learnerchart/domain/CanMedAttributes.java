package ca.medit.learnerchart.domain;

import ca.medit.learnerchart.dto.CanMedsActivityList;
import ca.medit.learnerchart.entity.CplanXid;

public class CanMedAttributes  implements Attribute {


    private String xid;
    private String label;
    private int activityCount;
    private int questionCount;

    public CanMedAttributes() {}

    public CanMedAttributes(CplanXid role, CanMedsActivityList list) {
        this();
        this.setXid(role.getValue());
        this.setLabel(role.getLabel());
        this.setActivityCount(list.size());
        this.setQuestionCount(list.getQuestionCount());
    }

    /**
     * @return the xid
     */
    public String getXid() {
        return xid;
    }
    /**
     * @param xid the xid to set
     */
    public void setXid(String xid) {
        this.xid = xid;
    }
    /**
     * @return the label
     */
    public String getLabel() {
        return label;
    }
    /**
     * @param label the label to set
     */
    public void setLabel(String label) {
        this.label = label;
    }
    /**
     * @return the activityCount
     */
    public int getActivityCount() {
        return activityCount;
    }
    /**
     * @param activityCount the activityCount to set
     */
    public void setActivityCount(int activityCount) {
        this.activityCount = activityCount;
    }
    /**
     * @return the questionsCount
     */
    public int getQuestionCount() {
        return questionCount;
    }
    /**
     * @param questionsCount the questionsCount to set
     */
    public void setQuestionCount(int questionsCount) {
        this.questionCount = questionsCount;
    }
    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "CanMedsAttribute [xid=" + xid + ", label=" + label
                + ", activityCount=" + activityCount + ", questionCount="
                + questionCount + "]";
    }

}
