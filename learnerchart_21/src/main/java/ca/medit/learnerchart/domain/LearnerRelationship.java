package ca.medit.learnerchart.domain;

public class LearnerRelationship implements Relationship{
    private JsonResponse groups;
    private JsonResponse assignedScholars;
    private JsonResponse learnerSupporters;

    public JsonResponse getGroups() {
        return groups;
    }

    public LearnerRelationship setGroups(JsonResponse groups) {
        this.groups = groups;
        return this;
    }

    public JsonResponse getAssignedScholars() {
        return assignedScholars;
    }

    public void setAssignedScholars(JsonResponse assignedScholars) {
        this.assignedScholars = assignedScholars;
    }

    public JsonResponse getLearnerSupporters() {
        return learnerSupporters;
    }

    public void setLearnerSupporters(JsonResponse learnerSupporters) {
        this.learnerSupporters = learnerSupporters;
    }
}
