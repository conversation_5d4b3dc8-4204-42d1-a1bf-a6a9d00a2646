package ca.medit.learnerchart.domain;

public enum FeedSourceTypeEnum {


    OASES("Oases"), EXAM_SOFT("Exam Soft"), MEDSIS("MedSIS"), OSCE("OSCE"), ELENTRA("Elentra"), UNKNOWN("Unknown");

    private String key;

    FeedSourceTypeEnum(String key) {
        this.key = key;
    }

    /**
     * @return the key
     */
    public String getKey() {
        return key;
    }

    /**
     * @param key
     *            the key to set
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * Look up the Enum value matching {@code jxb}
     *
     * @param n
     * @return the feed source enum, or UNKNOWN as default
     */
    public static FeedSourceTypeEnum nullSafeValueOf(String n) {
        FeedSourceTypeEnum source = UNKNOWN;
        if (n != null) {
            try {
                source = FeedSourceTypeEnum.valueOf(n);
            } catch (IllegalArgumentException e) {
                // let e be default
            }
        }
        return source;
    }

}
