package ca.medit.learnerchart.domain;

public class LearnerAttribute implements Attribute{

    private String firstName;
    private String lastName;
    private String email;
    private String utorid;
    private String cohortCode;
    private String imageLink;
    private JsonResponse alerts;

    public String getFirstName() {
        return firstName;
    }

    public LearnerAttribute setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public String getLastName() {
        return lastName;
    }

    public LearnerAttribute setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    /**
     * Example value: "1T9"
     *
     * @return the cohort code
     */
    public String getCohortCode() {
        return cohortCode;
    }

    public LearnerAttribute setCohortCode(String cohortCode) {
        this.cohortCode = cohortCode;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public LearnerAttribute setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getUtorid() {
        return utorid;
    }

    public LearnerAttribute setUtorid(String utorid) {
        this.utorid = utorid;
        return this;
    }

    public String getImageLink() {
        return imageLink;
    }

    public LearnerAttribute setImageLink(String imageLink) {
        this.imageLink = imageLink;
        return this;
    }

    public JsonResponse getAlerts() {
        return alerts;
    }

    public LearnerAttribute setAlerts(JsonResponse alerts) {
        this.alerts = alerts;
        return this;
    }


}
