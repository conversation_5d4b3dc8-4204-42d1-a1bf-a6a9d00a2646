package ca.medit.learnerchart.domain;


import org.joda.time.LocalDate;

public class ActivityAttribute implements Attribute{



    private String name;
    private LocalDate date;
    private JsonResponse year;
    private JsonResponse finalScore;
    private int questionCount;
    private String courseCode;
    private String supervisorFirstName;
    private String supervisorLastName;
    private JsonResponse links;

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }
    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }
    /**
     * @return the date
     */
    public LocalDate getDate() {
        return date;
    }
    /**
     * @param date the date to set
     */
    public void setDate(LocalDate date) {
        this.date = date;
    }
    public JsonResponse getYear() {
        return year;
    }
    public void setYear(JsonResponse year) {
        this.year = year;
    }
    /**
     *
     * @return
     */
    public JsonResponse getFinalScore() {
        return finalScore;
    }
    /**
     *
     * @param finalScore
     */
    public void setFinalScore(JsonResponse finalScore) {
        this.finalScore = finalScore;
    }

    public int getQuestionCount() {
        return questionCount;
    }

    public void setQuestionCount(int questionCount) {
        this.questionCount = questionCount;
    }

    public JsonResponse getLinks() {
        return links;
    }

    public void setLinks(JsonResponse links) {
        this.links = links;
    }

    public String getCourseCode() {
        return courseCode;
    }

    public void setCourseCode(String courseCode) {
        this.courseCode = courseCode;
    }

    public String getSupervisorFirstName() {
        return supervisorFirstName;
    }

    public void setSupervisorFirstName(String supervisorFirstName) {
        this.supervisorFirstName = supervisorFirstName;
    }

    public String getSupervisorLastName() {
        return supervisorLastName;
    }

    public void setSupervisorLastName(String supervisorLastName) {
        this.supervisorLastName = supervisorLastName;
    }

    @Override
    public String toString() {
        return "ActivityAttribute [name=" + name + ", date=" + date + ", finalScore=" + finalScore + ", questionCount="
                + questionCount + ", courseCode=" + courseCode + ", supervisorFirstName=" + supervisorFirstName
                + ", supervisorLastName=" + supervisorLastName + ", links=" + links + "]";
    }


}
