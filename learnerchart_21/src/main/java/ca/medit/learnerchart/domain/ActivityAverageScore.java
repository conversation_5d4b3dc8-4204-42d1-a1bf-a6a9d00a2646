package ca.medit.learnerchart.domain;

public class ActivityAverageScore {


    private final double scoreDouble;
    private final long scoreInt;

    public ActivityAverageScore(double averageScore) {
        this.scoreDouble = averageScore;
        this.scoreInt = Math.round(scoreDouble);
    }

    /**
     * @return the score
     */
    public long getScore() {
        return scoreInt;
    }


    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "ActivityAverageScore [scoreDouble=" + scoreDouble + "]";
    }

}
