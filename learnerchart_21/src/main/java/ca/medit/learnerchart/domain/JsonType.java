package ca.medit.learnerchart.domain;

import java.io.Serializable;

public class JsonType implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String TYPE_ACTIVITY = "activities";
    public static final String TYPE_ACTIVITY_TYPE = "activityTypes";
    public static final String TYPE_ACTIVITY_SCORE = "activityScores";
    public static final String TYPE_ACTIVITY_FEEDBACK = "activityFeedbacks";
    public static final String TYPE_ACTIVITY_FILE = "activityFiles";
    public static final String TYPE_ASSESSMENT_MODE = "assessmentModes";
    public static final String TYPE_PROGRAM_YEAR = "years";
    public static final String TYPE_LEARNER = "learners";
    public static final String TYPE_CANMED_ROLE="canMedsRoles";
    public static final String TYPE_USER="users";
    public static final String TYPE_YEAR="years";
    public static final String TYPE_GROUP = "groupItems";
    public static final String Type_KEY_COMPETENCY = "keyCompetencies";

    public static final String Type_ENABLING_COMPETENCY = "enablingCompetencies";

    public static final String TYPE_END_OF_SUBSECTION_OBJECTIVE = "endOfSubsectionObjectives";

    public static final String TYPE_END_OF_WEEK_OBJECTIVE = "endOfWeekObjectives";

    public static final String TYPE_CATEGORY = "categories";

    public static final String TYPE_CATEGORY_ITEM = "categoryItems";

    public static final String TYPE_FILES = "files";

    public static final String TYPE_PROGRESS_REVIEW_STATES = "progressReviewStates";

    public static final String TYPE_PROGRESS_REVIEW_DOCUMENTS = "progressReviewDocuments";

    public static final String TYPE_PROGRESS_REVIEW_FEEDBACKS = "feedbacks";

    public static final String TYPE_FILE_ARTIFACTS = "fileArtifacts";

    public static final String TYPE_LINK_ARTIFACTS = "linkArtifacts";

    public static final String TYPE_TEXT_ARTIFACTS = "textArtifacts";

    public static final String TYPE_ALERTS = "alerts";

    public static final String TYPE_CPLAN_XIDS = "cplanXids";

    public static final String TYPE_LINKS = "links";

    public static final String TYPE_FOCUSED_LEARNING_PLANS = "focusedLearningPlans";

    public static final String TYPE_LEARNING_ACTIVITY_TYPES = "learningActivityTypes";

    private Object id;
    private String type;
    private Attribute attributes;
    private Relationship relationships;

    public JsonType(Object id, String type) {
        this.id = id;
        this.type = type;
    }

    /**
     * @return the id
     */
    public Object getId() {
        return id;
    }

    /**
     * @param id
     *            the id to set
     */
    public void setId(Object id) {
        this.id = id;
    }

    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type
     *            the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return the attributes
     */
    public Attribute getAttributes() {
        return attributes;
    }

    /**
     * @param attributes
     *            the attributes to set
     */
    public void setAttributes(Attribute attributes) {
        this.attributes = attributes;
    }

    /**
     * @return the relationships
     */
    public Relationship getRelationships() {
        return relationships;
    }

    /**
     * @param relationships
     *            the relationships to set
     */
    public void setRelationships(Relationship relationships) {
        this.relationships = relationships;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        return "JsonType [id=" + id + ", type=" + type + ", attributes="
                + attributes + ", relationships=" + relationships + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((id == null) ? 0 : id.hashCode());
        result = prime * result + ((type == null) ? 0 : type.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        JsonType other = (JsonType) obj;
        if (id == null) {
            if (other.id != null)
                return false;
        } else if (!id.equals(other.id))
            return false;
        if (type == null) {
            return other.type == null;
        } else return type.equals(other.type);
    }

}
