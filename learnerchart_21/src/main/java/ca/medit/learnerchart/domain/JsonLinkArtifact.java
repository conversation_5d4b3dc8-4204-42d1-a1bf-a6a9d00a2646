package ca.medit.learnerchart.domain;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

public class JsonLinkArtifact extends JsonArtifact{


    @NotNull
    @NotEmpty
    private String link;

    public String getLink() {
        return link;
    }

    /**
     * @param link
     *            the link to set
     */
    public void setLink(String link) {
        this.link = link;
    }


}
