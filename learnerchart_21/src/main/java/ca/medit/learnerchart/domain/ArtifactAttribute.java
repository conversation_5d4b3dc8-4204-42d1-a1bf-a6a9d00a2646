package ca.medit.learnerchart.domain;

public class ArtifactAttribute implements Attribute{


    private boolean hasDownloadOption = true;
    private boolean hasGearOption = true;
    private boolean hasDeleteOption = true;
    private JsonResponse year;

    public boolean isHasDownloadOption() {
        return hasDownloadOption;
    }

    public void setHasDownloadOption(boolean hasDownloadOption) {
        this.hasDownloadOption = hasDownloadOption;
    }

    public boolean isHasGearOption() {
        return hasGearOption;
    }

    public void setHasGearOption(boolean hasGearOption) {
        this.hasGearOption = hasGearOption;
    }

    public boolean isHasDeleteOption() {
        return hasDeleteOption;
    }

    public void setHasDeleteOption(boolean hasDeleteOption) {
        this.hasDeleteOption = hasDeleteOption;
    }

    public JsonResponse getYear() {
        return year;
    }

    public void setYear(JsonResponse year) {
        this.year = year;
    }


}
