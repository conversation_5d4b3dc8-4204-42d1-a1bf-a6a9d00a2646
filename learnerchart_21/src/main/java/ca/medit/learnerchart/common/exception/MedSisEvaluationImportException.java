package ca.medit.learnerchart.common.exception;

import ca.medit.learnerchart.dto.EvaluationReport.EvaluationBase;

public class MedSisEvaluationImportException  extends Exception{

    private final EvaluationBase evaluation;
    public MedSisEvaluationImportException(String message, EvaluationBase evaluation) {
        super(message);
        this.evaluation = evaluation;
    }

    public MedSisEvaluationImportException(String message, EvaluationBase evaluation, Exception e) {
        super(message, e);
        this.evaluation = evaluation;
    }

    public EvaluationBase getEvaluation() {
        return evaluation;
    }

    public String getEvaluationId() {
        return (evaluation == null ? "" : evaluation.evalId);
    }

}
