package ca.medit.learnerchart.service;

import ca.medit.learnerchart.repository.AlertRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class AlertServiceImpl implements AlertService {

    @Autowired
    private AlertRepository alertRepository;
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void updateUserAlerts() {
        log.entry();
        alertRepository.deleteAssessmentFeedAlerts();
        alertRepository.markOldAlertsAsDeleted();
//        alertRepository.insertAssessmentFeedAlerts(
//                "Progress Review Type",
//                "Assignment Type",
//                "Evaluation Type",
//                "Mastery Exercise Type",
//                "Weekly Exercise Type",
//                "Bell Ringer Type",
//                "Progress Test",
//                "EPA Summaries",
//                "Progress Review Title"
//        );
        log.exit();
    }
}
