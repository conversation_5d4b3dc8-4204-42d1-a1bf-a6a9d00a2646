package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.dto.CohortNumericScoreAverageMap;
import ca.medit.learnerchart.dto.TimelineActivity;
import ca.medit.learnerchart.service.LearnerChartFilter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TransformerTimelineActivityJson extends TransformerActivityJson{


    private final List<TimelineActivity> timelineActivities;
    private final CohortNumericScoreAverageMap activityAverageMap;
    private final List<ActivityTypeJsonType> filterValues = new ArrayList<>();

    public TransformerTimelineActivityJson(List<TimelineActivity> list, CohortNumericScoreAverageMap map,
                                           LearnerChartFilter filter) {
        super(filter);
        timelineActivities = list;
        activityAverageMap = map;
        populateActivityTypes();
    }

    private void populateActivityTypes() {
        for (TimelineActivity ta : timelineActivities) {
            ActivityTypeJsonType type = new ActivityTypeJsonType(ta.getAssessmentType()).setMode(getAssessmentMode());
            boolean hasType = false;
            for (ActivityTypeJsonType fitlerValue : filterValues) {
                if (fitlerValue.equals(type)) {
                    hasType = true;
                    break;
                }
            }
            if (!hasType) {
                filterValues.add(type);
            }
        }
    }

    public ActivityTimelineJsonData getData() {
        ActivityTimelineJsonData data = new ActivityTimelineJsonData().setActivities(timelineActivitiesToActivityJson())
                .setActivityTypes(getActivityTypes());
        return data;
    }

    private List<JsonType> getActivityTypes() {
        return new ArrayList<JsonType>(filterValues);
    }

    private List<JsonType> timelineActivitiesToActivityJson() {
        if (timelineActivities == null || timelineActivities.isEmpty()) {
            return Collections.emptyList();
        }

        List<JsonType> data = new ArrayList<JsonType>();
        for (TimelineActivity timelineActivity : timelineActivities) {
            JsonTypeWithIncluded activity = new JsonTypeWithIncluded(timelineActivity.getAssessmentId(), JsonType.TYPE_ACTIVITY);

            ActivityAttribute attributes = getAttributesForActivity(timelineActivity);
            activity.setAttributes(attributes);

            ActivityRelationshipWithCanMedsRoles relationships = new ActivityRelationshipWithCanMedsRoles();
            relationships.setActivityType(
                    TransformerActivityTypeJson.toActivityTypeJson(timelineActivity.getAssessmentType()));
            relationships.setCanMedsRoles(
                    TransformerTimelineActivityCanMedsRolesJson.toJsonResponse(timelineActivity.getCanMedsRoles()));
            if (activityAverageMap != null) {
                relationships.setCohortAverage(TransformerAverageScoreJson
                        .toAverageScoreJson(activityAverageMap.get(timelineActivity.getAssessmentId())));
            }
            activity.setRelationships(relationships);

            activity.setIncluded(new ActivityIncluded()
                    .setScores(TransformerActivityScoreJson.getActivityScoreJsonByMode(getAssessmentMode())));

            data.add(activity);
        }

        return data;
    }

    private ActivityAttribute getAttributesForActivity(TimelineActivity timelineActivity) {
        ActivityAttribute attributes = null;
        if (timelineActivity != null && timelineActivity.getAssessmentType().equals(AssessmentType.OSCE.name())) {
            attributes = new ActivityAttributeWithParent().setParentName(timelineActivity.getParentName())
                    .setParentScore(timelineActivity.getParentScoreEnum());
        } else {
            attributes = new ActivityAttribute();
        }
        setAttributeValuesFromActivity(attributes, timelineActivity);
        return attributes;
    }

    private void setAttributeValuesFromActivity(ActivityAttribute attributes, TimelineActivity timelineActivity) {
        attributes.setName(timelineActivity.getAssessmentName());
        attributes.setDate(timelineActivity.getDate().toLocalDate());
        attributes.setYear(TransformerYearJson.toYearJson(timelineActivity.getAcademicYear(), timelineActivity.getProgramYear()));
        attributes.setQuestionCount(timelineActivity.getQuestionCount());
        attributes.setCourseCode(timelineActivity.getCourseCode());
        attributes.setSupervisorFirstName(timelineActivity.getSupervisorFirstName());
        attributes.setSupervisorLastName(timelineActivity.getSupervisorLastName());
        attributes.setLinks(TransformerLinkJson.toLinkJson(timelineActivity, filter));

        JsonResponse finalScore = new JsonResponse();

        JsonType finalScoreData = new JsonType(getScoreId(timelineActivity.getScoreEnum()),
                JsonType.TYPE_ACTIVITY_SCORE);

        ActivityScoreAttribute finalScoreAttribute = new ActivityScoreAttribute();
        finalScoreAttribute.setPointsAvailable(timelineActivity.getPointsAvailable());
        finalScoreAttribute.setPointsEarned(timelineActivity.getPointsEarned());
        finalScoreAttribute.setLabel(getScoreLabel(timelineActivity.getScoreEnum()));

        finalScoreData.setAttributes(finalScoreAttribute);
        finalScore.setData(finalScoreData);

        attributes.setFinalScore(finalScore);
    }


}
