package ca.medit.learnerchart.service;

import ca.medit.learnerchart.repository.ScoreRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class ScoreServiceImpl implements ScoreService{

    @Autowired
    private ScoreRepository scoreRepository;

    @Override
    public int claimOrphanedScores() {
        // Call the custom repository method
        return scoreRepository.insertOwnersForOrphanedScores();
    }

    @Override
    @Transactional(propagation= Propagation.REQUIRED)
    public void createCplanXidLinks() {
        log.entry();
        scoreRepository.createScoreCplanXidLinks();
        log.exit();
    }
}
