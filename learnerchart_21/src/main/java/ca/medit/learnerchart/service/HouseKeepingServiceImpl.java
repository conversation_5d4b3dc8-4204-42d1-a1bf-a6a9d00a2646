package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.AssessmentEvent;
import ca.medit.learnerchart.entity.AssessmentEventKey;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.repository.AssessmentEventRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class HouseKeepingServiceImpl implements HouseKeepingService{

    @Autowired
    private ScoreService scoreSvc;

    @Autowired
    private AlertService alertSvc;


    @Autowired
    private AssessmentEventRepository assessmentEventRepository;

    @Override
    public void keep(Assessment assessment) {
        scoreSvc.createCplanXidLinks();
        scoreSvc.claimOrphanedScores();
        synchronizeAssessmentEvents(assessment);
        alertSvc.updateUserAlerts();
    }

    @Override
    public void keep(List<Assessment> assessments) {
        scoreSvc.createCplanXidLinks();
        scoreSvc.claimOrphanedScores();
        alertSvc.updateUserAlerts();
    }

    @Override
    @Transactional(propagation= Propagation.REQUIRED)
    public void synchronizeAssessmentEvents(Assessment assessment) {
        if (assessment == null) return;
        FeedSourceTypeEnum source = assessment.getSource();
        if (source != FeedSourceTypeEnum.OASES) return;
        List<AssessmentEventKey> assessmentEventKeys = assessmentEventRepository.getAssessmentEventKeysByRemoteId(source, assessment.getRemoteId());
        List<AssessmentEventKey> seenKeys = new ArrayList<AssessmentEventKey>();
        Set<Score> scores = assessment.getScores();
        if (scores != null) {
            long remoteId = assessment.getRemoteId();
            String utorId = null;
            for (Score score : scores) {
                if (score.getUtorid() != null && !score.getUtorid().equals(utorId)) {
                    utorId = score.getUtorid();
                    AssessmentEventKey assessmentEventKey = new AssessmentEventKey();
                    assessmentEventKey.setRemoteId(remoteId);
                    assessmentEventKey.setSource(source);
                    assessmentEventKey.setUtorid(utorId);
                    if (assessmentEventKeys.remove(assessmentEventKey)) {
                        seenKeys.add(assessmentEventKey);
                    } else if (!seenKeys.contains(assessmentEventKey)) {
                        AssessmentEvent assessmentEvent = new AssessmentEvent();
                        assessmentEvent.setEventKey(assessmentEventKey);
                      //  assessmentEventDAO.makePersistent(assessmentEvent);
                        assessmentEventRepository.save(assessmentEvent);
                        seenKeys.add(assessmentEventKey);
                    }
                }
            }
        }
        for (AssessmentEventKey assessmentEventKey : assessmentEventKeys) {
            assessmentEventRepository.deleteById(assessmentEventKey);
        }
    }
}
