package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.dto.*;
import ca.medit.learnerchart.entity.OscePdf;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.repository.OscePdfRepository;
import ca.medit.learnerchart.repository.ScoreRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Log4j2
public class ActivityServiceImpl implements ActivityService{

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ScoreRepository scoreRepository;

    @Autowired
    private OscePdfRepository oscePdfRepository;

    @Override
    public List<TimelineActivity> getTimelineActivities(LearnerChartFilter filter) {

      //  logger.entry();
        if (filter == null) {
            throw new MedITException("Client request was not complete. Filter is required");
        }
        List<TimelineActivity> activities = Collections.emptyList();

        try {
            List<TimelineActivity> scores = getScoresForTimeline(filter);
            if (scores != null && !scores.isEmpty()) {
                activities = scores;
            }
        } catch (Exception dde) {
            throw new MedITException(dde.getMessage(), dde);
        }
        return activities;
    }

    @Override
    public CohortNumericScoreAverageMap getActivityAverageMap(LearnerChartFilter filter) {
        CohortNumericScoreAverageMap activityAverageMap = null;
        if (filter.getMode() == AssessmentMode.WRITTEN) {
            try {
                activityAverageMap = getCohortAverageNumericScoreMap(filter);
            } catch (Exception e) {
                throw new MedITException("Error getting average scores for Written assessments", e);
            }
        }
        return activityAverageMap;
    }

    @Transactional(readOnly = true)
    public List<TimelineActivity> getScoresForTimeline(LearnerChartFilter filter) {
        if (filter == null) {
            throw new MedITException("Filter must not be null");
        }

        List<TimelineActivity> scores = new ArrayList<>();
        try {

            String sb = " select " +
                    " distinct s.RAW_VALUE, s.POINTS_AVAILABLE, " + // 0,1
                    " s.POINTS_EARNED, s.NUMBER_OF_ITEMS, " + // 2,3
                    " a.ASSESSMENT_ID, a.REMOTE_ID, a.NAME, a.ASSESSMENT_TYPE, a.DUE_DATE, s.CPLAN_XID, " + // 4,5,6,7,8,9
                    " c.CPLAN_XID_ID, c.LABEL, " + // 10,11
                    " s.PARENT_NAME, s.PARENT_RAW_VALUE, s.SCORE_ID, s.reference, " + // 12,13,14,15
                    " a.COURSE_CODE, a.SUPERVISOR_FIRST_NAME, a.SUPERVISOR_LAST_NAME, " + // 16,17,18
                    " a.ACADEMIC_YEAR, a.PROGRAM_YEAR, a.SOURCE " + // 19,20,21
                    " from " +
                    " assessment a, score s " +
                    " join authuser_score us on us.score_id=s.score_id " +
                    " join users u on us.authuser_id=u.users_id " +
                    " left outer join canmeds_map m on s.CPLAN_XID_ID = m.CPLAN_XID_ID_CHILD " +
                    " left join cplan_xid c on m.CPLAN_XID_TOP = c.CPLAN_XID_ID " +
                    " where a.ASSESSMENT_ID = s.ASSESSMENT_ID " +
                    " and a.academic_year = :academicYear " +
                    " and a.program_year = :programYear " +
                    " and u.utorid = :utorid " +
                    " and a.ASSESSMENT_MODE = :assessmentMode " +
                    " and a.assessment_type != 'PROGRESS_REVIEW' and s.DELETED = 0" +
                    " and ( a.RELEASE_DATE IS NULL OR  a.RELEASE_DATE < CAST(GETDATE() AS DATE))";

            // Native SQL query execution
            Query query = entityManager.createNativeQuery(sb);
            query.setParameter("utorid", filter.getUsername());
            query.setParameter("academicYear", filter.getAcademicYear());
            query.setParameter("programYear", filter.getProgramYear());
            query.setParameter("assessmentMode", filter.getMode().name());

            @SuppressWarnings("unchecked")
            List<Object[]> rows = query.getResultList();

            TimelineActivityMap map = new TimelineActivityMap();
            if (rows != null && !rows.isEmpty()) {
                for (Object[] row : rows) {
                    map.add(TimelineActivity.mapRowToTimelineActivity(row, filter.getMode()));
                }
            }
            scores = map.values();
        } catch (RuntimeException re) {
            throw new MedITException("Failed to get scores for Program Year " + filter.getProgramYear(), re);
        }
        return scores;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Score> getAsessmentScoresForLearner(long assessmentId, String username) {
        log.entry();
        List<Score> scores = new ArrayList<Score>();
        try {
            String query = "select s.score_id, s.raw_value, s.cplan_xid, s.reference from score s join authuser_score a on a.score_id = s.score_id "
                    + "join users u on u.users_id = a.authuser_id and u.utorid = :username "
                    + "where s.assessment_id = :assessmentId";
            List<Object[]> list = entityManager.createNativeQuery(query)
                    .setParameter("username", username)
                    .setParameter("assessmentId", assessmentId).getResultList();
            for (Object[] row : list) {
                Score score = new Score();
                score.setScoreId(((BigInteger) row[0]).longValue());
                score.setRawValue((String) row[1]);
                score.setCplanXid((String) row[2]);
                score.setReference((String) row[3]);
                scores.add(score);
            }
        } catch (RuntimeException re) {
            log.error("Failed to get the scores for assessment id = " + assessmentId + " and user name = " + username, re);
            throw new MedITException(
                    "Failed to get the scores for assessment id = " + assessmentId + " and user name = " + username,
                    re);
        }
        log.exit();
        return scores;
    }

    @Transactional(readOnly = true) // Mark the method as transactional with read-only
    public CohortNumericScoreAverageMap getCohortAverageNumericScoreMap(LearnerChartFilter filter) {
        CohortNumericScoreAverageMap map = new CohortNumericScoreAverageMap();

        try {
            String sql = "select s.POINTS_AVAILABLE, s.POINTS_EARNED, a.ASSESSMENT_ID, a.NAME, a.DUE_DATE " +
                    "from assessment a, score s where a.ASSESSMENT_ID = s.ASSESSMENT_ID " +
                    "and a.ASSESSMENT_MODE = :assessmentMode and s.CPLAN_XID = :totalScoreXid " +
                    "and a.ASSESSMENT_ID in (select distinct a.ASSESSMENT_ID from assessment a, score s, authuser_score aus, users u " +
                    "where a.ASSESSMENT_ID = s.ASSESSMENT_ID and s.SCORE_ID = aus.SCORE_ID and aus.AUTHUSER_ID = u.USERS_ID " +
                    "and a.ASSESSMENT_MODE = :assessmentMode and (s.CPLAN_XID = :totalScoreXid or s.CPLAN_XID = :absenteeXid) " +
                    "and a.ACADEMIC_YEAR = :academicYear and a.PROGRAM_YEAR = :programYear and u.username = :username)";

            // Native query execution
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("totalScoreXid", "TOTAL_SCORE_XID");
            query.setParameter("absenteeXid","ABSENTEE_XID");
            query.setParameter("username", filter.getUsername());
            query.setParameter("academicYear", filter.getAcademicYear());
            query.setParameter("programYear", filter.getProgramYear());
            query.setParameter("assessmentMode", filter.getMode().name());

            @SuppressWarnings("unchecked")
            List<Object[]> scores = query.getResultList();

            for (Object[] score : scores) {
               // Long assessmentId = ((BigInteger) score[2]).longValue();
                Long assessmentId;

                if (score[2] instanceof Long) {
                    assessmentId = (Long) score[2];
                } else if (score[2] instanceof BigInteger) {
                    assessmentId = ((BigInteger) score[2]).longValue();
                } else {
                    throw new IllegalArgumentException("Unexpected type for score[2]: " + score[2].getClass());
                }

                AverageNumericScore existingExercise = new AverageNumericScore(assessmentId);
                existingExercise.setName((String) score[3]);
                existingExercise.setDate(convertToLocalDateTime(score[4]));
                existingExercise.setPointsAvailable((Double) score[0]);
                existingExercise.setPointsEarned((Double) score[1]);
                map.add(existingExercise);
            }
        } catch (RuntimeException re) {
            throw new MedITException("Unable to get the cohort scores", re);
        }

        return map;
    }

    /**
     *
     * @param o
     * @return
     */
    public static LocalDateTime convertToLocalDateTime(Object o) {
        LocalDateTime localDateTime = null;
        if (o instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) o;
        } else if (o instanceof Timestamp) {
            localDateTime = new LocalDateTime(o);
        } else if (o instanceof String) {
            try {
                localDateTime = new LocalDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse((String)o));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return localDateTime;
    }

    @Override
    public LearnerFile getOsceFile(Long scoreId) throws SQLException {
        LearnerFile file = null;
        log.entry();
        try {
            Score score = scoreRepository.getById(scoreId);
            if (score == null) {
                throw new MedITException("Score not found for ID = " + scoreId);
            }
          //  Utils.assertUserHasAccessToLearner(score.getAuthUser().getUtorId());
            file = new LearnerFile();
//            if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
//                file.setFileName(PseudoFileUtil.PSEUDO_OSCE_FILE_NAME);
//                file.setInputStream(PseudoFileUtil.getOscePseudoFile());
//            } else {

            System.out.println("score.getReference()>>"+score.getReference());
            OscePdf oscePdf = oscePdfRepository.getById(score.getReference());
                if (oscePdf == null) {
                    throw new MedITException("Pdf not found for file name = " + score.getReference());
                }
                file.setFileName(score.getReference());
                file.setInputStream(oscePdf.getOsceData().getBinaryStream());
      //      }
        } catch (Exception dde) {
            throw new MedITException(dde.getMessage(), dde);
        }
        log.exit();
        return file;
    }


}
