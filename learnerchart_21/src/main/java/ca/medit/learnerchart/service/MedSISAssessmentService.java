package ca.medit.learnerchart.service;

import ca.medit.learnerchart.dto.EvaluationReport;
import ca.medit.learnerchart.dto.StudentReport;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.User;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface MedSISAssessmentService {

    void saveStudentUsers(InputStream inputStream) throws IOException;

    /**
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    StudentReport convertToStudentReport(InputStream inputStream) throws IOException;

    /**
     *
     * @param report
     * @return
     */
    List<User> convertToStudentUsers(StudentReport report);

    void saveAssessments(InputStream inputStream) throws IOException;

    /**
     *
     * @param inputStream
     * @return
     */
    EvaluationReport convertToEvaluationReport(InputStream inputStream) throws IOException;

    /**
     *
     * @param report
     * @return
     */
    List<Assessment> convertToAssessments(EvaluationReport report);


    void saveStudentImages(InputStream inputStream) throws IOException;

}
