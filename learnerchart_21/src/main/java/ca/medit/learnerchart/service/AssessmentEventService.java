package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.AssessmentEvent;

import java.util.List;

public interface AssessmentEventService {

    void deleteByRemoteId(FeedSourceTypeEnum source, long remoteId);

    List<AssessmentEvent> createAssessmentEvents(Assessment assessment);

}
