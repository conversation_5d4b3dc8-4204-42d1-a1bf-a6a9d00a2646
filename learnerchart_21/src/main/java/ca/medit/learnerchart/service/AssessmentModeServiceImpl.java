package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.AssessmentModeAttribute;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AssessmentModeServiceImpl implements AssessmentModeService {

    @Override
    public JsonResponse getAssessmentModes() {
        JsonResponse json = new JsonResponse().setData(getActivityTypeList());
        return json;
    }

    @Override
    public JsonResponse getAssessmentMode(String key) {
        JsonResponse json = new JsonResponse().setData(getAssessmentModeJsonType(getModeForKey(key)));
        return json;
    }

    @Override
    public AssessmentMode getModeForKey(String key) {
        if(key == null) {
            throw new MedITException("Assessment mode must not be null");
        }
        AssessmentMode mode = null;
        try {
            mode = AssessmentMode.valueOf(key);
        } catch (IllegalArgumentException e) {
            throw new MedITException("Invalid assessment mode: " + key);
        }
        return mode;
    }


    private List<JsonType> getActivityTypeList() {
        // TODO get this from the DAO layer
        List<JsonType> jsonTypes = new ArrayList<>();
        for (AssessmentMode mode : AssessmentMode.values()) {
            JsonType jsonType = getAssessmentModeJsonType(mode);
            jsonTypes.add(jsonType);
        }
        return jsonTypes;
    }

    private JsonType getAssessmentModeJsonType(AssessmentMode mode) {
        // TODO get this from the DAO layer
        JsonType jsonType = new JsonType(mode.name(), JsonType.TYPE_ASSESSMENT_MODE);
        jsonType.setAttributes(new AssessmentModeAttribute().setLabel(mode.getLabel())
                .setLabelPlural(mode.getLabelPlural()).setHelpText(mode.getHelpText()));
        return jsonType;
    }

}
