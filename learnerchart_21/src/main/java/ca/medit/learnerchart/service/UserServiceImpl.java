package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.entity.UserImage;
import ca.medit.learnerchart.repository.UserImageRepository;
import ca.medit.learnerchart.repository.UserRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import ca.medit.learnerchart.security.CustomUserDetails;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class UserServiceImpl implements UserService{
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserImageRepository userImageRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public User saveStudent(User authUser) {
        User student = createOrGetStudent(authUser);
        updateStudentFromOther(student, authUser);
        try {
            student.setFailedLoginAttempts(0);
            student.setOptlock(1L);
            student.setPassword("$2a$12$rPdd6RIowNpxpJFMVnSaJ.V3ZY/3ALIpvHj8sPokYzOo44MYjnGJe");
            student = userRepository.save(student);
        } catch (Exception e) {
            e.printStackTrace();
           // throw new MedITException(e.getMessage(), e);
        }
        return student;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    private User createOrGetStudent(User authUser) {
        User student = null;
        String utorId = getUtorIdFromAuthUser(authUser);
        try {
            student = userRepository.findByUsername(utorId)
                    .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + utorId));
        } catch (Exception e) {
            e.printStackTrace();
          //  throw new MedITException(e.getMessage(), e);
        }
        if (student == null) {
            student = createStudentFromAuthUser(authUser);
        }
        return student;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    private User createStudentFromAuthUser(User student) {
        User created = null;

        try {
            log.info("Creating student with username: {}", student.getUsername());
         //   student.getGroups().forEach(group -> group.addUser(student)); // Ensure bidirectional consistency

            student.setFailedLoginAttempts(0);
            student.setOptlock(1L);
            student.setPassword("$2a$12$rPdd6RIowNpxpJFMVnSaJ.V3ZY/3ALIpvHj8sPokYzOo44MYjnGJe");
            created = userRepository.save(student);
        } catch (Exception e) {
            e.printStackTrace();
           // throw new MedITException("Unable to create student: " + student, e);
        }
        return created;
    }

    private String getUtorIdFromAuthUser(User authUser) {
        if (authUser == null) {
            throw new MedITException("User to create must not be null");
        }
        String utorId = null;
        utorId = authUser.getUtorid();
        if (utorId == null || utorId.isEmpty()) {
            // try the other field
            utorId = authUser.getUsername();
        }
        if (utorId == null || utorId.isEmpty()) {
            throw new MedITException("User to create must have a UTORID");
        }
        return utorId;
    }

    private void updateStudentFromOther(User student, User authUser) {
        if (student == null) {
            throw new MedITException("Attempt to update a null Learner");
        }
        if (authUser == null) {
            throw new MedITException("Attempt to update a Learner with null data");
        }
        student.setFirstName(authUser.getFirstName());
        student.setLastName(authUser.getLastName());
        student.setEmail(authUser.getEmail());
        student.setCohortName(authUser.getCohortName());
      //  student.setPictureName(authUser.getPictureName());
        student.setStudentNumber(authUser.getStudentNumber());
        student.setYearOfStudy(authUser.getYearOfStudy());
    }

    @Override
    public JsonResponse getCurrentUser() {
        log.entry();
        JsonResponse json = new JsonResponse();

      //  LocalDateTime lastLogin = userSessionSvc.getLastLogin();

        try {

            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails userdetails) {
                User user = userRepository.findByUsername(userdetails.getUsername())
                        .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + userdetails.getUsername()));
                JsonType type = new JsonType(user.getUsersId(), JsonType.TYPE_USER);
                LCUserAttribute attrs = new LCUserAttribute().setFirstName(user.getFirstName()).setLastName(user.getLastName())
                        .setEmail(user.getEmail()).setUtorid(user.getUsername());
                attrs.setCanAddFocusedLearningPlan(false);
                attrs.setCanAddSupportingDocument(true);
               // if (Utils.isUserInRole(Authority.ROLE_MODIFY_SCHOLAR_NOTES) &&  !Utils.isUserInRole(Authority.ROLE_ADMIN)) {
                    attrs.setCanAddSupportingDocument(false);
                //}
                //attrs.setLastLogin(user.get);
              //  attrs.setAlerts(TransformerAlertJson.toAlertJson(authUserDAO.getNotificationsForUser(u.getId())));
                type.setAttributes(attrs);
            //    type.setRelationships(getRelationshipsForCurrentUser());
                json.setData(type);

            }
        } catch (RuntimeException re) {
            throw new MedITException("Error getting current user data for login session", re);
        }

        log.exit();
        return json;
    }


    @Override
    @Transactional(readOnly = true)
    public UserImage getLearnerPicture(String learnerId) {
        UserImage image = null;
        try {
            User learner = findUserByUsername(learnerId);
            if (learner == null) {
                throw new MedITException("Learner not found. ID = " + learnerId);
            }
//            if (!Utils.isAuthorizedToViewLearnerChart(learner)) {
//                throw new AccessDeniedException("The user does not have access.");
//            }

            String pictureName = learner.getPictureName();
            if (pictureName != null && !pictureName.isEmpty()) {
                Optional<UserImage> userImageOptional = Optional.empty();
                userImageOptional = userImageRepository.findByImageName(pictureName);
                if (userImageOptional.isPresent()) {
                    image = userImageOptional.get();
                }else {
                    log.info("Profile Image cannot be found for user's UTOR ID: " + learnerId);
                }
            }else{
                log.info("Profile Image's Name cannot be found for user's UTOR ID: " + learnerId);
            }
        } catch (Exception e) {
            throw new MedITException("Error getting learner picture. ID = " + learnerId, e);
        }
        return image;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean checkUserAlreadyExists(String username) {
        Optional<User> user = userRepository.findByUsername(username);
        return user.isPresent();
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public User getUser(Long userId) {
        return userRepository.getById(userId);
    }

    public User findUserByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));
        return user;
    }


    public User updateUser(User user) {
        return userRepository.save(user);
    }

    public void deleteUser(User user) {
        userRepository.delete(user);
    }

    public User createUser(User user) {
        return userRepository.save(user);
    }

//    private Relationship getRelationshipsForCurrentUser() {
//        UserRelationship r = new UserRelationship();
//        JsonResponse groups = new JsonResponse();
//        List<JsonType> groupList = new ArrayList<>();
//        for(Group g: Utils.getAuthUser().getGroups()) {
//            JsonType data = new JsonType(g.getId(), JsonType.TYPE_GROUP);
//            GroupItemAttribute attr = new GroupItemAttribute();
//            attr.setName(g.getName());
//            data.setAttributes(attr);
//            groupList.add(data);
//        }
//        groups.setData(groupList);
//        r.setGroups(groups);
//        return r;
//    }
    // Other business logic methods can be added here
}

