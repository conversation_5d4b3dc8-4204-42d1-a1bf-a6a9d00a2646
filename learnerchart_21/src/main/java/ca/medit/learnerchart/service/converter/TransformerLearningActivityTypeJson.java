package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.domain.LearningActivityTypeAttribute;
import ca.medit.learnerchart.domain.LearningActivityTypeEnum;

import java.util.ArrayList;
import java.util.List;

public class TransformerLearningActivityTypeJson {


    public static JsonResponse toLearningActivityTypeJson() {
        JsonResponse json = new JsonResponse();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        for (LearningActivityTypeEnum activityType : LearningActivityTypeEnum.values()) {
            jsonTypes.add(toLearningActivityTypeJsonType(activityType));
        }
        json.setData(jsonTypes);
        return json;
    }

    public static JsonResponse toLearningActivityTypeJson(LearningActivityTypeEnum activityType) {
        JsonResponse json = new JsonResponse();
        json.setData(toLearningActivityTypeJsonType(activityType));
        return json;
    }

    private static JsonType toLearningActivityTypeJsonType(LearningActivityTypeEnum activityType) {
        JsonType jsonType = new JsonType(activityType.name(), JsonType.TYPE_LEARNING_ACTIVITY_TYPES);
        LearningActivityTypeAttribute attributes = new LearningActivityTypeAttribute();
        attributes.setLabel(activityType.getLabel());
        jsonType.setAttributes(attributes);
        return jsonType;
    }


}
