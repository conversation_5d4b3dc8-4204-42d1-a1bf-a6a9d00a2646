package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.entity.CplanXid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class CplanXidServiceImpl implements CplanXidService{


    @Autowired
    private CanMedsRoleService canMedsRoleService;
    @Override
    @Transactional(readOnly = true)
    public List<CplanXid> getCanMedsRolesCplanXids() {
        List<CplanXid> cplanXids = new ArrayList<CplanXid>();
        try {
            List<CanMedsMapItem> canMedsMapItems = canMedsRoleService.getCanMedsRolesItems();
            for (CanMedsMapItem canMedsMapItem : canMedsMapItems) {
                CplanXid cplanXid = new CplanXid();
                cplanXid.setCplanXidId(canMedsMapItem.getParentCplanXid());
                cplanXid.setLabel(canMedsMapItem.getParentLabel());
                cplanXid.setValue(canMedsMapItem.getParentValue());
                cplanXids.add(cplanXid);
            }
        } catch (MedITException e) {
            throw new MedITException("Unable to get CanMedsRoles CplanXids", e);
        }
        return cplanXids;
    }
}
