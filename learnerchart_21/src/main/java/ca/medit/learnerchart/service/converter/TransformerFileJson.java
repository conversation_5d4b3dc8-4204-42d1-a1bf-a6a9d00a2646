package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.FileAttribute;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.entity.File;

import java.time.LocalDateTime;

public abstract class TransformerFileJson {
    protected boolean isLearner;
    protected File file;
    protected Long containerId;

    public TransformerFileJson() {
    }

    public TransformerFileJson(Long containerId, Long fileId, String fileName, Long fileSize, LocalDateTime createdOn) {
        this.containerId = containerId;
        this.file = new File();
        this.file.setFileId(fileId);
        this.file.setFileName(fileName);
        this.file.setFileSize(fileSize);
        this.file.setCreatedon(createdOn);
    }

    public void setContainerId(Long containerId) {
        this.containerId = containerId;
    }

    public TransformerFileJson setLearner(boolean isLearner) {
        this.isLearner = isLearner;
        return this;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public abstract JsonType toFileJsonType();

    protected FileAttribute getFileAttribute() {
        FileAttribute attributes = new FileAttribute();
        if (file.getCreatedon() != null) {
            attributes.setDateCreated(file.getCreatedon());
        }
        attributes.setFileName(file.getFileName());
        attributes.setFileSize(file.getFileSize());
        attributes.setFileType(file.getFileExtension());
        return attributes;
    }

    public JsonResponse toFileJson() {
        JsonResponse json = new JsonResponse();
        json.setData(toFileJsonType());
        return json;
    }
}

