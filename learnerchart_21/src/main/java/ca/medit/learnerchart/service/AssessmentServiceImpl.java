package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.repository.AssessmentRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;

@Service
@Log4j2
public class AssessmentServiceImpl implements AssessmentService{

    @Autowired
    private AssessmentRepository assessmentRepository;
    @Autowired
    private AssessmentEventService assessmentEventSvc;
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Assessment> getListOfAssessments() {

        log.entry();
        List<Assessment> assessments = new ArrayList<Assessment>();
        try {
            assessments = assessmentRepository.getListOfAssessments();
        } catch (Exception e) {
            throw new MedITException("Unable to get list of Assessments", e);
        }
        return assessments;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Assessment saveAssessment(Assessment assessment) {

        Assessment saved = null;

        try {
            if (assessment == null) {
                throw new MedITException("Cannot save null Assessment");
            }
            if (assessment.getRemoteId() == null) {
                throw new MedITException("Cannot save an Assessment without a remote ID");
            }
            if (assessment.getSource() == null) {
                throw new MedITException("Cannot save an Assessment without a Source");
            }
            Assessment existing = assessmentRepository.findByRemoteIdAndSource(assessment.getRemoteId(), assessment.getSource());
            if (existing != null) {
                assessmentRepository.deleteAuthUserScore(existing.getRemoteId(), existing.getSource().name());
                assessmentRepository.deleteScores(existing.getRemoteId(), existing.getSource().name());
               // assessmentRepository.deleteAssessment(remoteId, sourceString);

                assessmentRepository.deleteAssessment(existing.getRemoteId(), existing.getSource().name());
            }
            if (!assessment.getScores().isEmpty()) {
                saved = saveNewAssessment(assessment);
                if (existing == null && assessment.getSource() == FeedSourceTypeEnum.MEDSIS) {
                    assessmentEventSvc.createAssessmentEvents(saved);
                }
            } else {
                assessmentEventSvc.deleteByRemoteId(assessment.getSource(), assessment.getRemoteId());
            }
        } catch (Exception e) {
            log.catching(e);
            e.printStackTrace();
            String message = "Unknown exception";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        }
        return saved;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Assessment> getMedsisAssessmentsToRemove(List<Assessment> assessments, int programYear) {
        List<Assessment> assessmentsToRemove = new ArrayList<Assessment>();
        try {
           // Map<Long, Long> map = assessmentRepository.getMedsisAssessmentIdsMap();
            List<Object[]> result = assessmentRepository.getMedsisAssessmentIdsMap();
            Map<Long, Long> map = new HashMap<>();
            for (Object[] row : result) {
                map.put(((BigInteger) row[0]).longValue(), ((BigInteger) row[1]).longValue());
            }
            for (Assessment assessment : assessments) {
                map.remove(assessment.getRemoteId());
            }
            for (Iterator<Map.Entry<Long, Long>> i = map.entrySet().iterator(); i.hasNext(); ) {
                Map.Entry<Long, Long> entry = i.next();
                Assessment assessmentToRemove = new Assessment();
                assessmentToRemove.setAssessmentId(entry.getValue());
                assessmentToRemove.setRemoteId(entry.getKey());
                assessmentToRemove.setSource(FeedSourceTypeEnum.MEDSIS);
                assessmentsToRemove.add(assessmentToRemove);
            }
        } catch (Exception e) {
            throw new MedITException(e.getMessage(), e);
        }
        return assessmentsToRemove;
    }

    private Assessment saveNewAssessment(Assessment assessment) {
        Assessment saved = null;

        try {
         //   assessment.setUpdatedon(LocalDateTime.now());
            saved = assessmentRepository.save(assessment);
        } catch (Exception e) {
            e.printStackTrace();
            throw new MedITException(
                    "Cannot save the Assessment: " + assessment.getRemoteId() + " (" + assessment.getSource() + ")", e);
        }

        return saved;
    }
}
