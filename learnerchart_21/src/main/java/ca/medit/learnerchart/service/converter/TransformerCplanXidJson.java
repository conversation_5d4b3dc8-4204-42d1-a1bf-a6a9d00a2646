package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.CplanXidAttribute;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.entity.CplanXid;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class TransformerCplanXidJson {


    public static JsonType toCplanXidJsonType(CplanXid cplanXid) {
        JsonType jsonType = new JsonType(cplanXid.getValue(), JsonType.TYPE_CPLAN_XIDS);
        CplanXidAttribute attribute = new CplanXidAttribute();
        attribute.setLabel(cplanXid.getLabel());
        jsonType.setAttributes(attribute);
        return jsonType;
    }

    public static JsonResponse toCplanXidJson(Set<CplanXid> cplanXids) {
        if (cplanXids == null || cplanXids.size() == 0) return null;
        return toCplanXidJson(new ArrayList<CplanXid>(cplanXids));
    }

    public static JsonResponse toCplanXidJson(CplanXid cplanXid) {
        if (cplanXid == null) return null;
        List<CplanXid> cplanXids = new ArrayList<CplanXid>();
        cplanXids.add(cplanXid);
        return toCplanXidJson(cplanXids);
    }

    public static JsonResponse toCplanXidJson(List<CplanXid> cplanXids) {
        JsonResponse json = new JsonResponse();
        if (cplanXids != null) {
            List<JsonType> list = new ArrayList<JsonType>();
            for (CplanXid cplanXid : cplanXids) {
                list.add(toCplanXidJsonType(cplanXid));
            }
            json.setData(list);
        }
        return json;
    }


}
