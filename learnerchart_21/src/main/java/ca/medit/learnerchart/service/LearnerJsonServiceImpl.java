package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.dto.*;
import ca.medit.learnerchart.entity.Group;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.repository.UserRepository;
import ca.medit.learnerchart.service.converter.TransformerAlertJson;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Log4j2
@Service
public class LearnerJsonServiceImpl implements LearnerJsonService{

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private UserRepository userRepository;

    @Override
    @Transactional
    public JsonResponse getLearners() {
        log.entry();
        JsonResponse json = new JsonResponse();
        List<LearnerDTO> learners = new ArrayList<>();

        try {
            learners = getAllLearners();
        } catch (Exception e) {
            throw new MedITException(e.getMessage(), e);
        }

        List<JsonType> data = new ArrayList<>();
        for (LearnerDTO u : learners) {
            JsonType type = getLearnerTypeForAuthUser(u);
            data.add(type);
        }
        json.setData(data);

        log.exit();
        return json;
    }

    @Transactional
    private JsonType getLearnerTypeForAuthUser(LearnerDTO dto) {
        JsonType jsonType = new JsonTypeWithLinks(dto.getId(), JsonType.TYPE_LEARNER);
        jsonType.setAttributes(createLearnerAttributes(dto));
        jsonType.setRelationships(createRxpsForLearner(dto));
        return jsonType;
    }


    public List<LearnerDTO> getAllLearners() {
//        if(Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
//            //authUserService.managePseudoData();
//            return getAllPseudoLearners();
//        }
        List<LearnerDTO> learners = Collections.emptyList();

//        if (!Utils.isUserInRole(Authority.ROLE_ADMIN) && getVisibleUtorIdsForContext().isEmpty()) {
//            // nothing to see here!
//            return learners;
//        }

        StringBuilder sb = new StringBuilder("select ");
        sb.append(" u.users_id as u_users_id, ").append("u.username as u_username, ")
                .append(" u.first_name as u_fname, ").append(" u.last_name as u_lname, ").append(" u.email as u_email, ")
                .append(" u.cohort_name, ").append(" u.picture_name, ");

        sb.append(" g.GROUP_ID, g.NAME, ");
        sb.append(" sc.users_id as sc_users_id, ").append(" sc.username as sc_uname, ")
                .append(" sc.first_name as sc_fname, ").append(" sc.last_name as sc_lname, ")
                .append(" sc.email as sc_email, ");
        sb.append(" su.users_id as su_users_id, ").append(" su.username as su_uname, ")
                .append(" su.first_name as su_fname, ").append(" su.last_name as su_lname, ")
                .append(" su.email as su_email ");
        sb.append(" from USERS u ");
        sb.append(" join GROUP_MEMBERS gm on u.users_id=gm.USERS_ID ");
        sb.append(" join GROUPS g on gm.GROUP_ID=g.GROUP_ID ");
        sb.append(" left join AUTHUSER_SCHOLAR_FOR aschol on u.users_id=aschol.LEARNERCHART_USERS_ID ");
        sb.append(" left join USERS sc on aschol.USERS_ID=sc.users_id ");
        sb.append(" left join AUTHUSER_SUPPORTER_FOR asup on u.users_id=asup.LEARNERCHART_USERS_ID ");
        sb.append(" left join USERS su on asup.USERS_ID=su.users_id ");
        sb.append(" where g.name = :group and u.COHORT_NAME in (select cohort_name from cohort)");

//        if (!Utils.isUserInRole(Authority.ROLE_ADMIN)) {
//            // we will get the visible learners below
//            sb.append(" and u.username in (:permitted) ");
//        }

        try {
            Query q = entityManager.createNativeQuery(sb.toString());
            q.setParameter("group", Group.LEARNER);
//            if (!Utils.isUserInRole(Authority.ROLE_ADMIN)) {
//                q.setParameter("permitted", getVisibleUtorIdsForContext());
//            }

            @SuppressWarnings("unchecked")
            List<Object[]> rows = q.getResultList();
            if (rows != null && !rows.isEmpty()) {
                LearnerDTOMap wrapper = new LearnerDTOMap();
                for (Object[] row : rows) {
                    LearnerDTO dto = wrapper.addRow(row);
                    dto.setAlerts(getNotificationsForUser(dto.getUser().getId()));
                }
                learners = wrapper.getLearners();
            }
        } catch (RuntimeException re) {
            String message = "Error when searching for all Students";
            if (re.getMessage() != null && !re.getMessage().isEmpty()) {
                message = re.getMessage();
            }
            throw new MedITException(message, re);
        }
        log.exit();
        return learners;
    }

    private LearnerAttribute createLearnerAttributes(LearnerDTO dto) {
        UserDTO learner = dto.getUser();
        LearnerAttribute attrs = new LearnerAttribute().setFirstName(learner.getFirstName())
                .setLastName(learner.getLastName()).setEmail(learner.getEmail()).setUtorid(learner.getUtorId())
                .setCohortCode(dto.getCohortName())
                .setImageLink((dto.getPictureName() == null || dto.getPictureName().isEmpty())
                        ? null : "/appl/learners/" + learner.getUtorId() + "/image")
                .setAlerts(createAlertRxpsForLearner(dto));
        return attrs;
    }

    private Relationship createRxpsForLearner(LearnerDTO dto) {
        LearnerRelationship rel = new LearnerRelationship();
        rel.setAssignedScholars(createJsonResponseFromAuthUsers(dto.getScholars()));
        rel.setGroups(createGroupRxpsForLearner(dto));
        rel.setLearnerSupporters(createJsonResponseFromAuthUsers(dto.getLearnerSupporters()));
        return rel;
    }

    private JsonResponse createAlertRxpsForLearner(LearnerDTO learner) {
        return TransformerAlertJson.toAlertJson(learner.getAlerts());
    }

    private JsonResponse createJsonResponseFromAuthUsers(List<UserDTO> authUsers) {
        JsonResponse json = new JsonResponse();
        List<JsonType> types = new ArrayList<>();
        if (authUsers != null) {
            for (UserDTO authUser : authUsers) {
                JsonType userType = new JsonType(authUser.getUtorId(), JsonType.TYPE_USER);
                UserAttribute attributes = new UserAttribute();
                attributes.setFirstName(authUser.getFirstName());
                attributes.setLastName(authUser.getLastName());
                attributes.setEmail(authUser.getEmail());
                attributes.setUtorid(authUser.getUtorId());
                userType.setAttributes(attributes);
                types.add(userType);
            }
        }
        json.setData(types);
        return json;
    }

    private JsonResponse createGroupRxpsForLearner(LearnerDTO learner) {
        JsonResponse groups = new JsonResponse();
        List<JsonType> groupList = new ArrayList<>();
        for (GroupDTO g : learner.getGroups()) {
            JsonType data = new JsonType(g.getId(), JsonType.TYPE_GROUP);
            GroupItemAttribute attr = new GroupItemAttribute();
            attr.setName(g.getName());
            data.setAttributes(attr);
            groupList.add(data);
        }
        groups.setData(groupList);
        return groups;
    }

    public List<AlertDTO> getNotificationsForUser(long userId) {
        List<AlertDTO> alerts = new ArrayList<AlertDTO>();
        try {
            String sql = "SELECT alert_id, reference_id, type, title, assessment_mode, alert_type, alert_date, year_Id " +
                    "FROM alert WHERE deleted <> 1 AND alert_date >= dateadd(day, -30, CURRENT_TIMESTAMP) AND users_id = :userId";
            @SuppressWarnings("unchecked")
            List<Object[]> rows = entityManager.createNativeQuery(sql).setParameter("userId", userId).getResultList();
            for (Object[] row : rows) {
                AlertDTO alert = new AlertDTO();
                alert.setId(((BigInteger) row[0]).longValue());
                if (row[1] != null) {
                    alert.setReferenceId(((BigInteger) row[1]).longValue());
                }
                alert.setType((String) row[2]);
                alert.setTitle((String) row[3]);
                if (row[4] != null) {
                    alert.setAssessmentMode(AssessmentMode.valueOf((String) row[4]));
                }
                alert.setAlertType(AlertType.valueOf((String) row[5]));
                alert.setDateTime(convertToLocalDateTime(row[6]));
                alert.setYearId((Integer) row[7]);
                alerts.add(alert);
            }
        } catch (RuntimeException re) {
            String message = "Error when getting alerts for user " + userId;
            if (re.getMessage() != null && !re.getMessage().isEmpty()) {
                message = re.getMessage();
            }
            throw new MedITException(message, re);
        }
        return alerts;
    }

    public static LocalDateTime convertToLocalDateTime(Object o) {
        LocalDateTime localDateTime = null;
        if (o instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) o;
        } else if (o instanceof Timestamp) {
            localDateTime = new LocalDateTime(o);
        } else if (o instanceof String) {
            try {
                localDateTime = new LocalDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse((String)o));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return localDateTime;
    }

    @Override
    public JsonResponse getLearnerAlerts(String utorId){
        log.entry();
        JsonResponse json = new JsonResponse();
        try {
//        if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
//            utorId = userSvc.getUtoridForPseudoUtorid(utorId);
//        }

            User user = userRepository.findByUtorid(utorId)
                    .orElseThrow(() -> new UsernameNotFoundException("User not found with UtorId: " + utorId));


            List<AlertDTO> alerts = getNotificationsForUser(user.getUsersId());

            json = TransformerAlertJson.toAlertJson(alerts);

        } catch (Exception e) {
            String message = "Error when getting alerts for user with UTOR ID " + utorId;
            if (e.getMessage() != null && !e.getMessage().isEmpty()) {
                message = e.getMessage();
                log.error(message+""+e.getMessage());
            }
            throw new MedITException(message, e);
        }
        log.exit();
        return json;
    }
    @Override
    public JsonResponse getLearnerYearList(String utorId){
        return null;
    }
}
