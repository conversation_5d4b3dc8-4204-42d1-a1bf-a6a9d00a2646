package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.FocusedLearningPlanAttribute;
import ca.medit.learnerchart.domain.FocusedLearningPlanRelationship;
import ca.medit.learnerchart.domain.JsonResponseWithIncluded;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.entity.FocusedLearningPlan;

import java.util.ArrayList;
import java.util.List;

public class TransformerFocusedLearningPlanJson {


    private static JsonType toFocusedLearningPlanJsonType(FocusedLearningPlan focusedLearningPlan, boolean hasAdminRole) {
        JsonType jsonType = new JsonType(focusedLearningPlan.getFocusedLearningPlanId(), JsonType.TYPE_FOCUSED_LEARNING_PLANS);
        FocusedLearningPlanAttribute attributes = new FocusedLearningPlanAttribute();
        attributes.setHasGearOption(hasAdminRole);
        attributes.setHasDeleteOption(hasAdminRole);
        attributes.setYear(TransformerYearJson.toYearJson(focusedLearningPlan.getAcademicYear(), focusedLearningPlan.getProgramYear()));
        attributes.setActivityType(TransformerLearningActivityTypeJson.toLearningActivityTypeJson(focusedLearningPlan.getActivityType()));
        jsonType.setAttributes(attributes);
        FocusedLearningPlanRelationship relationships = new FocusedLearningPlanRelationship();
        TransformerFileJsonFLN transformer = new TransformerFileJsonFLN(focusedLearningPlan);
        relationships.setFile(transformer.toFileJson());
        //relationships.setCplanXids(TransformerCplanXidJson.toCplanXidJson(focusedLearningPlan.getCplanXids()));
        jsonType.setRelationships(relationships);
        return jsonType;
    }

    public static JsonResponseWithIncluded toFocusedLearningPlanJson(List<FocusedLearningPlan> focusedLearningPlans, boolean hasAdminRole) {
        JsonResponseWithIncluded json = new JsonResponseWithIncluded();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        for (FocusedLearningPlan focusedLearningPlan : focusedLearningPlans) {
            jsonTypes.add(TransformerFocusedLearningPlanJson.toFocusedLearningPlanJsonType(focusedLearningPlan, hasAdminRole));
        }
        json.setData(jsonTypes);
        return json;
    }


}
