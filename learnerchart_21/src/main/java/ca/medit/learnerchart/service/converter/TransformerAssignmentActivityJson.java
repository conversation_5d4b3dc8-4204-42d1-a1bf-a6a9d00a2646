package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.dto.LearnerActivity;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.entity.CplanXid;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.service.LearnerChartFilter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

public class TransformerAssignmentActivityJson extends TransformerActivityJson{


    public TransformerAssignmentActivityJson(LearnerChartFilter filter) {
        super(filter);
    }

    public JsonType toAssignmentActivitySkeletonJsonType(Assessment assessment, List<Score> scores, List<CanMedsMapItem> canMedsRoles) {

        JsonTypeWithIncluded data = new JsonTypeWithIncluded(assessment.getAssessmentId(), JsonType.TYPE_ACTIVITY);

        ActivityAttribute attributes = new ActivityAttribute();
        attributes.setName(assessment.getName());
        attributes.setDate(new org.joda.time.LocalDate((assessment.getDueDate().toLocalDate())));
        attributes.setYear(TransformerYearJson.toYearJson(assessment.getAcademicYear(), assessment.getProgramYear()));
        List<String> grades = new ArrayList<String>();
        for(Score s: scores) {
            grades.add(s.getRawValue());
        }
        attributes.setFinalScore(TransformerActivityScoreJson.toActivityScoreJson(grades));
        data.setAttributes(attributes);

        ActivityRelationshipWithFeedbacks relationships = new ActivityRelationshipWithFeedbacks();
        relationships.setActivityType(TransformerActivityTypeJson.toActivityTypeJson(assessment.getAssessmentType()));
        data.setRelationships(relationships);

        ActivityIncluded included = new ActivityIncluded();
        included.setScores(TransformerActivityScoreJson.toPassFailActivityScoreJson(ActivityScoreEnum.class));
        data.setIncluded(included);

        Map<Long, List<String>> submissionFileMap = parseScores(scores, data);
        relationships.setFiles(TransformerActivityFileJson.toActivityFileJson(submissionFileMap, canMedsRoles));

        return data;
    }

    private Map<Long, List<String>> parseScores(List<Score> scores, JsonType data) {
        Map<Long, List<String>> submissionFileMap = new HashMap<Long, List<String>>();
        for (Score score : scores) {
            if (!CplanXid.FINAL_SCORE_XID.equals(score.getCplanXid())) {
                List<String> xids = submissionFileMap.get(Long.parseLong(score.getReference()));
                if (xids == null) {
                    xids = new ArrayList<String>();
                    submissionFileMap.put(Long.parseLong(score.getReference()), xids);
                }
                xids.add(score.getCplanXid());
            }

        }
        return submissionFileMap;
    }

    public JsonType toAssignmentActivityJsonType(LearnerActivity activity) {
        JsonTypeWithIncluded data = new JsonTypeWithIncluded(activity.getAssessmentId(), JsonType.TYPE_ACTIVITY);

        ActivityAttribute attributes = new ActivityAttribute();
        attributes.setName(activity.getAssessmentName());
        attributes.setDate(activity.getDate().toLocalDate());
        attributes.setYear(TransformerYearJson.toYearJson(activity.getAcademicYear(), activity.getProgramYear()));
        attributes.setFinalScore(TransformerActivityScoreJson.toActivityScoreJson(activity.getRawScore()));
        attributes.setQuestionCount(activity.getQuestionCount());
        attributes.setCourseCode(activity.getCourseCode());
        attributes.setSupervisorFirstName(activity.getSupervisorFirstName());
        attributes.setSupervisorLastName(activity.getSupervisorLastName());
        attributes.setLinks(TransformerLinkJson.toLinkJson(activity, filter));
        data.setAttributes(attributes);

        ActivityRelationship relationships = new ActivityRelationship();
        relationships.setActivityType(TransformerActivityTypeJson.toActivityTypeJson(activity.getAssessmentType()));
        data.setRelationships(relationships);

        ActivityIncluded included = new ActivityIncluded();
        included.setScores(TransformerActivityScoreJson.toPassFailActivityScoreJson(ActivityScoreEnum.class));
        data.setIncluded(included);

        return data;
    }


}
