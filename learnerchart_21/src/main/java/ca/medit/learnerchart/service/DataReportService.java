package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.DataReport;

import java.util.List;

public interface DataReportService {
    List<DataReport> getReportsByYearAndType(Integer year, String dataType);
    List<DataReport> getReportsByProgramYearAndType(Integer year, Integer programYr, String dataType);
   // List<DataReport> getAdvancedReports(Integer year, String dataType, String category, String status);
}
