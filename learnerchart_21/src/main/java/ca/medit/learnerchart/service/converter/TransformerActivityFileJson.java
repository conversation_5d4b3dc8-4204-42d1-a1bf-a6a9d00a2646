package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.entity.CanMedsMapItem;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class TransformerActivityFileJson {


    private final List<CanMedsMapItem> canMedsRoles;

    private TransformerActivityFileJson(List<CanMedsMapItem> canMedsRoles) {
        this.canMedsRoles = canMedsRoles;
    }

    private JsonType toActivityFileJsonType(Map.Entry<Long, List<String>> submissionFile) {

        JsonTypeWithLinks jsonType = new JsonTypeWithLinks(submissionFile.getKey(), JsonType.TYPE_ACTIVITY_FILE);

        ActivityFileAttribute attributes = new ActivityFileAttribute();
        List<String> xids = submissionFile.getValue();
        List<CanMedsMapItem> canMedsRoleItems = new ArrayList<CanMedsMapItem>();
        for (String xid : xids) {
            for (CanMedsMapItem canMedsRole : canMedsRoles) {
                if (canMedsRole.getChildValue().equalsIgnoreCase(xid)) {
                    canMedsRoleItems.add(canMedsRole);
                }
            }
        }
        attributes.setCanMedsRoles(TransformerCanMedsRole.toCanMedsRolesJson(canMedsRoleItems));
        jsonType.setAttributes(attributes);

        ActivityFileLinks links = new ActivityFileLinks();
        links.setSelf("/appl/submission/download/" + submissionFile.getKey());
        jsonType.setLinks(links);

        return jsonType;
    }

    public static JsonResponse toActivityFileJson(Map<Long, List<String>> submissionFileMap, List<CanMedsMapItem> canMedsRoles) {
        JsonResponse json = new JsonResponse();
        TransformerActivityFileJson transformer = new TransformerActivityFileJson(canMedsRoles);
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        for (Iterator<Map.Entry<Long, List<String>>> i = submissionFileMap.entrySet().iterator(); i.hasNext(); ) {
            Map.Entry<Long, List<String>> submissionFile = i.next();
            jsonTypes.add(transformer.toActivityFileJsonType(submissionFile));
        }
        json.setData(jsonTypes);
        return json;
    }


}
