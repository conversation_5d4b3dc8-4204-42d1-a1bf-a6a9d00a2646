package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.ActivityAverageScore;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.dto.AverageNumericScore;

public class TransformerAverageScoreJson {

    public static JsonResponse toAverageScoreJson(AverageNumericScore exercise) {
        if(exercise == null) {
            return null;
        }
        JsonResponse cohortAverage = new JsonResponse();

        ActivityAverageScore score = new ActivityAverageScore(
                exercise.getAverageScore());
        cohortAverage.setData(score);

        return cohortAverage;
    }


}
