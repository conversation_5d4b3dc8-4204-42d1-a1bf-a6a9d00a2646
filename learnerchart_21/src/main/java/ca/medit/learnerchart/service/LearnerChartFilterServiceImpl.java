package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class LearnerChartFilterServiceImpl  implements LearnerChartFilterService{


    @Value("${medsis.evaluation.url}")
    private String medsisUrl;

    @Value("${examsoft.url}")
    private String examsoftUrl;

    @Value("${elentra.assessment.url}")
    private String elentraUrl;

    @Autowired
    private AcademicYearService academicYearSvc;

    @Autowired
    private UserService userSvc;

    @Override
    public LearnerChartFilter getFilter(Integer yearId, String utorId, AssessmentMode mode) {
       // if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
            return getFilterForPseudoAdmin(yearId, utorId, mode);
//        }
//        return internalGetFilter(yearId, utorId, mode);
    }

    private LearnerChartFilter getFilterForPseudoAdmin(Integer yearId, String pseudoUtorId, AssessmentMode mode) {
       // String utorId = userSvc.getUtoridForPseudoUtorid(pseudoUtorId);
        LearnerChartFilter filter = internalGetFilter(yearId, pseudoUtorId, mode);
        filter.setMedsisUrl(medsisUrl);
        return filter;
    }

    private LearnerChartFilter internalGetFilter(Integer yearId, String utorId, AssessmentMode mode) {
        LearnerChartFilter filter;
        try {
            int academicYear, programYear;
            int digits = yearId.toString().length();
            if (digits == 1) {
                academicYear = academicYearSvc.getCurrentAcademicYear();
                programYear = yearId;
            } else if (digits == 5) {
                academicYear = Integer.parseInt(yearId.toString().substring(0, 4));
                programYear = Integer.parseInt(yearId.toString().substring(4));
            } else {
                throw new IllegalArgumentException("yearId has a wrong format");
            }
            filter = new LearnerChartFilter(academicYear, programYear, utorId, mode);
            filter.setMedsisUrl(medsisUrl);
            filter.setExamsoftUrl(examsoftUrl);
            filter.setElentraUrl(elentraUrl);
            if (AssessmentMode.ASSIGNMENT == mode) {
                User learner = userSvc.findUserByUsername(utorId);
                filter.setLearnerId(learner.getUsersId());
            }
        } catch (IllegalArgumentException e) {
            throw new MedITException("Invalid filter value: " + e.getMessage(), e);
        }
        return filter;
    }
}
