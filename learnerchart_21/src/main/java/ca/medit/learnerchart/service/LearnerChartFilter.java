package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.AssessmentMode;

public class LearnerChartFilter {

    private int academicYear;
    private final int programYear;
    private long learnerId;
    private final AssessmentMode mode;
    private final String username;
    private String medsisUrl;
    private String examsoftUrl;
    private String elentraUrl;

    public LearnerChartFilter(int academicYear, int programYear,
                              String username, AssessmentMode mode) {
        if (programYear < 1 || programYear > 4) {
            throw new IllegalArgumentException("Program year must be between 1 and 4");
        }

        if (username == null || username.isEmpty()) {
            throw new IllegalArgumentException("Username is required.");
        }
        //Utils.assertUserHasAccessToLearner(username);
        this.academicYear = academicYear;
        this.programYear = programYear;
        this.username = username;
        this.mode = mode;
    }

    public int getAcademicYear() {
        return academicYear;
    }

    public void setAcademicYear(int academicYear) {
        this.academicYear = academicYear;
    }

    /**
     * @return the programYear
     */
    public int getProgramYear() {
        return programYear;
    }

    public long getLearnerId() {
        return learnerId;
    }

    public void setLearnerId(long learnerId) {
        this.learnerId = learnerId;
    }

    /**
     * @return the mode
     */
    public AssessmentMode getMode() {
        return mode;
    }


    /**
     * @return the username
     */
    public String getUsername() {
        return username;
    }

    public String getMedsisUrl() {
        return medsisUrl;
    }

    public void setMedsisUrl(String medsisUrl) {
        this.medsisUrl = medsisUrl;
    }

    public String getExamsoftUrl() {
        return examsoftUrl;
    }

    public void setExamsoftUrl(String examsoftUrl) {
        this.examsoftUrl = examsoftUrl;
    }

    public String getElentraUrl() {
        return elentraUrl;
    }

    public void setElentraUrl(String elentraUrl) {
        this.elentraUrl = elentraUrl;
    }

    @Override
    public String toString() {
        return "LearnerChartFilter [academicYear=" + academicYear + ", programYear=" + programYear + ", mode=" + mode
                + ", username=" + username + "]";
    }
}
