package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.domain.YearAttribute;
import ca.medit.learnerchart.dto.YearDTO;

import java.util.ArrayList;
import java.util.List;

public class TransformerYearJson {


    public static JsonType toYearJsonType(int academicYear, int programYear) {
        JsonType jsonType = new JsonType(academicYear + "" + programYear, JsonType.TYPE_YEAR);
        YearAttribute attributes = new YearAttribute();
        attributes.setAcademicYear(academicYear);
        attributes.setProgramYear(programYear);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    public static JsonResponse toYearJson(int academicYear, int programYear) {
        JsonResponse json = new JsonResponse();
        json.setData(toYearJsonType(academicYear, programYear));
        return json;
    }

    public static JsonResponse toYearJson(List<YearDTO> years) {
        JsonResponse json = new JsonResponse();
        List<JsonType> types = new ArrayList<JsonType>();
        for (YearDTO year : years) {
            types.add(toYearJsonType(year.getAcademicYear(), year.getProgramYear()));
        }
        json.setData(types);
        return json;
    }

    public static JsonResponse toYearJson(YearDTO year) {
        JsonResponse json = new JsonResponse();
        json.setData(toYearJsonType(year.getAcademicYear(), year.getProgramYear()));
        return json;
    }


}
