package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.dto.PseudoFeedback;
import ca.medit.learnerchart.dto.SubmissionFeedback;
import ca.medit.learnerchart.dto.SubmissionFileInfo;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
@Log4j2
public class OasesServiceImpl implements OasesService{

    @PersistenceContext
    private EntityManager entityManager;
    @Override
    public SubmissionFileInfo getSubmissionFileInfo(long submissionFileId) {
        log.entry();

        SubmissionFileInfo submissionFileInfo = null;
        try {
            String sql = "select submission_file_id, file_length, file_name, submission_file_date, submission_id from oases_submission_file where submission_file_id = :submissionFileId";
            Query query = entityManager.createNativeQuery(sql).setParameter("submissionFileId", submissionFileId);
            Object object = query.getSingleResult();
            Object[] results = (Object[]) object;
            submissionFileInfo = new SubmissionFileInfo();
            submissionFileInfo.setId(((BigInteger) results[0]).longValue());
            submissionFileInfo.setFileLength(((BigInteger) results[1]).longValue());
            submissionFileInfo.setFileName((String) results[2]);
            submissionFileInfo.setSubmissionDate(convertToLocalDateTime(results[3]));
            submissionFileInfo.setSubmissionId(((BigInteger) results[4]).longValue());

        } catch (NoResultException e) {
        } catch (RuntimeException re) {
            throw new MedITException("Failed to retrieve sumbission file. Submission file id = " + submissionFileId,
                    re);
        }

        log.exit();
        return submissionFileInfo;

    }
    @Override
    public List<SubmissionFeedback> getSubmissionFeedbacks(long submissionId) {

        log.entry();
        List<SubmissionFeedback> feedbacks = new ArrayList<SubmissionFeedback>();
        try {
            Query query = entityManager.createNativeQuery(
                    "select assessment_id, assessment_type, grade, feedback from oases_submission_feedback where submission_id = :submissionId");
            query.setParameter("submissionId", submissionId);
            List<Object[]> objects = query.getResultList();
            for (Object[] results : objects) {
                SubmissionFeedback feedback = new SubmissionFeedback();
                feedback.setAssessmentId(((BigInteger) results[0]).longValue());
                feedback.setAssessmentType((String) results[1]);
                feedback.setGrade((String) results[2]);
                feedback.setFeedback((String) results[3]);
                feedbacks.add(feedback);
            }
          //  if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
                for (int i = 0; i < feedbacks.size(); i++) {
                    SubmissionFeedback feedback = feedbacks.get(i);
                    feedback.setFeedback(PseudoFeedback.getPseudoFeedback(i));
                }
          //  }
        } catch (RuntimeException re) {
            throw new MedITException("Failed to retrieve feedback for submission id = " + submissionId, re);
        }
        log.exit();
        return feedbacks;

    }

    /**
     *
     * @param o
     * @return
     */
    public static LocalDateTime convertToLocalDateTime(Object o) {
        LocalDateTime localDateTime = null;
        if (o instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) o;
        } else if (o instanceof Timestamp) {
            localDateTime = new LocalDateTime(o);
        } else if (o instanceof String) {
            try {
                localDateTime = new LocalDateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").parse((String)o));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return localDateTime;
    }
}
