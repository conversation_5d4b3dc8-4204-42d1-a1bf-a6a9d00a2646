package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.domain.ProgramYearAttribute;
import ca.medit.learnerchart.dto.YearDTO;
import ca.medit.learnerchart.service.converter.TransformerYearJson;
import jakarta.persistence.PersistenceContext;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProgramYearServiceImpl implements ProgramYearService {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public JsonResponse getProgramYears() {
        JsonResponse json = null;

        json = new JsonResponse().setData(getAllProgramYearsAsJson());
        return json;
    }

    @Override
    public JsonResponse getProgramYear(Integer yearId) {
        JsonResponse json = new JsonResponse().setData(createYearJsonType(yearId));
        return json;
    }

    private List<JsonType> getAllProgramYearsAsJson() {
        List<JsonType> years = new ArrayList<>();

        for(int i = 1; i < 5; i++) {
            JsonType type = createYearJsonType(i);
            years.add(type);
        }
        return years;
    }

    private JsonType createYearJsonType(int i) {
        JsonType type = null;
        if(i < 1 || i > 4) {
            throw new MedITException("Invalid program year: " + i);
        }
        String label = "Year " + i;
        Integer id = i;
        type = new JsonType(id, JsonType.TYPE_PROGRAM_YEAR);
        type.setAttributes(new ProgramYearAttribute(label));
        return type;
    }

    @Override
    public JsonResponse getLearnerYearList(String utorId) {
        //logger.entry();
        JsonResponse json = null;
        try {
           // if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
           //     utorId = userSvc.getUtoridForPseudoUtorid(utorId);
           // }
            List<YearDTO> years = getYearsForUser(utorId);
            json = TransformerYearJson.toYearJson(years);
        } catch (Exception e) {
            throw new MedITException(e.getMessage(), e);
        }
       // logger.exit();
        return json;
    }

    @Transactional(readOnly = true)
    public List<YearDTO> getYearsForUser(String utorId) {
        List<YearDTO> years = new ArrayList<>();
        try {
            // Native SQL query
            String sql = "SELECT DISTINCT a.ACADEMIC_YEAR, a.PROGRAM_YEAR " +
                    "FROM assessment a, authuser_score aus, score s, users u " +
                    "WHERE s.SCORE_ID = aus.SCORE_ID " +
                    "AND u.USERS_ID = aus.AUTHUSER_ID " +
                    "AND a.ASSESSMENT_ID = s.ASSESSMENT_ID " +
                    "AND a.ASSESSMENT_TYPE <> 'PROGRESS_REVIEW' " +
                    "AND u.UTORID = :utorId " +
                    "ORDER BY a.ACADEMIC_YEAR ASC, a.PROGRAM_YEAR ASC";

            // Execute the query
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("utorId", utorId);

            @SuppressWarnings("unchecked")
            List<Object[]> rows = query.getResultList();

            // Map the result set into YearDTO objects
            for (Object[] row : rows) {
                YearDTO year = new YearDTO();
                year.setAcademicYear((Integer) row[0]);
                year.setProgramYear((Integer) row[1]);
                years.add(year);
            }
        } catch (RuntimeException re) {
            String message = "Error when getting years for user " + utorId;
            throw new DataAccessException(message, re) {};
        }
        return years;
    }



}
