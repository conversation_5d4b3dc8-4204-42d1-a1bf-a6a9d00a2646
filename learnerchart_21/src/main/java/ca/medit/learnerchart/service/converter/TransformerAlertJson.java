package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.AlertAttribute;
import ca.medit.learnerchart.domain.AlertType;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.dto.AlertDTO;
import ca.medit.learnerchart.dto.YearDTO;
import ca.medit.learnerchart.entity.Alert;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class TransformerAlertJson {

    public static JsonResponse toAlertJson(Set<Alert> alerts) {
        JsonResponse json = new JsonResponse();
        List<JsonType> types = new ArrayList<JsonType>();
        for (Alert alert : alerts) {
            types.add(toAlertJsonType(convertToAlertDTO(alert)));
        }
        json.setData(types);
        return json;
    }

    public static JsonResponse toAlertJson(List<AlertDTO> alerts) {
        JsonResponse json = new JsonResponse();
        List<JsonType> types = new ArrayList<JsonType>();
        for (AlertDTO alert : alerts) {
            types.add(toAlertJsonType(alert));
        }
        json.setData(types);
        return json;
    }

    public static JsonType toAlertJsonType(AlertDTO alert) {
        JsonType jsonType = new JsonType(alert.getId(), JsonType.TYPE_ALERTS);
        AlertAttribute attriubte = new AlertAttribute();
        attriubte.setReferenceId(alert.getReferenceId());
        attriubte.setType(alert.getType());
        attriubte.setTitle(alert.getTitle());
        attriubte.setExternalLink("");
        attriubte.setYear(TransformerYearJson.toYearJson(new YearDTO(alert.getYearId())));
        if (alert.getDateTime() != null) {
            attriubte.setDate(alert.getDateTime().toLocalDate());
        }
        if (alert.getAlertType() == AlertType.FOCUSED_LEARNING_PLAN) {
            attriubte.setScholarVisible(true);
            attriubte.setButtonLabel("View files");
        }
        jsonType.setAttributes(attriubte);
        return jsonType;
    }

    private static AlertDTO convertToAlertDTO(Alert alert) {
        AlertDTO alertDTO = new AlertDTO();
        alertDTO.setId(alert.getAlertId());
        alertDTO.setReferenceId(alert.getReferenceId());
        alertDTO.setType(alert.getType());
        alertDTO.setTitle(alert.getTitle());
        alertDTO.setAssessmentMode(alert.getAssessmentMode());
        alertDTO.setAlertType(alert.getAlertType());
        alertDTO.setExternalLink(alert.getExternalLink());
        alertDTO.setDateTime(alert.getCreatedon());
        alertDTO.setYearId(alert.getYearId());
        return alertDTO;
    }


}
