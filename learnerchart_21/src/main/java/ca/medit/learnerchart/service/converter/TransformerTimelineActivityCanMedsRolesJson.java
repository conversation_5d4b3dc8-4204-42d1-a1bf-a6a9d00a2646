package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.dto.CanMedsRole;

import java.util.Collections;
import java.util.Set;

public class TransformerTimelineActivityCanMedsRolesJson {

    public static JsonResponse toJsonResponse(Set<CanMedsRole> canMedsRoles) {
        JsonResponse jsonResponse = new JsonResponse();
        if(canMedsRoles == null) {
            canMedsRoles = Collections.emptySet();
        }
        jsonResponse.setData(canMedsRoles);
        return jsonResponse;
    }

}
