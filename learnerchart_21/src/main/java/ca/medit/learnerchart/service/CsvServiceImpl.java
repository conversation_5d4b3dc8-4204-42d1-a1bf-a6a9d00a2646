package ca.medit.learnerchart.service;

import ca.medit.learnerchart.entity.Score;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import ca.medit.learnerchart.repository.ScoreRepository;

@Service
public class CsvServiceImpl implements CsvService{

    @Autowired
    private ScoreRepository scoreRepository;

    @Override
    public void parseCsv(MultipartFile file) {

        List<Score> scores = new ArrayList<>();

        try (Reader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT
                    .withFirstRecordAsHeader()
                    .withIgnoreHeaderCase()
                    .withTrim());

            for (CSVRecord csvRecord : csvParser) {

                Score score = new Score();

//                score.setAcademicYear(csvRecord.get("AcadYear"));
//                score.setProgramYear(csvRecord.get("ProgramYear"));
//                score.setScore(Double.valueOf(csvRecord.get("Score")));
//                score.setUploadedOn(LocalDateTime.now());
//                score.setUploadId(Long.valueOf(csvRecord.get("UploadId")));
                score.setStudentNumber(Long.valueOf(csvRecord.get("StudentNumber")));

                scoreRepository.save(score);

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
