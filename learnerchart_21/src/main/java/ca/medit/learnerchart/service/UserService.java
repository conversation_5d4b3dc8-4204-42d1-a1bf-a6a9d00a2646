package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.entity.UserImage;

import java.util.List;

public interface UserService {

    User saveStudent(User authUser);
    List<User> getAllUsers() ;

    User createUser(User user);

    User getUser(Long userId);

    User updateUser(User user);

    void deleteUser(User user);

    JsonResponse getCurrentUser();

    User findUserByUsername(String username);

    boolean checkUserAlreadyExists(String username);

    UserImage getLearnerPicture(String utorId);

}
