package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

@Service
public class MedSISFileServiceImpl implements MedSISFileService{


    @Value("${medsis.file.base}")
    private String base;

    @Value("${medsis.file.scheme}")
    private String scheme;

    @Value("${medsis.file.student}")
    private String studentFile;

    @Value("${medsis.file.evaluation}")
    private String evaluationFile;

    @Value("${medsis.file.picture}")
    private String pictureFile;

    @Override
    public InputStream readStudentFile() throws IOException {
       // if (scheme.equals("file")) {
            return getLocalFileAsInputStream(studentFile);
       // }

//        URI uri = getStudentFileUri();
//        return getURIAsInputStream(uri);
    }

    @Override
    public InputStream readEvaluationFile() throws IOException {
      //  if (scheme.equals("file")) {
            return getLocalFileAsInputStream(evaluationFile);
      //  }

    //    URI uri = getEvaluationFileUri();
    //    return getURIAsInputStream(uri);
    }

    @Override
    public InputStream readPictureFile() throws IOException {

        if (scheme.equals("file")) {
            return getLocalFileAsInputStream(pictureFile);
        }

//        URI uri = getPictureFileUri();
//        return getURIAsFileInputStream(uri);
        return null;
    }

    private InputStream getLocalFileAsInputStream(String fileName) {
        InputStream input = null;
        File f = null;
        try {
            f = new File(base, fileName);
            input = new FileInputStream(f);
        } catch (FileNotFoundException e) {
            throw new MedITException("File not found: " + f, e);
        }

        return input;
    }
}
