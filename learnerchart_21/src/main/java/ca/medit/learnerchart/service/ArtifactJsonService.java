package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.entity.TextArtifact;
import ca.medit.learnerchart.entity.User;

public interface ArtifactJsonService {


    /**
     *
     * @param utorId
     * @return
     */
    JsonResponse getTextArtifactsForLearner(String utorId);

    /**
     *
     * @param utorId
     * @return
     */
    JsonResponse getFileArtifactsForLearner(String utorId);

    /**
     *
     * @param utorId
     * @return
     */
    JsonResponse getLinkArtifactsForLearner(String utorId);

    /**
     *
     * @param json
     */
    JsonResponse addFileArtifactForLearner(JsonFileArtifact json);

    /**
     * Create a new {@link TextArtifact} for the {@link User} and return a
     * JSON object containing the result
     *
     * @param utorId
     * @param textArtifact
     * @return JSON
     */
    JsonResponse addTextArtifactForLearner(String utorId, JsonTextArtifact textArtifact);

    /**
     *
     *
     * @param utorId
     * @param linkArtifact
     * @return
     */
    JsonResponse addLinkArtifactForLearner(String utorId, JsonLinkArtifact linkArtifact);

    JsonResponse addFocusedLearningPlansForLearner(FocusedLearningPlanJsonFile jsonFile);

    JsonResponse updateFocusedLearningPlanForLearner(FocusedLearningPlanJsonFile jsonFile);

    /**
     *
     * @param json
     * @return
     */
    JsonResponse updateFileArtifactForLearner(JsonFileArtifact json);

    /**
     *
     * @param json
     * @return
     */
    JsonResponse updateTextArtifactForLearner(JsonTextArtifact json);

    /**
     *
     * @param json
     * @return
     */
    JsonResponse updateLinkArtifactForLearner(JsonLinkArtifact json);

    /**
     * Toggle the association between the CplanXid whose label matches xid and
     * the {@linkArtifactId} and return a JsonResponse object representing the
     * updated entity.
     *
     * @param xid
     * @param linkArtifactId
     * @return {@link JsonResponse}
     */
    JsonResponse toggleCplanXidForLinkArtifact(String xid, Long linkArtifactId);

    JsonResponse getFocusedLearningPlansForLearner(String utorId);


}
