package ca.medit.learnerchart.service;

import ca.medit.learnerchart.entity.Assessment;

import java.util.List;

public interface AssessmentService {

    /**
     *
     * @return
     */
    List<Assessment> getListOfAssessments();

    /**
     * Update the {@link Assessment} or create a new one if it does not exist.
     *
     * @param assessment
     * @return the persisted entity
     */
    Assessment saveAssessment(Assessment assessment);

    /**
     *
     * @param assessments
     * @param programYear
     * @return
     */
    List<Assessment> getMedsisAssessmentsToRemove(List<Assessment> assessments, int programYear);
}
