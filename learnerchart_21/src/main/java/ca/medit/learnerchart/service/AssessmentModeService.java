package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.JsonResponse;

public interface AssessmentModeService {

    JsonResponse getAssessmentModes();

    JsonResponse getAssessmentMode(String key);

    /**
     * Handle exceptions in a standard way when resolving String keys into Enum
     *
     * @param key
     * @return the enum value
     * @throws MedITException if <code>key</code> is not legal
     */
    AssessmentMode getModeForKey(String key);
}
