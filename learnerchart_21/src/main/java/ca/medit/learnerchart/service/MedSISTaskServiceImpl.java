package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.web.multipart.MultipartFile;

@Log4j2
@Service
public class MedSISTaskServiceImpl implements MedSISTaskService {


    @Autowired
    private MedSISFileService fileSvc;

    @Autowired
    private MedSISAssessmentService assessmentSvc;

    private final ConcurrentHashMap<String, Boolean> taskLockMap = new ConcurrentHashMap<>();

    @Override
    public void runStudentDataImport() {

        try {
            InputStream input = fileSvc.readStudentFile();
            assessmentSvc.saveStudentUsers(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        }
    }

    @Override
    public void runStudentDataImportFile(MultipartFile file) {

        try {
            InputStream input = file.getInputStream();
            assessmentSvc.saveStudentUsers(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        }
    }

    @Override
    public void runStudentImageImport() {
        try {
            InputStream input = fileSvc.readPictureFile();
            assessmentSvc.saveStudentImages(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        }
    }

    @Async
    @Override
    public void runEvaluationDataImport() {
        if (taskLockMap.putIfAbsent("evaluationImport", true) != null) {
            // Task is already running
            return;
        }

        try {
            InputStream input = fileSvc.readEvaluationFile();
            assessmentSvc.saveAssessments(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        } finally {
            taskLockMap.remove("evaluationImport");
        }

    }


    @Override
    public void runEvaluationDataImportFile(MultipartFile file) {
        if (taskLockMap.putIfAbsent("evaluationImport", true) != null) {
            // Task is already running
            return;
        }

        try {
            InputStream input = file.getInputStream();
            assessmentSvc.saveAssessments(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        } finally {
            taskLockMap.remove("evaluationImport");
        }

    }


    @Override
    public void runStudentImageImportFile(MultipartFile file) {

        try {
            InputStream input = file.getInputStream();
            assessmentSvc.saveStudentImages(input);
        } catch (IOException e) {
            log.catching(e);
            String message = "Unknown IO error";
            if (e.getMessage() != null) {
                message = e.getMessage();
            }
            throw new MedITException(message, e);
        }

    }
}
