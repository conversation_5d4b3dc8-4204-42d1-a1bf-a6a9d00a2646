package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.repository.CanmedsMapRepository;
import ca.medit.learnerchart.service.converter.TransformerCanMedsRoleFilter;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CanMedsRoleServiceImpl implements CanMedsRoleService{

    private static final Logger logger = LogManager.getLogger(CanMedsRoleService.class);

    @Autowired
    private CanmedsMapRepository canmedsMapRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<CanMedsMapItem> getCanMedsRolesItems() {
        List<CanMedsMapItem> items = new ArrayList<>();

        try {
            // JPQL query to get parent and child mappings
            String query = "SELECT x.cplanXidId as parent_id, x.frameworkDef as parent_def, x.label as parent_label, x.value as parent_value, "
                    + "y.cplanXidId as child_id, y.frameworkDef as child_def, y.label as child_label, y.value as child_value "
                    + "FROM CanmedsMap m JOIN m.cplanXidParent x JOIN m.cplanXidChild y "
                    + "WHERE m.cplanXidParent = m.cplanXidTop "
                    + "AND x.cplanXidId <> y.cplanXidId";

            List<Object[]> results = entityManager.createQuery(query, Object[].class).getResultList();

            if (results != null) {
                for (Object[] row : results) {
                    CanMedsMapItem item = new CanMedsMapItem();
                    item.setParentCplanXid(((Long) row[0]));
                    item.setParentFrameworkDef((String) row[1]);
                    item.setParentLabel((String) row[2]);
                    item.setParentValue((String) row[3]);
                    item.setChildCplanXid(((Long) row[4]));
                    item.setChildFrameworkDef((String) row[5]);
                    item.setChildLabel((String) row[6]);
                    item.setChildValue((String) row[7]);
                    item.setLevel(2);  // Set to 2 based on your logic
                    items.add(item);
                }
            }
        } catch (RuntimeException re) {
            logger.error("Unable to get Key Competencies", re);
            throw new RuntimeException("Unable to get Key Competencies", re);
        }

        return items;
    }

    @Override
    public JsonResponse getCanMedsRoles() {
        logger.entry();
        List<CanMedsMapItem> items = new ArrayList<>();
        JsonResponse json = new JsonResponse();

        try {
            // JPQL query to get parent and child mappings
            String query = "SELECT x.cplanXidId as parent_id, x.frameworkDef as parent_def, x.label as parent_label, x.value as parent_value, "
                    + "y.cplanXidId as child_id, y.frameworkDef as child_def, y.label as child_label, y.value as child_value "
                    + "FROM CanmedsMap m JOIN m.cplanXidParent x JOIN m.cplanXidChild y "
                    + "WHERE m.cplanXidParent = m.cplanXidTop "
                    + "AND x.cplanXidId <> y.cplanXidId";

            List<Object[]> results = entityManager.createQuery(query, Object[].class).getResultList();

            if (results != null) {
                for (Object[] row : results) {
                    CanMedsMapItem item = new CanMedsMapItem();
                    item.setParentCplanXid(((Long) row[0]));
                    item.setParentFrameworkDef((String) row[1]);
                    item.setParentLabel((String) row[2]);
                    item.setParentValue((String) row[3]);
                    item.setChildCplanXid(((Long) row[4]));
                    item.setChildFrameworkDef((String) row[5]);
                    item.setChildLabel((String) row[6]);
                    item.setChildValue((String) row[7]);
                    item.setLevel(2);  // Set to 2 based on your logic
                    items.add(item);
                }
            }

            json.setData(TransformerCanMedsRoleFilter.toCanMedRole(items));

        } catch (RuntimeException re) {
            logger.error("Unable to get Key Competencies", re);
            throw new RuntimeException("Unable to get Key Competencies", re);
        }

        logger.exit();
        return json;
    }



}
