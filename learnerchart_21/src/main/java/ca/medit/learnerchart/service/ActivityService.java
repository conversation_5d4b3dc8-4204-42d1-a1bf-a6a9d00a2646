package ca.medit.learnerchart.service;

import ca.medit.learnerchart.dto.CohortNumericScoreAverageMap;
import ca.medit.learnerchart.dto.LearnerFile;
import ca.medit.learnerchart.dto.TimelineActivity;
import ca.medit.learnerchart.entity.Score;

import java.sql.SQLException;
import java.util.List;

public interface ActivityService {
    List<TimelineActivity> getTimelineActivities(LearnerChartFilter filter);

    CohortNumericScoreAverageMap getActivityAverageMap(LearnerChartFilter filter);

    LearnerFile getOsceFile(Long scoreId) throws SQLException;

    List<Score> getAsessmentScoresForLearner(long assessmentId, String username);

}
