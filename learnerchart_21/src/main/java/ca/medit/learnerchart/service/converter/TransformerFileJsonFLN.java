package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.FileAttribute;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.entity.FocusedLearningPlan;

public class TransformerFileJsonFLN extends TransformerFileJson {

    public TransformerFileJsonFLN(FocusedLearningPlan focusedLearningPlan) {
        super();
        containerId = focusedLearningPlan.getFocusedLearningPlanId();
        file = focusedLearningPlan.getFile();
    }

    @Override
    public JsonType toFileJsonType() {
        JsonType jsonType = new JsonType(file.getFileId(), JsonType.TYPE_FILES);
        FileAttribute attributes = getFileAttribute();
        attributes.setFileLink("/appl/artifacts/learningPlan/file/" + containerId + "/download");
        jsonType.setAttributes(attributes);
        return jsonType;
    }


}
