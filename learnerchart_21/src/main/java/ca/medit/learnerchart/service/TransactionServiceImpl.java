package ca.medit.learnerchart.service;

import ca.medit.learnerchart.entity.Assessment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TransactionServiceImpl implements TransactionService{

    @Autowired
    private AssessmentService assessmentService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Assessment saveAssessment(Assessment assessment) {
        return assessmentService.saveAssessment(assessment);
    }
}
