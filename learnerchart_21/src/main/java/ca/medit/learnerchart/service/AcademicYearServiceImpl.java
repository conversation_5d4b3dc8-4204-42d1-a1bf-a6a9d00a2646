package ca.medit.learnerchart.service;

import org.springframework.stereotype.Service;

import java.util.Calendar;

@Service
public class AcademicYearServiceImpl implements AcademicYearService{

    @Override
    public int getCurrentAcademicYear() {
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);
        return month >= 8 ? year : year - 1;
    }
}
