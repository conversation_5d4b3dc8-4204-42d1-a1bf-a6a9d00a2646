package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.dto.LearnerActivity;
import ca.medit.learnerchart.service.LearnerChartFilter;

import java.util.List;

public class TransformerLinkJson {


    public static final String LINK_LABEL = "View";
    public static final String LINK_DOWNLOAD_LABEL = "Download file";
    public static final String LINK_WRITTEN_NAME = "Written";
    public static final String lLINK_WRITTEN_PROGRESS_TEST_NAME = "ProgressTest";
    public static final String lLINK_WRITTEN_EPA_SUMMARIES_NAME = "EpaSummaries";
    public static final String LINK_EVALUATION_NAME = "EvaluationForm";
    public static final String LINK_PERFORMANCE_BASED_NAME = "Osce";
    public static final String LINK_ASSIGNMENT_NAME = "Assignment";

    public static JsonResponse toLinkJson(LearnerActivity activity, LearnerChartFilter filter) {
        JsonResponse json = null;
        JsonType type = null;
        switch (filter.getMode()) {
            case EVALUATION:
                type = toEvaluationLinkJsonType(activity, filter);
                break;
            case PERFORMANCE_BASED:
                type = toPerformanceBasedLinkJsonType(activity);
                break;
            case WRITTEN:
                if (AssessmentType.PROGRESS_TEST.name().equals(activity.getAssessmentType())) {
                    type = toWrittenProgressTestLinkJsonType(activity);
                } else if (!AssessmentType.BELL_RINGER.name().equals(activity.getAssessmentType())) {
                    type = toWrittenLinkJsonType(activity, filter.getExamsoftUrl());
                } else if (AssessmentType.EPA_SUMMARIES.name().equals(activity.getAssessmentType())) {
                    type = toWrittenEpaSummariesLinkJsonType(activity);
                }
                break;
            case ASSIGNMENT:
                type = toAssignmentLinkJsonType(activity, filter);
                break;
        }
        if (type != null) {
            json = new JsonResponse();
            json.setData(List.of(type));
        }
        return json;
    }

    private static JsonType toAssignmentLinkJsonType(LearnerActivity activity, LearnerChartFilter filter) {
        JsonType jsonType = new JsonType(activity.getAssessmentId(), JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(LINK_ASSIGNMENT_NAME);
        attributes.setUrl("/learnerchart/assignment/" + filter.getLearnerId() + "/activity/" + activity.getAssessmentId());
        attributes.setLabel(LINK_LABEL);
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    private static JsonType toWrittenLinkJsonType(LearnerActivity activity, String examsoftUrl) {
        JsonType jsonType = new JsonType(activity.getAssessmentId(), JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(LINK_WRITTEN_NAME);
        if (AssessmentType.EPA_SUMMARIES.name().equals(activity.getAssessmentType())) {
            attributes.setUrl("/learnerchart/appl/activities/osce/" + activity.getScoreId() + "/file");
            attributes.setLabel(LINK_DOWNLOAD_LABEL);
        }else {
            attributes.setUrl(examsoftUrl);
            attributes.setLabel(LINK_LABEL);
        }
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    private static JsonType toWrittenProgressTestLinkJsonType(LearnerActivity activity) {
        JsonType jsonType = new JsonType(activity.getScoreId(), JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(lLINK_WRITTEN_PROGRESS_TEST_NAME);
        attributes.setUrl("/learnerchart/appl/activities/osce/" + activity.getScoreId() + "/file");
        attributes.setLabel(LINK_DOWNLOAD_LABEL);
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    private static JsonType toWrittenEpaSummariesLinkJsonType(LearnerActivity activity) {
        JsonType jsonType = new JsonType(activity.getScoreId(), JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(lLINK_WRITTEN_EPA_SUMMARIES_NAME);
        attributes.setUrl("/learnerchart/appl/activities/osce/" + activity.getScoreId() + "/file");
        attributes.setLabel(LINK_DOWNLOAD_LABEL);
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    private static JsonType toEvaluationLinkJsonType(LearnerActivity activity, LearnerChartFilter filter) {
        Long remoteId = activity.getRemoteId();
        if (remoteId == null) return null;
        JsonType jsonType = new JsonType(remoteId, JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(LINK_EVALUATION_NAME);
        if (FeedSourceTypeEnum.ELENTRA == activity.getSource()) {
            attributes.setUrl(filter.getElentraUrl() + "/assessments/assessment?dassessment_id=" + remoteId);
        } else {
            attributes.setUrl(filter.getMedsisUrl() + "/medsis/externalAuth/previewEvaluation.cfm?evalID=" + remoteId);
        }
        attributes.setLabel(LINK_LABEL);
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }

    private static JsonType toPerformanceBasedLinkJsonType(LearnerActivity activity) {
        String reference = activity.getReference();
        if (reference == null || reference.trim().isEmpty()) return null;
        JsonType jsonType = new JsonType(activity.getScoreId(), JsonType.TYPE_LINKS);
        LinkAttribute attributes = new LinkAttribute();
        attributes.setName(LINK_PERFORMANCE_BASED_NAME);
        attributes.setUrl("/learnerchart/appl/activities/osce/" + activity.getScoreId() + "/file");
        attributes.setLabel(LINK_LABEL);
        attributes.setEnabled(true);
        jsonType.setAttributes(attributes);
        return jsonType;
    }


}
