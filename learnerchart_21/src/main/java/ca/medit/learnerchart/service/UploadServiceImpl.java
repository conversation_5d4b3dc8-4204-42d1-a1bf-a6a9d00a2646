package ca.medit.learnerchart.service;

import ca.medit.learnerchart.entity.UploadedFile;
import ca.medit.learnerchart.entity.UploadRecord;
import ca.medit.learnerchart.repository.UploadedFileRepository;
import ca.medit.learnerchart.repository.UploadRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;

@Service
public class UploadServiceImpl implements UploadService {

    @Autowired
    private UploadedFileRepository uploadedFileRepository;

    @Autowired
    private UploadRecordRepository uploadRecordRepository;

    @Override
    public void saveUploadedFiles(MultipartFile resultsFile, MultipartFile zipFile, String programYear, String academicYear) throws IOException {
        UploadedFile resultsUploadedFile = saveFile(resultsFile);
        UploadedFile zipUploadedFile = saveFile(zipFile);

        UploadRecord uploadRecord = new UploadRecord();
        uploadRecord.setProgramYear(programYear);
        uploadRecord.setAcademicYear(academicYear);
        uploadRecord.setResultsFile(resultsUploadedFile);
        uploadRecord.setZipFile(zipUploadedFile);
        uploadRecord.setUploadedOn(LocalDateTime.now());

        uploadRecordRepository.save(uploadRecord);
    }

    private UploadedFile saveFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }

        UploadedFile uploadedFile = new UploadedFile();
        uploadedFile.setFileName(file.getOriginalFilename());
        uploadedFile.setFileType(file.getContentType());
        uploadedFile.setData(file.getBytes());
        uploadedFile.setUploadedOn(LocalDateTime.now());

        return uploadedFileRepository.save(uploadedFile);
    }
}
