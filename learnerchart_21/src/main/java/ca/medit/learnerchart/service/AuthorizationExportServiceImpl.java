package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AuthorizationJsonResponse;
import ca.medit.learnerchart.entity.Group;
import ca.medit.learnerchart.repository.UserRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Log4j2
public class AuthorizationExportServiceImpl implements AuthorizationExportService{

    @Autowired
    private UserRepository userRepository;

    class SupervisorPair {
        String supervisor;
        String learner;

        public SupervisorPair(Object[] r) {
            if (r.length != 2 || !(r[0] instanceof String && r[1] instanceof String)) {
                throw new IllegalArgumentException("Expect pair of Strings");
            }
            supervisor = (String) r[0];
            learner = (String) r[1];
        }
    }

    @Override
    public AuthorizationJsonResponse getAuthorizationJson() {
        log.entry();
        AuthorizationJsonResponse json = new AuthorizationJsonResponse();

        try {
            List<String> admins = userRepository.fetchAdmins(Group.UME_ADMINISTRATOR);

            for (String admin : admins) {
                json.addAdministrator(admin);
            }
        } catch (RuntimeException e) {
            throw new MedITException("Cannot find UME Administrators", e);
        }
        for (SupervisorPair p : getScholarsWithLearners()) {
            json.addSupervisorForLearner(p.supervisor, p.learner);
        }
        for (SupervisorPair p : getSupportersWithLearners()) {
            json.addSupervisorForLearner(p.supervisor, p.learner);
        }

        return json;
    }

    private List<SupervisorPair> getScholarsWithLearners() {
        List<SupervisorPair> list = new ArrayList<>();

        try {
            List<Object[]> rows = userRepository.fetchScholarsWithLearners(Group.SCHOLAR);

            for (Object[] r : rows) {
                list.add(new SupervisorPair(r));
            }
        } catch (IllegalArgumentException e) {
            throw new MedITException("Cannot cast results as SupervisorPair data", e);
        } catch (RuntimeException re) {
            throw new MedITException("Cannot find Scholar/Learner pairs", re);
        }

        return list;
    }

    private List<SupervisorPair> getSupportersWithLearners() {
        List<SupervisorPair> list = new ArrayList<>();

        try {
            List<Object[]> rows = userRepository.fetchSupportersWithLearners(Group.LEARNER_SUPPORTER);
            for (Object[] r : rows) {
                list.add(new SupervisorPair(r));
            }
        } catch (IllegalArgumentException e) {
            throw new MedITException("Cannot cast results as SupervisorPair data", e);
        } catch (RuntimeException re) {
            throw new MedITException("Cannot find Scholar/Learner pairs", re);
        }

        return list;
    }
}
