package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.entity.CanMedsMapItem;

import java.util.*;

public class TransformerCanMedsRoleFilter {

    public static Object toCanMedRole(List<CanMedsMapItem> canMapItems) {
        List<CanMedsMapItem> canMedsRolesItems = getCanMedsRolesItems(canMapItems);
        List<JsonType> canMedRoles = new ArrayList<JsonType>();
        for (CanMedsMapItem canMedsRolesItem : canMedsRolesItems) {
            canMedRoles.add(transformRole(canMapItems, canMedsRolesItem));
        }
        return canMedRoles;
    }

    private static List<CanMedsMapItem> getCanMedsRolesItems(List<CanMedsMapItem> keyCompetencyItems) {
        Map<Long, CanMedsMapItem> map = new HashMap<Long, CanMedsMapItem>();
        for (CanMedsMapItem keyCompetencyItem : keyCompetencyItems) {
            CanMedsMapItem canMedsRoleItem = map.get(keyCompetencyItem.getParentCplanXid());
            if (canMedsRoleItem == null) {
                canMedsRoleItem = new CanMedsMapItem();
                canMedsRoleItem.setChildCplanXid(keyCompetencyItem.getParentCplanXid());
                canMedsRoleItem.setChildFrameworkDef(keyCompetencyItem.getParentFrameworkDef());
                canMedsRoleItem.setChildLabel(keyCompetencyItem.getParentLabel());
                canMedsRoleItem.setChildValue(keyCompetencyItem.getParentValue());
                map.put(keyCompetencyItem.getParentCplanXid(), canMedsRoleItem);
            }
        }
        List<CanMedsMapItem> canMedsRoles = new ArrayList<CanMedsMapItem>(map.values());
        Collections.sort(canMedsRoles, new Comparator<CanMedsMapItem>() {
            @Override
            public int compare(CanMedsMapItem o1, CanMedsMapItem o2) {
                return (int) (o1.getChildCplanXid() - o2.getChildCplanXid());
            }
        });
        return canMedsRoles;
    }

    private static JsonType transformRole(List<CanMedsMapItem> canMapItems,
                                          CanMedsMapItem mapItem) {
        JsonType canMedRole = new JsonType(mapItem.getChildCplanXid(),
                JsonType.TYPE_CANMED_ROLE);

        CanMedAttributes attribute = new CanMedAttributes();
        attribute.setXid(mapItem.getChildValue());
        attribute.setLabel(mapItem.getChildLabel());
        canMedRole.setAttributes(attribute);

        JsonResponse keyCompetencyResp = getKeyCompetencies(canMapItems, mapItem);

        CanmedRelationship canmedRelationship = new CanmedRelationship();
        canmedRelationship.setKeyCompetencies(keyCompetencyResp);

        canMedRole.setRelationships(canmedRelationship);

        return canMedRole;
    }

    private static JsonResponse getKeyCompetencies(
            List<CanMedsMapItem> canMapItems, CanMedsMapItem role) {
        JsonResponse keyCompetencyResp = new JsonResponse();
        List<JsonType> keyCompetencies = new ArrayList<JsonType>();

        for (CanMedsMapItem mapItem : canMapItems) {
            if (mapItem.getChildFrameworkDef().equals(
                    FrameworkDef.PROGRAM_OBJECTIVES.name())
                    && mapItem.getParentCplanXid().equals(role.getChildCplanXid())) {
                JsonType keyCompetency = new JsonType(
                        mapItem.getChildCplanXid(),
                        JsonType.Type_KEY_COMPETENCY);

                CanMedAttributes attribute = new CanMedAttributes();
                attribute.setXid(mapItem.getChildValue());
                attribute.setLabel(mapItem.getChildLabel());
                keyCompetency.setAttributes(attribute);

                keyCompetencies.add(keyCompetency);
            }
        }
        keyCompetencyResp.setData(keyCompetencies);
        return keyCompetencyResp;
    }

}
