package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.AssessmentEvent;
import ca.medit.learnerchart.entity.AssessmentEventKey;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.repository.AssessmentEventRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@Log4j2
public class AssessmentEventServiceImpl  implements AssessmentEventService{


    @Autowired
    private AssessmentEventRepository assessmentEventRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void deleteByRemoteId(FeedSourceTypeEnum source, long remoteId) {
        try {
            assessmentEventRepository.deleteByRemoteId(source, remoteId);
        } catch (Exception e) {
            throw new MedITException("Failed to delete assessment event by remote Id.", e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<AssessmentEvent> createAssessmentEvents(Assessment assessment) {
        List<AssessmentEvent> assessmentEvents = new ArrayList<AssessmentEvent>();
        Set<Score> scores = assessment.getScores();
        if (scores != null) {
            long remoteId = assessment.getRemoteId();
            FeedSourceTypeEnum source = assessment.getSource();
            String utorId = null;
            for (Score score : scores) {
                if (score.getUtorid() != null && !score.getUtorid().equals(utorId)) {
                    utorId = score.getUtorid();
                    AssessmentEventKey assessmentEventKey = new AssessmentEventKey();
                    assessmentEventKey.setRemoteId(remoteId);
                    assessmentEventKey.setSource(source);
                    assessmentEventKey.setUtorid(utorId);
                    AssessmentEvent assessmentEvent = new AssessmentEvent();
                    assessmentEvent.setEventKey(assessmentEventKey);
                    //assessmentEvent = assessmentEventDAO.makePersistent(assessmentEvent);
                    assessmentEvent = assessmentEventRepository.save(assessmentEvent);
                    assessmentEvents.add(assessmentEvent);
                }
            }
        }
        return assessmentEvents;
    }
}
