package ca.medit.learnerchart.service;


import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.ActivityFileAttribute;
import ca.medit.learnerchart.domain.ActivityRelationshipWithFeedbacks;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.dto.CohortNumericScoreAverageMap;
import ca.medit.learnerchart.dto.SubmissionFeedback;
import ca.medit.learnerchart.dto.SubmissionFileInfo;
import ca.medit.learnerchart.dto.TimelineActivity;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.repository.AssessmentRepository;
import ca.medit.learnerchart.service.converter.TransformerActivityFeedbackJson;
import ca.medit.learnerchart.service.converter.TransformerAssignmentActivityJson;
import ca.medit.learnerchart.service.converter.TransformerTimelineActivityJson;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Log4j2
public class ActivityTimelineJsonServiceImpl implements ActivityTimelineJsonService{




   // XLogger logger = XLoggerFactory.getXLogger(this.getClass());
    @Autowired
    private ActivityService activitySvc;

    @Autowired
    private AssessmentRepository assessmentRepository;

    @Autowired
    private OasesService oasesSvc;

    private List<CanMedsMapItem> canMedsRoles;

//    @PostConstruct
//    public void loadCanMedsRoles() {
//        canMedsRoles = canMedsMapDAO.getCanMedsRoles();
//    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public JsonResponse getActivitiesForTimeLine(LearnerChartFilter filter) {
     //   logger.entry();
        JsonResponse json = new JsonResponse();

        List<TimelineActivity> activities = activitySvc.getTimelineActivities(filter);
        CohortNumericScoreAverageMap activityAverageMap = activitySvc.getActivityAverageMap(filter);
        TransformerTimelineActivityJson transformer = new TransformerTimelineActivityJson(activities,
                activityAverageMap, filter);
        json.setData(transformer.getData());

      //  logger.exit();
        return json;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Transactional(readOnly = true)
    public JsonResponse getAssignmentActivityDetail(long activityId, String username) {

        log.entry();
        JsonResponse json = new JsonResponse();

        try {
            Assessment assessment = assessmentRepository.getById(activityId);
            if (assessment == null) {
                throw new MedITException("Assessment not found. Activity ID = " + activityId);
            }
//            if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
//                username = userSvc.getUtoridForPseudoUtorid(username);
//            }
            List<Score> scores = activitySvc.getAsessmentScoresForLearner(activityId, username);
            JsonType skeletonJsonType = new TransformerAssignmentActivityJson(null)
                    .toAssignmentActivitySkeletonJsonType(assessment, scores, canMedsRoles);

            ActivityRelationshipWithFeedbacks relationships = (ActivityRelationshipWithFeedbacks) skeletonJsonType
                    .getRelationships();
            List<JsonType> files = (List<JsonType>) relationships.getFiles().getData();
            long submissionId = -1;

            for (JsonType file : files) {
                SubmissionFileInfo submissionFile = oasesSvc.getSubmissionFileInfo((Long) file.getId());
                if (submissionFile != null) {
                    ActivityFileAttribute fileAttributes = (ActivityFileAttribute) file.getAttributes();
//                    if (Utils.isUserInRole(Authority.ROLE_PSEUDO_ADMIN)) {
//                        fileAttributes.setName(PseudoFileUtil.PSEUDO_OASES_FILE_NAME);
//                    } else {
                        fileAttributes.setName(submissionFile.getFileName());
//                    }
                    fileAttributes.setDate(submissionFile.getSubmissionDate().toLocalDate());
                    submissionId = submissionFile.getSubmissionId();
                }
            }

            if (submissionId != -1) {
                List<SubmissionFeedback> feedbacks = oasesSvc.getSubmissionFeedbacks(submissionId);
                relationships.setFeedbacks(TransformerActivityFeedbackJson.toActivityFeedbackJson(feedbacks));
            } else {
                relationships.setFiles(null);
            }

            json.setData(skeletonJsonType);

        } catch (MedITException dde) {
            throw new MedITException(dde.getMessage(), dde);
        }
        log.exit();
        return json;
    }


}
