package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.ActivityScoreEnum;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.service.LearnerChartFilter;

public class TransformerActivityJson {

  //  private XLogger logger = XLoggerFactory.getXLogger(this.getClass());

    protected LearnerChartFilter filter;

    public TransformerActivityJson(LearnerChartFilter filter) {
        this.filter = filter;
    }

    public LearnerChartFilter getFilter() {
        return filter;
    }

    public void setFilter(LearnerChartFilter filter) {
        this.filter = filter;
    }

    public AssessmentMode getAssessmentMode() {
        return filter.getMode();
    }

    /**
     * Null safe utility
     *
     * @param scoreEnum
     * @return a String value to use as a JsonType id
     */
    protected String getScoreId(ActivityScoreEnum scoreEnum) {
        String id = null;
        if (scoreEnum != null) {
            id = scoreEnum.name();
        }
        return id;
    }

    /**
     * Null safe utility
     *
     * @param scoreEnum
     * @return a String value to use as a JsonType id
     */
    protected String getScoreLabel(ActivityScoreEnum scoreEnum) {
        String label = null;
        if (scoreEnum != null) {
            label = scoreEnum.getLabel();
        }
        return label;
    }


}
