package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.common.exception.MedSisEvaluationImportException;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.dto.AssessmentHelper;
import ca.medit.learnerchart.dto.EvaluationReport;
import ca.medit.learnerchart.dto.EvaluationReport.Evaluation;
import ca.medit.learnerchart.dto.EvaluationReport.EvaluationBase;
import ca.medit.learnerchart.dto.StudentHelper;
import ca.medit.learnerchart.dto.StudentReport;
import ca.medit.learnerchart.entity.*;
import ca.medit.learnerchart.repository.CohortRepository;
import ca.medit.learnerchart.repository.GroupRepository;
import ca.medit.learnerchart.repository.UserImageRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Service
@Log4j2
public class MedSISAssessmentServiceImpl implements MedSISAssessmentService{

    @Autowired
    private TransactionService transactionSvc;

    @Autowired
    private HouseKeepingService houseKeepingSvc;
    @Autowired
    private GroupRepository groupRepository;

    @Autowired
    private UserService userSvc;

    @Autowired
    private CohortRepository cohortRepository;

    @Autowired
    private ScoreService scoreSvc;

    @Autowired
    private UserImageRepository userImageRepository;


    @Override
    public void saveStudentUsers(InputStream inputStream) throws IOException {


        // Fetch the group
        Optional<Group> optionalLearnerGroup = groupRepository.findByName(Group.LEARNER);
        Group learnerGroup = optionalLearnerGroup.orElseThrow(() ->
                new IllegalArgumentException("Group with name " + Group.LEARNER + " not found"));

        if (learnerGroup == null) {
            throw new MedITException("No learner group found.");
        }

        StudentReport report = convertToStudentReport(inputStream);
        List<User> students = convertToStudentUsers(report);
        Set<String> cohortNames = new HashSet<String>();

        for (User student : students) {
            // add user ONLY when the user does not exist to avoid duplications
            if (!userSvc.checkUserAlreadyExists(student.getUsername())) {
                student.addGroup(learnerGroup);
                userSvc.saveStudent(student);
                cohortNames.add(student.getCohortName());
            }else {
                log.error("Skipped to add the existing user:"+student.getUsername());
            }
        }

        if (!cohortNames.isEmpty()) {
            cohortRepository.deleteAll();
            for (String cohortName : cohortNames) {
                Cohort cohort = new Cohort();
                cohort.setCohortName(cohortName);
                cohortRepository.save(cohort);
            }
        }

        scoreSvc.claimOrphanedScores();

    }

    @Override
    public StudentReport convertToStudentReport(InputStream inputStream) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(inputStream, StudentReport.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<User> convertToStudentUsers(StudentReport report) {
        List<User> students = new ArrayList<User>();
        for (StudentReport.ReportData reportData : report.reportData) {
            students.addAll(StudentHelper.fromStudentReportData(reportData));
        }
        return students;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveAssessments(InputStream inputStream) throws IOException {
        EvaluationReport report = convertToEvaluationReport(inputStream);
        List<Assessment> assessments = convertToAssessments(report);
        List<Assessment> processedAssessments = new ArrayList<Assessment>();

        for (Assessment assessment : assessments) {
            try {
                Assessment saved = transactionSvc.saveAssessment(assessment);
                if (saved != null) {
                    processedAssessments.add(saved);
                }
            } catch (Exception e) {
                e.printStackTrace();
                String message = e.getMessage() + ", Remote ID = " + assessment.getRemoteId();
                log.error(FeedSourceTypeEnum.MEDSIS.name(), message);
            }
        }

        houseKeepingSvc.keep(processedAssessments);

    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveStudentImages(InputStream inputStream) throws IOException {

        ZipInputStream zip = null;
        ByteArrayOutputStream out = null;

        try {
            zip = new ZipInputStream(inputStream);
            ZipEntry entry;

            while ((entry = zip.getNextEntry()) != null) {
                out = new ByteArrayOutputStream();
                IOUtils.copy(zip, out);
                Optional<UserImage> userImageOptional = Optional.empty();
                userImageOptional = userImageRepository.findByImageName(entry.getName());
                if (!userImageOptional.isPresent()) {
                    UserImage userImage = new UserImage();
                    userImage.setImageName(entry.getName());
                    userImage.setImageData(out.toByteArray());
                    userImageRepository.save(userImage);
                    log.info("Saved image: " + entry.getName());
                } else{
                    log.info("Skipped for the pre-existing image file: " + entry.getName());
                }
                out.close();
            }
            zip.close();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
            if (zip != null) {
                try {
                    zip.close();
                } catch (IOException e) {
                }
            }
        }
    }

    @Override
    public EvaluationReport convertToEvaluationReport(InputStream inputStream) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(inputStream, EvaluationReport.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<Assessment> convertToAssessments(EvaluationReport report) {

        List<Assessment> assessments = new ArrayList<Assessment>();
        List<MedSisEvaluationImportException> errors = new ArrayList<>();
        for (EvaluationReport.ReportData reportData : report.reportData) {
            for (Evaluation evaluation : nullSafe(reportData.evaluations)) {
                try {
                    assessments.add(AssessmentHelper.fromEvaluation(reportData.formName, evaluation));
                } catch (MedSisEvaluationImportException e) {
                    errors.add(e);
                }
            }
            for (EvaluationBase evaluation : nullSafe(reportData.canceledEvaluations)) {
                try {
                    assessments.add(AssessmentHelper.fromEvaluationBase(reportData.formName, evaluation));
                } catch (MedSisEvaluationImportException e) {
                    errors.add(e);
                }
            }
        }
        handleImportErrors(errors);
        return assessments;

    }

    @Transactional(propagation = Propagation.REQUIRED)
    private void handleImportErrors(List<MedSisEvaluationImportException> errors) {
        if (errors == null || errors.isEmpty()) {
            return;
        }
        for (MedSisEvaluationImportException e : errors) {
            MedITException dce = new MedITException(e.getMessage() + ", " + e.getEvaluationId(), e);
            log.catching(dce);
            log.error(dce);
        }
    }


    public static <T> Collection<T> nullSafe(Collection<T> c) {
        return (c == null) ? Collections.emptyList() : c;
    }
}
