package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.CanMedAttributes;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.entity.CanMedsMapItem;

import java.util.ArrayList;
import java.util.List;

public class TransformerCanMedsRole {


//    @SuppressWarnings("unchecked")
//    public static JsonType toCanMedRoleNumeric(CanMedsActivityMap activitiesMap, CplanXid role,
//                                               List<CanMedsMapItem> canMapItems) {
//
//        JsonType canMedRole = new JsonType(role.getId(), JsonType.TYPE_CANMED_ROLE);
//
//        CanMedsActivityList list = activitiesMap.getActivityList(role.getId());
//        canMedRole.setAttributes(new CanMedAttributes(role, list));
//
//        JsonResponse keyCompetencyResp = getKeyCompetenciesNumeric(activitiesMap, role, canMapItems);
//
//        List<EnablingCompetencyJsonType> enablingCompetencies = getEnablingCompetencies(canMapItems, activitiesMap, keyCompetencyResp, true);
//
//        getEndOfSubsectionObjectivesNumeric(canMapItems, activitiesMap, enablingCompetencies);
//
//        getEndOfWeekObjectivesNumeric(canMapItems, activitiesMap, enablingCompetencies);
//
//        removeEmptyEnablingCompetencies((List<JsonType>) keyCompetencyResp.getData());
//
//        CanmedRelationship canmedRelationship = new CanmedRelationship();
//        canmedRelationship.setKeyCompetencies(keyCompetencyResp);
//
//        List<JsonType> activities = list.toJsonTypeListNumeric();
//        if (activities != null && !activities.isEmpty()) {
//            JsonResponse activitiesResp = new JsonResponse();
//            activitiesResp.setData(activities);
//            canmedRelationship.setActivities(activitiesResp);
//        }
//
//        canMedRole.setRelationships(canmedRelationship);
//        return canMedRole;
//    }
//
//    @SuppressWarnings("unchecked")
//    public static Object toCanMedRolePassFail(CanMedsActivityMap activitiesMap, CplanXid role,
//                                              List<CanMedsMapItem> canMapItems) {
//        JsonType canMedRole = new JsonType(role.getId(), JsonType.TYPE_CANMED_ROLE);
//
//        CanMedsActivityList list = activitiesMap.getActivityList(role.getId());
//        canMedRole.setAttributes(new CanMedAttributes(role, list));
//
//        JsonResponse keyCompetencyResp = getKeyCompetenciesPassFail(activitiesMap, role, canMapItems);
//
//        List<EnablingCompetencyJsonType> enablingCompetencies = getEnablingCompetencies(canMapItems, activitiesMap, keyCompetencyResp, false);
//
//        getEndOfSubsectionObjectivesPassFail(canMapItems, activitiesMap, enablingCompetencies);
//
//        getEndOfWeekObjectivesPassFail(canMapItems, activitiesMap, enablingCompetencies);
//
//        removeEmptyEnablingCompetencies((List<JsonType>) keyCompetencyResp.getData());
//
//        CanmedRelationship canmedRelationship = new CanmedRelationship();
//        canmedRelationship.setKeyCompetencies(keyCompetencyResp);
//
//        List<JsonType> activities = list.toJsonTypeListPassFail();
//        if (activities != null && !activities.isEmpty()) {
//            JsonResponse activitiesResp = new JsonResponse();
//            activitiesResp.setData(activities);
//            canmedRelationship.setActivities(activitiesResp);
//        }
//
//        canMedRole.setRelationships(canmedRelationship);
//        return canMedRole;
//    }
//
//    @SuppressWarnings("unchecked")
//    private static void removeEmptyEnablingCompetencies(List<JsonType> keyCompetencies) {
//        for (JsonType keyCompetency : keyCompetencies) {
//            KeyCompetencyRelationship keyCompetencyRelationship = (KeyCompetencyRelationship) keyCompetency.getRelationships();
//            List<EnablingCompetencyJsonType> enablingCompetencies = (List<EnablingCompetencyJsonType>) keyCompetencyRelationship.getEnablingCompetencies().getData();
//            for (Iterator<EnablingCompetencyJsonType> i = enablingCompetencies.iterator(); i.hasNext(); ){
//                EnablingCompetencyJsonType enablingCompetency = i.next();
//                if (enablingCompetency.getActivityList().size() == 0) {
//                    i.remove();
//                }
//            }
//        }
//    }
//
//    private static JsonResponse getKeyCompetenciesNumeric(CanMedsActivityMap activitiesMap, CplanXid role,
//                                                          List<CanMedsMapItem> canMapItems) {
//        JsonResponse keyCompetencyResp = new JsonResponse();
//        List<JsonType> keyCompetencies = new ArrayList<JsonType>();
//
//        for (CanMedsMapItem mapItem : canMapItems) {
///*			if (mapItem.getChildFrameworkDef().equals(FrameworkDef.PROGRAM_OBJECTIVES.name())
//					&& mapItem.getParentCplanXid().equals(role.getId()) && isKeyCompetency(mapItem.getLevel())) {*/
//
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.PROGRAM_OBJECTIVES.name())
//                    && mapItem.getParentCplanXid().equals(role.getId())
//                    && !mapItem.getChildCplanXid().equals(mapItem.getParentCplanXid())) {
//
//                JsonType keyCompetency = new JsonType(mapItem.getChildCplanXid(), JsonType.Type_KEY_COMPETENCY);
//
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                CanMedAttributes attributes = new CanMedAttributes();
//                attributes.setXid(mapItem.getChildValue());
//                attributes.setLabel(mapItem.getChildLabel());
//                attributes.setActivityCount(list.getActivityCount());
//                attributes.setQuestionCount(list.getQuestionCount());
//                keyCompetency.setAttributes(attributes);
//
//                KeyCompetencyRelationship relationship = new KeyCompetencyRelationship();
//                relationship.setEnablingCompetencies(new JsonResponse());
//                relationship.getEnablingCompetencies().setData(new ArrayList<JsonType>());
//                relationship.setEndOfSubsectionObjectives(new JsonResponse());
//                relationship.getEndOfSubsectionObjectives().setData(new ArrayList<JsonType>());
//                relationship.setEndOfWeekObjectives(new JsonResponse());
//                relationship.getEndOfWeekObjectives().setData(new ArrayList<JsonType>());
//                keyCompetency.setRelationships(relationship);
//
//                List<JsonType> activities = list.toJsonTypeListNumeric();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    relationship.setActivities(activitiesResp);
//                }
//                keyCompetencies.add(keyCompetency);
//            }
//        }
//        keyCompetencyResp.setData(keyCompetencies);
//        return keyCompetencyResp;
//    }
//
//    private static JsonResponse getKeyCompetenciesPassFail(CanMedsActivityMap activitiesMap, CplanXid role,
//                                                           List<CanMedsMapItem> canMapItems) {
//        JsonResponse keyCompetencyResp = new JsonResponse();
//        List<JsonType> keyCompetencies = new ArrayList<JsonType>();
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.PROGRAM_OBJECTIVES.name())
//                    && mapItem.getParentCplanXid().equals(role.getId())
//                    && !mapItem.getChildCplanXid().equals(mapItem.getParentCplanXid())) {
//                JsonType keyCompetency = new JsonType(mapItem.getChildCplanXid(), JsonType.Type_KEY_COMPETENCY);
//
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                CanMedAttributes attributes = new CanMedAttributes();
//                attributes.setXid(mapItem.getChildValue());
//                attributes.setLabel(mapItem.getChildLabel());
//                attributes.setActivityCount(list.getActivityCount());
//                attributes.setQuestionCount(list.getQuestionCount());
//                keyCompetency.setAttributes(attributes);
//
//                KeyCompetencyRelationship relationship = new KeyCompetencyRelationship();
//                relationship.setEnablingCompetencies(new JsonResponse());
//                relationship.getEnablingCompetencies().setData(new ArrayList<JsonType>());
//                relationship.setEndOfSubsectionObjectives(new JsonResponse());
//                relationship.getEndOfSubsectionObjectives().setData(new ArrayList<JsonType>());
//                relationship.setEndOfWeekObjectives(new JsonResponse());
//                relationship.getEndOfWeekObjectives().setData(new ArrayList<JsonType>());
//                keyCompetency.setRelationships(relationship);
//
//                List<JsonType> activities = list.toJsonTypeListPassFail();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    relationship.setActivities(activitiesResp);
//                }
//                keyCompetencies.add(keyCompetency);
//            }
//        }
//        keyCompetencyResp.setData(keyCompetencies);
//        return keyCompetencyResp;
//    }
//
//    private static List<EnablingCompetencyJsonType> getEnablingCompetencies(List<CanMedsMapItem> canMapItems,
//                                                                            CanMedsActivityMap activitiesMap, JsonResponse keyCompetencyResp, boolean isNumericScore) {
//        List<EnablingCompetencyJsonType> enablingCompetencies = new ArrayList<EnablingCompetencyJsonType>();
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.ENABLING_OBJECTIVES.name())) {
//                EnablingCompetencyJsonType enablingCompetency = new EnablingCompetencyJsonType(mapItem.getChildCplanXid(),
//                        JsonType.Type_ENABLING_COMPETENCY);
//                enablingCompetency.setNumericScore(isNumericScore);
//                enablingCompetency.setXid(mapItem.getChildValue());
//                enablingCompetency.setLabel(mapItem.getChildLabel());
//                enablingCompetency.setCanMedsActivityList(activitiesMap.getActivityList(mapItem.getChildCplanXid()));
//
//                updateKeyCompetencyResp(keyCompetencyResp, enablingCompetency, mapItem.getParentCplanXid());
//
//                enablingCompetencies.add(enablingCompetency);
//            }
//        }
//
//        return enablingCompetencies;
//    }
//
//    @SuppressWarnings("unchecked")
//    private static void updateKeyCompetencyResp(JsonResponse keyCompetencyResp, EnablingCompetencyJsonType enablingCompetency,
//                                                Long parentCplanXid) {
//        List<JsonType> keyCompetencies = (List<JsonType>) keyCompetencyResp.getData();
//        for (JsonType keyCompetency : keyCompetencies) {
//            if (!keyCompetency.getId().equals(parentCplanXid)) {
//                continue;
//            }
//            KeyCompetencyRelationship keyCompetencyRelationship = (KeyCompetencyRelationship) keyCompetency.getRelationships();
//            enablingCompetency.setKeyCompetencyRelationship(keyCompetencyRelationship);
//            List<JsonType> enablingCompetencies = (List<JsonType>) keyCompetencyRelationship.getEnablingCompetencies().getData();
//            enablingCompetencies.add(enablingCompetency);
//        }
//
//    }
//
//    private static void getEndOfSubsectionObjectivesNumeric(List<CanMedsMapItem> canMapItems,
//                                                            CanMedsActivityMap activitiesMap, List<EnablingCompetencyJsonType> enablingCompetencies) {
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.KEY_CONCEPT.name())) {
//                JsonType endOfSubsectionObjective = new JsonType(mapItem.getChildCplanXid(),
//                        JsonType.TYPE_END_OF_SUBSECTION_OBJECTIVE);
//
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                if (list == null || list.size() == 0) continue;
//
//                CanMedAttributes attribute = new CanMedAttributes();
//                attribute.setXid(mapItem.getChildValue());
//                attribute.setLabel(mapItem.getChildLabel());
//                attribute.setQuestionCount(list.getQuestionCount());
//                attribute.setActivityCount(list.getActivityCount());
//                endOfSubsectionObjective.setAttributes(attribute);
//
//                List<JsonType> activities = list.toJsonTypeListNumeric();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    EndOfSubsectionObjectiveRelationship relationship = new EndOfSubsectionObjectiveRelationship();
//                    relationship.setActivities(activitiesResp);
//                    endOfSubsectionObjective.setRelationships(relationship);
//                }
//
//                updateKeyCompetencyRelationship(enablingCompetencies, endOfSubsectionObjective,
//                        mapItem.getParentCplanXid());
//            }
//        }
//    }
//
//    private static void getEndOfSubsectionObjectivesPassFail(List<CanMedsMapItem> canMapItems,
//                                                             CanMedsActivityMap activitiesMap, List<EnablingCompetencyJsonType> enablingCompetencies) {
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.KEY_CONCEPT.name())) {
//                JsonType endOfSubsectionObjective = new JsonType(mapItem.getChildCplanXid(),
//                        JsonType.TYPE_END_OF_SUBSECTION_OBJECTIVE);
//
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                if (list == null || list.size() == 0) continue;
//
//                CanMedAttributes attribute = new CanMedAttributes();
//                attribute.setXid(mapItem.getChildValue());
//                attribute.setLabel(mapItem.getChildLabel());
//                attribute.setQuestionCount(list.getQuestionCount());
//                attribute.setActivityCount(list.getActivityCount());
//                endOfSubsectionObjective.setAttributes(attribute);
//
//                List<JsonType> activities = list.toJsonTypeListPassFail();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    EndOfSubsectionObjectiveRelationship relationship = new EndOfSubsectionObjectiveRelationship();
//                    relationship.setActivities(activitiesResp);
//                    endOfSubsectionObjective.setRelationships(relationship);
//                }
//
//                updateKeyCompetencyRelationship(enablingCompetencies, endOfSubsectionObjective,
//                        mapItem.getParentCplanXid());
//            }
//        }
//    }
//
//    @SuppressWarnings("unchecked")
//    private static void updateKeyCompetencyRelationship(List<EnablingCompetencyJsonType> enablingCompetencies,
//                                                        JsonType endOfSubsectionObjective, Long parentCplanXid) {
//
//        if (enablingCompetencies == null || enablingCompetencies.isEmpty()) {
//            return;
//        }
//
//        for (EnablingCompetencyJsonType enablingCompetency : enablingCompetencies) {
//            if (enablingCompetency.getId().equals(parentCplanXid)) {
//                List<JsonType> endOfSubsectionObjectives = (List<JsonType>) enablingCompetency.getKeyCompetencyRelationship().getEndOfSubsectionObjectives().getData();
//                for (JsonType jsonType : endOfSubsectionObjectives) {
//                    if (jsonType.getId().equals(endOfSubsectionObjective.getId())) return;
//                }
//                endOfSubsectionObjectives.add(endOfSubsectionObjective);
//                break;
//            }
//        }
//    }
//
//    private static void getEndOfWeekObjectivesNumeric(List<CanMedsMapItem> canMapItems,
//                                                      CanMedsActivityMap activitiesMap, List<EnablingCompetencyJsonType> enablingCompetencies) {
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.LEARNING_OUTCOMES.name())
//                    && mapItem.getParentFrameworkDef().equals(FrameworkDef.ENABLING_OBJECTIVES.name())) {
//                JsonType endOfWeekObjective = new JsonType(mapItem.getChildCplanXid(),
//                        JsonType.TYPE_END_OF_WEEK_OBJECTIVE);
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                if (list == null || list.size() == 0) continue;
//                CanMedAttributes attribute = new CanMedAttributes();
//                attribute.setXid(mapItem.getChildValue());
//                attribute.setLabel(mapItem.getChildLabel());
//                attribute.setActivityCount(list.getActivityCount());
//                attribute.setQuestionCount(list.getQuestionCount());
//                endOfWeekObjective.setAttributes(attribute);
//
//                List<JsonType> activities = list.toJsonTypeListNumeric();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    EndOfWeekObjectivesRelationship relationship = new EndOfWeekObjectivesRelationship();
//                    relationship.setActivities(activitiesResp);
//                    endOfWeekObjective.setRelationships(relationship);
//                }
//
//                updateKeyCompetencyEOWRelationship(enablingCompetencies, endOfWeekObjective,
//                        mapItem.getParentCplanXid());
//            }
//        }
//    }
//
//    private static void getEndOfWeekObjectivesPassFail(List<CanMedsMapItem> canMapItems,
//                                                       CanMedsActivityMap activitiesMap, List<EnablingCompetencyJsonType> enablingCompetencies) {
//
//        for (CanMedsMapItem mapItem : canMapItems) {
//            if (mapItem.getChildFrameworkDef().equals(FrameworkDef.LEARNING_OUTCOMES.name())
//                    && mapItem.getParentFrameworkDef().equals(FrameworkDef.ENABLING_OBJECTIVES.name())) {
//                JsonType endOfWeekObjective = new JsonType(mapItem.getChildCplanXid(),
//                        JsonType.TYPE_END_OF_WEEK_OBJECTIVE);
//                CanMedsActivityList list = activitiesMap.getActivityList(mapItem.getChildCplanXid());
//                if (list == null || list.size() == 0) continue;
//                CanMedAttributes attribute = new CanMedAttributes();
//                attribute.setXid(mapItem.getChildValue());
//                attribute.setLabel(mapItem.getChildLabel());
//                attribute.setActivityCount(list.getActivityCount());
//                attribute.setQuestionCount(list.getQuestionCount());
//                endOfWeekObjective.setAttributes(attribute);
//
//                List<JsonType> activities = list.toJsonTypeListPassFail();
//                if (activities != null && !activities.isEmpty()) {
//                    JsonResponse activitiesResp = new JsonResponse();
//                    activitiesResp.setData(activities);
//                    EndOfWeekObjectivesRelationship relationship = new EndOfWeekObjectivesRelationship();
//                    relationship.setActivities(activitiesResp);
//                    endOfWeekObjective.setRelationships(relationship);
//                }
//
//                updateKeyCompetencyEOWRelationship(enablingCompetencies, endOfWeekObjective,
//                        mapItem.getParentCplanXid());
//            }
//        }
//    }
//
//    @SuppressWarnings("unchecked")
//    private static void updateKeyCompetencyEOWRelationship(List<EnablingCompetencyJsonType> enablingCompetencies,
//                                                           JsonType endOfWeekObjective, Long parentCplanXid) {
//        if (enablingCompetencies == null || enablingCompetencies.isEmpty()) {
//            return;
//        }
//
//        for (EnablingCompetencyJsonType enablingCompetency : enablingCompetencies) {
//            if (enablingCompetency.getId().equals(parentCplanXid)) {
//                List<JsonType> endOfWeekObjectives = (List<JsonType>) enablingCompetency.getKeyCompetencyRelationship().getEndOfWeekObjectives().getData();
//                for (JsonType jsonType : endOfWeekObjectives) {
//                    if (jsonType.getId().equals(endOfWeekObjective.getId())) {
//                        return;
//                    }
//                }
//                endOfWeekObjectives.add(endOfWeekObjective);
//                break;
//            }
//        }
//    }

    private static boolean isKeyCompetency(int level) {
        return level == 2;
    }

    public static JsonResponse toCanMedsRolesJson(List<CanMedsMapItem> canMedsRoles) {
        JsonResponse json = new JsonResponse();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        for (CanMedsMapItem canMedsRole : canMedsRoles) {
            jsonTypes.add(toCanMedsRoleJsonType(canMedsRole));
        }
        json.setData(jsonTypes);
        return json;
    }

    private static JsonType toCanMedsRoleJsonType(CanMedsMapItem canMedsRole) {
        JsonType jsonType = new JsonType(canMedsRole.getChildCplanXid(), JsonType.TYPE_CANMED_ROLE);
        CanMedAttributes attributes = new CanMedAttributes();
        attributes.setXid(canMedsRole.getChildValue());
        attributes.setLabel(canMedsRole.getChildLabel());
        jsonType.setAttributes(attributes);
        return jsonType;
    }


}
