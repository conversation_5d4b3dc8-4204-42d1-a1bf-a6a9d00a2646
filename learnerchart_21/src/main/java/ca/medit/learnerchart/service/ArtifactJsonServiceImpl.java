package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.entity.CplanXid;
import ca.medit.learnerchart.entity.FocusedLearningPlan;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.repository.UserRepository;
import ca.medit.learnerchart.service.converter.TransformerCplanXidJson;
import ca.medit.learnerchart.service.converter.TransformerFocusedLearningPlanJson;
import ca.medit.learnerchart.service.converter.TransformerLearningActivityTypeJson;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Log4j2
public class ArtifactJsonServiceImpl implements ArtifactJsonService{

    @Autowired
    private CplanXidService cplanXidService;

    @Autowired
    private UserRepository userRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public JsonResponse getTextArtifactsForLearner(String utorId) {
        return null;
    }

    @Override
    public JsonResponse getFileArtifactsForLearner(String utorId) {
        return null;
    }

    @Override
    public JsonResponse getLinkArtifactsForLearner(String utorId) {
        return null;
    }

    @Override
    public JsonResponse addFileArtifactForLearner(JsonFileArtifact json) {
        return null;
    }

    @Override
    public JsonResponse addTextArtifactForLearner(String utorId, JsonTextArtifact textArtifact) {
        return null;
    }

    @Override
    public JsonResponse addLinkArtifactForLearner(String utorId, JsonLinkArtifact linkArtifact) {
        return null;
    }

    @Override
    public JsonResponse addFocusedLearningPlansForLearner(FocusedLearningPlanJsonFile jsonFile) {
        return null;
    }

    @Override
    public JsonResponse updateFocusedLearningPlanForLearner(FocusedLearningPlanJsonFile jsonFile) {
        return null;
    }

    @Override
    public JsonResponse updateFileArtifactForLearner(JsonFileArtifact json) {
        return null;
    }

    @Override
    public JsonResponse updateTextArtifactForLearner(JsonTextArtifact json) {
        return null;
    }

    @Override
    public JsonResponse updateLinkArtifactForLearner(JsonLinkArtifact json) {
        return null;
    }

    @Override
    public JsonResponse toggleCplanXidForLinkArtifact(String xid, Long linkArtifactId) {
        return null;
    }

    @Override
    public JsonResponse getFocusedLearningPlansForLearner(String utorId) {
        log.entry();
        JsonResponseWithIncluded jsonResponse = null;
      //  User learner = new AuthLearner(utorId);
        User learner = userRepository.findByUsername(utorId)
                .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + utorId));
        try {
            List<FocusedLearningPlan> focusedLearningPlans = getFocusedLearningPlanForLearner(learner);
            jsonResponse = TransformerFocusedLearningPlanJson.toFocusedLearningPlanJson(focusedLearningPlans, true);
            jsonResponse.setIncluded(getFocusedLearningPlanIncluded());
        } catch (Exception e) {
            throw new MedITException(e.getMessage(), e);
        }
        log.exit();
        return jsonResponse;
    }

    private List<FocusedLearningPlan> getFocusedLearningPlanForLearner(User learner) {
        log.entry();
        List<FocusedLearningPlan> focusedLearningPlans = null;
        try {
            focusedLearningPlans = entityManager.createQuery("select distinct f from FocusedLearningPlan f "
                                    + "left join fetch f.file left join fetch f.cplanXids where f.learner.utorid = :utorId",
                            FocusedLearningPlan.class).setParameter("utorId", learner.getUtorid())
                    .getResultList();
        }catch (Exception re) {
            throw new MedITException(
                    "Failed to retrieve list of focused leaerning plans for learner", re);
        }
        log.exit();
        return focusedLearningPlans;
    }

    private CplanXidIncluded getCplanXidIncluded() {
        List<CplanXid> cplanXids = cplanXidService.getCanMedsRolesCplanXids();
        CplanXidIncluded included = new CplanXidIncluded();
        included.setCplanXids(TransformerCplanXidJson.toCplanXidJson(cplanXids));
        return included;
    }

    private FocusedLearningPlanIncluded getFocusedLearningPlanIncluded() {
        FocusedLearningPlanIncluded included = new FocusedLearningPlanIncluded();
        included.setCplanXids(getCplanXidIncluded().getCplanXids());
        included.setActivityTypes(TransformerLearningActivityTypeJson.toLearningActivityTypeJson());
        return included;
    }
}
