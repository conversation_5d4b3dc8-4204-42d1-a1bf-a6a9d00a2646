package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.*;

import java.util.ArrayList;
import java.util.List;

public class TransformerActivityScoreJson {
    private static JsonType toActivityScoreJsonType(ActivityScoreEnum activityScore) {
        JsonType data = new JsonType(activityScore.name(), JsonType.TYPE_ACTIVITY_SCORE);
        ActivityScoreAttribute attributes = new ActivityScoreAttribute();
        attributes.setLabel(activityScore.getLabel());
        data.setAttributes(attributes);
        return data;
    }

    public static JsonResponse toPassFailActivityScoreJson(Class<ActivityScoreEnum> activityScoreEnum) {
        JsonResponse json = new JsonResponse();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        for (ActivityScoreEnum activityScore : ActivityScoreEnum.getPassFailValues()) {
            jsonTypes.add(toActivityScoreJsonType(activityScore));
        }
        json.setData(jsonTypes);
        return json;
    }

    public static JsonResponse getActivityScoreJsonByMode(AssessmentMode mode) {
        if (mode == AssessmentMode.WRITTEN || mode == AssessmentMode.PERFORMANCE_BASED) {
            return toNumericActivityScoreJson();
        }
        return toPassFailActivityScoreJson(ActivityScoreEnum.class);
    }

    private static JsonResponse toNumericActivityScoreJson() {
        JsonResponse json = new JsonResponse();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        jsonTypes.add(toActivityScoreJsonType(ActivityScoreEnum.NUMERIC));
        jsonTypes.add(toActivityScoreJsonType(ActivityScoreEnum.INCOMPLETE));
        json.setData(jsonTypes);
        return json;
    }

    public static JsonResponse toActivityScoreJson(String grade) {
        JsonResponse json = null;
        if (grade != null) {
            ActivityScoreEnum score = ActivityScoreEnum.valueOf(grade);
            if (score != null) {
                json = new JsonResponse();
                json.setData(toActivityScoreJsonType(score));
            }
        }
        return json;
    }

    public static JsonResponse toActivityScoreJson(List<String> grades) {
        String finalGrade = ActivityScoreEnum.UNABLE_TO_ASSESS.name();
        if (grades != null && !grades.isEmpty()) {
            for (String grade : grades) {
                if (ActivityScoreEnum.NOT_MEET_REQUIREMENTS.name().equals(grade)) {
                    finalGrade = grade;
                    break;
                }
                if (ActivityScoreEnum.MEET_REQUIREMENTS.name().equals(grade)) {
                    finalGrade = grade;
                }
            }
        }
        return toActivityScoreJson(finalGrade);
    }

    public static JsonResponse getActivityScoreJsonByExample(ActivityScoreEnum scoreEnum) {
        if (scoreEnum == null) {
            return null;
        } else if (scoreEnum == ActivityScoreEnum.NUMERIC || scoreEnum == ActivityScoreEnum.INCOMPLETE) {
            return toNumericActivityScoreJson();
        } else {
            return toPassFailActivityScoreJson(ActivityScoreEnum.class);
        }
    }


}
