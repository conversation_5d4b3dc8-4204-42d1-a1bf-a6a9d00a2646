package ca.medit.learnerchart.service.converter;

import ca.medit.learnerchart.domain.ActivityFeedbackAttribute;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.dto.SubmissionFeedback;

import java.util.ArrayList;
import java.util.List;

public class TransformerActivityFeedbackJson {


    public enum FeedbackType {

        ASSESSOR("Assessor"), ADJUDICATOR("Adjudicator");

        FeedbackType(String label) {
            this.label = label;
        }

        private String label;

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

    }

    private int assessorCount;

    private TransformerActivityFeedbackJson() {
    }

    private JsonType toActivityFeedbackJsonType(SubmissionFeedback feedback) {

        JsonType jsonType = new JsonType(feedback.getAssessmentId(), JsonType.TYPE_ACTIVITY_FEEDBACK);

        ActivityFeedbackAttribute attributes = new ActivityFeedbackAttribute();
        String feedbackText = feedback.getFeedback();
        if (feedbackText == null || feedbackText.isEmpty()) {
            attributes.setFeedbackText("No feedback available.");
        } else {
            attributes.setFeedbackText(feedbackText);
        }

        FeedbackType feedbackType = FeedbackType.valueOf(feedback.getAssessmentType());
        if (feedbackType != null) {
            switch (feedbackType) {
                case ASSESSOR:
                    assessorCount++;
                    attributes.setTitle(feedbackType.getLabel() + " " + assessorCount);
                    attributes.setFeedbackTextTitle(feedbackType.getLabel() + " Feedback");
                    break;
                case ADJUDICATOR:
                    attributes.setTitle(feedbackType.getLabel());
                    attributes.setFeedbackTextTitle(feedbackType.getLabel() + " Feedback");
                    break;
            }
        }

        attributes.setScore(TransformerActivityScoreJson.toActivityScoreJson(feedback.getGrade()));

        jsonType.setAttributes(attributes);
        return jsonType;
    }

    public static JsonResponse toActivityFeedbackJson(List<SubmissionFeedback> feedbacks) {
        JsonResponse json = new JsonResponse();
        List<JsonType> jsonTypes = new ArrayList<JsonType>();
        TransformerActivityFeedbackJson transformer = new TransformerActivityFeedbackJson();
        for (SubmissionFeedback feedback : feedbacks) {
            jsonTypes.add(transformer.toActivityFeedbackJsonType(feedback));
        }
        if (transformer.assessorCount == 1) {
            for (JsonType jsonType : jsonTypes) {
                ActivityFeedbackAttribute attributes = (ActivityFeedbackAttribute) jsonType.getAttributes();
                if (attributes.getTitle().equals(FeedbackType.ASSESSOR.getLabel() + " 1")) {
                    attributes.setTitle(FeedbackType.ASSESSOR.getLabel());
                }
                break;
            }
        }
        json.setData(jsonTypes);
        return json;
    }


}
