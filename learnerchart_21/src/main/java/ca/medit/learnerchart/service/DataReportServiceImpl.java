package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.DataReport;
import ca.medit.learnerchart.repository.ScoreRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataReportServiceImpl implements DataReportService{

     @Autowired
     private ScoreRepository scoreRepository;

    @Override
    public List<DataReport> getReportsByYearAndType(Integer year, String dataType) {
        List<Map<String, Object>> results = scoreRepository.findByYearAndDataType(year, dataType);

        if (results == null) {
            return Collections.emptyList();
        }

        return results.stream()
                .map(this::convertToDataReport)
                .collect(Collectors.toList());
    }

    @Override
    public List<DataReport> getReportsByProgramYearAndType(Integer year, Integer programYr, String dataType) {
        List<Map<String, Object>> results = scoreRepository.findByProgramYearAndDataType(year, programYr, dataType);

        if (results == null) {
            return Collections.emptyList();
        }

        return results.stream()
                .map(this::convertToDataReport)
                .collect(Collectors.toList());
    }

    private DataReport convertToDataReport(Map<String, Object> row) {
        DataReport report = new DataReport();
        report.setAssessmentId(((Number) row.get("assessmentId")).longValue());
        report.setName((String) row.get("name"));
        report.setAssessmentType((String) row.get("assessmentType"));
        report.setScoreId(((Number) row.get("scoreId")).longValue());
        report.setUtorid((String) row.get("utorid"));
        report.setPointsAvailable(row.get("pointsAvailable") != null ? ((Number) row.get("pointsAvailable")).doubleValue() : null);
        report.setPointsEarned(row.get("pointsEarned") != null ? ((Number) row.get("pointsEarned")).doubleValue() : null);
        report.setRawValue((String) row.get("rawValue"));
        report.setCplanXid((String) row.get("cplanXid"));
        report.setNumberOfCorrect(row.get("numberOfCorrect") != null ? ((Number) row.get("numberOfCorrect")).intValue() : null);
        report.setNumberOfItems(row.get("numberOfItems") != null ? ((Number) row.get("numberOfItems")).intValue() : null);
        report.setParentRawValue((String) row.get("parentRawValue"));
        report.setParentName((String) row.get("parentName"));
        report.setStudentNumber((Long) row.get("studentNumber"));
        report.setReference((String) row.get("reference"));
        return report;
    }


}
