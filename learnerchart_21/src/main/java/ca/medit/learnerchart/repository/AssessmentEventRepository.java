package ca.medit.learnerchart.repository;

import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.AssessmentEvent;
import ca.medit.learnerchart.entity.AssessmentEventKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface AssessmentEventRepository extends JpaRepository<AssessmentEvent, AssessmentEventKey> {

    List<AssessmentEvent> findByEventKeyRemoteId(Long remoteId);

    void deleteByEventKey(AssessmentEventKey eventKey);

    @Query("SELECT a.eventKey FROM AssessmentEvent a WHERE a.eventKey.source = :source AND a.eventKey.remoteId = :remoteId")
    List<AssessmentEventKey> getAssessmentEventKeysByRemoteId(@Param("source") FeedSourceTypeEnum source, @Param("remoteId") long remoteId);

    // Method to delete an AssessmentEvent by its composite key
    @Transactional
    void deleteById(AssessmentEventKey assessmentEventKey);

    @Modifying
    @Transactional
    @Query("DELETE FROM AssessmentEvent ae WHERE ae.eventKey.source = :source AND ae.eventKey.remoteId = :remoteId")
    void deleteByRemoteId(@Param("source") FeedSourceTypeEnum source, @Param("remoteId") long remoteId);

}

