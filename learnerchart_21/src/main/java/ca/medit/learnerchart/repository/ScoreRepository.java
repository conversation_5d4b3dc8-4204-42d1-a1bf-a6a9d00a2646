package ca.medit.learnerchart.repository;

import ca.medit.learnerchart.entity.Score;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Repository
public interface ScoreRepository extends JpaRepository<Score, Long> {

    @Modifying
    @Transactional
    @Query(value = "INSERT INTO AUTHUSER_SCORE (AUTHUSER_ID, SCORE_ID) " +
            "SELECT DISTINCT u.USERS_ID, s.SCORE_ID FROM SCORE s " +
            "JOIN USERS u ON u.UTORID = s.UTORID OR u.STUDENT_NUMBER = s.STUDENT_NUMBER " +
            "WHERE s.SCORE_ID NOT IN (SELECT SCORE_ID FROM AUTHUSER_SCORE)", nativeQuery = true)
    int insertOwnersForOrphanedScores();

    @Modifying
    @Transactional
    @Query(value = "UPDATE s " +
            "SET s.cplan_xid_id = c.cplan_xid_id " +
            "FROM score s " +
            "JOIN cplan_xid c ON s.cplan_xid = c.value " +
            "WHERE s.cplan_xid IS NOT NULL " +
            "AND (s.cplan_xid_id != c.cplan_xid_id OR s.cplan_xid_id IS NULL) " +
            "AND s.cplan_xid NOT IN ('FINAL_SCORE_XID', 'TOTAL_SCORE_XID', 'ABSENTEE_XID')",
            nativeQuery = true)
    int createScoreCplanXidLinks();

    @Query(value = "select a.ASSESSMENT_ID as assessmentId, a.NAME as name, a.ASSESSMENT_TYPE as assessmentType, " +
            "s.SCORE_ID as scoreId, s.UTORID as utorid, s.POINTS_AVAILABLE as pointsAvailable, " +
            "s.POINTS_EARNED as pointsEarned, s.RAW_VALUE as rawValue, s.CPLAN_XID as cplanXid, " +
            "s.NUMBER_OF_CORRECT as numberOfCorrect, s.NUMBER_OF_ITEMS as numberOfItems, " +
            "s.PARENT_RAW_VALUE as parentRawValue, s.PARENT_NAME as parentName, " +
            "s.STUDENT_NUMBER as studentNumber, s.REFERENCE as reference " +
            "FROM SCORE s " +
            "INNER JOIN ASSESSMENT a ON s.ASSESSMENT_ID = a.ASSESSMENT_ID " +
            "WHERE (a.ASSESSMENT_TYPE = :dataType OR a.ASSESSMENT_MODE = :dataType) " +
            "AND a.ACADEMIC_YEAR = :year AND s.DELETED = 0 AND a.DELETED = 0" +
            "ORDER BY s.SCORE_ID", nativeQuery = true)
    List<Map<String, Object>> findByYearAndDataType(@Param("year") Integer year, @Param("dataType") String dataType);

    @Query(value = "select a.ASSESSMENT_ID as assessmentId, a.NAME as name, a.ASSESSMENT_TYPE as assessmentType, " +
            "s.SCORE_ID as scoreId, s.UTORID as utorid, s.POINTS_AVAILABLE as pointsAvailable, " +
            "s.POINTS_EARNED as pointsEarned, s.RAW_VALUE as rawValue, s.CPLAN_XID as cplanXid, " +
            "s.NUMBER_OF_CORRECT as numberOfCorrect, s.NUMBER_OF_ITEMS as numberOfItems, " +
            "s.PARENT_RAW_VALUE as parentRawValue, s.PARENT_NAME as parentName, " +
            "s.STUDENT_NUMBER as studentNumber, s.REFERENCE as reference " +
            "FROM SCORE s " +
            "INNER JOIN ASSESSMENT a ON s.ASSESSMENT_ID = a.ASSESSMENT_ID " +
            "WHERE (a.ASSESSMENT_TYPE = :dataType OR a.ASSESSMENT_MODE = :dataType) " +
            "AND a.ACADEMIC_YEAR = :year AND a.PROGRAM_YEAR = :programYear AND s.DELETED = 0 AND a.DELETED = 0" +
            "ORDER BY s.SCORE_ID", nativeQuery = true)
    List<Map<String, Object>> findByProgramYearAndDataType(@Param("year") Integer year, @Param("programYear") Integer programYear, @Param("dataType") String dataType);
}
