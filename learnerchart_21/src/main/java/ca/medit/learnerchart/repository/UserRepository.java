package ca.medit.learnerchart.repository;

import ca.medit.learnerchart.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

        Optional<User> findByUsername(String username);
        Optional<User> findByUtorid(String utorid);

        // Custom query method to find User by studentNumber
        Optional<User> findByStudentNumber(Long studentNumber);

        @Query("SELECT s.utorid from User s join s.groups g where g.name = :adminGroupName")
        List<String> fetchAdmins(String adminGroupName);

        @Query(value = "select s.UTORID, l.UTORID from USERS s join group_members g on s.users_id = g.USERS_ID " +
                "join AUTHUSER_SCHOLAR_FOR u on s.users_id  = u.USERS_ID join users l on l.users_id = u.LEARNERCHART_USERS_ID " +
                "join groups r on g.GROUP_ID = r.GROUP_ID where r.NAME = :groupName", nativeQuery = true)
        List<Object[]> fetchScholarsWithLearners(@Param("groupName") String groupName);

        @Query(value = "select s.UTORID, l.UTORID from USERS s join group_members g on s.users_id = g.USERS_ID " +
                "join AUTHUSER_SUPPORTER_FOR u on s.users_id  = u.USERS_ID join users l on l.users_id = u.LEARNERCHART_USERS_ID " +
                "join groups r on g.GROUP_ID = r.GROUP_ID where r.NAME = :groupName", nativeQuery = true)
        List<Object[]> fetchSupportersWithLearners(@Param("groupName") String groupName);
}

