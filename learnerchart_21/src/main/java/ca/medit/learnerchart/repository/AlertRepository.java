package ca.medit.learnerchart.repository;

import ca.medit.learnerchart.entity.Alert;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface AlertRepository extends JpaRepository<Alert, Long> {

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM ALERT WHERE ALERT_TYPE = 'ASSESSMENT_FEED'", nativeQuery = true)
    int deleteAssessmentFeedAlerts();

    @Modifying
    @Transactional
    @Query(value = "UPDATE ALERT SET DELETED = 1 WHERE createdon < DATEADD(day, -30, CURRENT_TIMESTAMP)", nativeQuery = true)
    int markOldAlertsAsDeleted();

    @Modifying
    @Transactional
    @Query(value = "WITH UniqueCombinations AS ( "
            + "    SELECT UTORID, PARENT_NAME "
            + "    FROM dbo.SCORE "
            + "    WHERE CPLAN_XID = 'FINAL_SCORE_XID' "
            + "      AND source = 'OSCE' "
            + "    GROUP BY UTORID, PARENT_NAME "
            + "    HAVING COUNT(*) = 1 "
            + "), "
            + "INSERT INTO ALERT (users_id, reference_id, type, title, assessment_mode, alert_type, alert_date, year_id, deleted, optlock) "
            + "SELECT x.authuser_id, x.assessment_id, CASE x.title WHEN 'Missed' THEN 'Missed assessment' ELSE x.assessment_type END AS assessment_type, "
            + "       CASE x.title WHEN 'Missed' THEN x.title + ' ' +  x.assessment_type ELSE x.title END AS title, "
            + "       x.assessment_mode, x.alert_type, x.updated_date, x.year_id, x.deleted, x.optlock "
            + "FROM ( "
            + "    (SELECT DISTINCT aus.authuser_id, a.assessment_id, "
            + "            CASE a.assessment_mode "
            + "                WHEN 'ASSIGNMENT' THEN CASE a.assessment_type "
            + "                                       WHEN 'PROGRESS_REVIEW' THEN :progressReviewType "
            + "                                       ELSE :assignmentType END "
            + "                WHEN 'EVALUATION' THEN :evaluationType "
            + "                ELSE CASE a.assessment_type "
            + "                        WHEN 'MASTERY_EXERCISE' THEN :masteryExerciseType "
            + "                        WHEN 'WEEKLY_EXERCISE' THEN :weeklyExerciseType "
            + "                        WHEN 'BELL_RINGER' THEN :bellRingerType "
            + "                        WHEN 'PROGRESS_TEST' THEN :progressTest "
            + "                        WHEN 'EPA_SUMMARIES' THEN :epaSummaries "
            + "                        ELSE a.assessment_type END "
            + "            END AS assessment_type, "
            + "            CASE s.cplan_xid "
            + "                WHEN 'ABSENTEE_XID' THEN 'Missed' "
            + "                ELSE CASE a.assessment_type "
            + "                        WHEN 'PROGRESS_REVIEW' THEN :progressReviewTitle "
            + "                        ELSE a.name END "
            + "            END AS title, "
            + "            a.assessment_mode, 'ASSESSMENT_FEED' AS alert_type, "
            + "            CASE WHEN ae.event_date IS NULL THEN a.updatedon ELSE ae.event_date END AS updated_date, "
            + "            (a.academic_year * 10) + a.program_year AS year_id, 0 AS deleted, 0 AS optlock "
            + "     FROM assessment a "
            + "     JOIN dbo.score s ON a.assessment_id = s.assessment_id "
            + "     JOIN dbo.authuser_score aus ON s.score_id = aus.score_id "
            + "     LEFT JOIN assessment_event ae ON ae.remote_id = a.remote_id AND ae.source = a.source AND ae.utorid = s.utorid "
            + "     WHERE a.assessment_type <> 'OSCE') "
            + "UNION ( "
            + "    SELECT aus.authuser_id, MAX(a.assessment_id), a.assessment_type, t.title, a.assessment_mode, "
            + "           'ASSESSMENT_FEED' AS alert_type, MAX(a.updatedon), "
            + "           (a.academic_year * 10) + a.program_year AS year_id, 0 AS deleted, 0 AS optlock "
            + "    FROM assessment a "
            + "    JOIN dbo.score s ON a.assessment_id = s.assessment_id "
            + "    JOIN dbo.authuser_score aus ON s.score_id = aus.score_id "
            + "    JOIN (SELECT s2.SCORE_ID, CASE s2.RAW_VALUE WHEN 'INCOMPLETE' THEN 'Missed' ELSE s2.PARENT_NAME END AS title "
            + "          FROM dbo.SCORE s2 "
            + "          JOIN UniqueCombinations uc ON s2.UTORID = uc.UTORID AND s2.PARENT_NAME = uc.PARENT_NAME "
            + "          WHERE s2.CPLAN_XID = 'FINAL_SCORE_XID') AS t ON t.score_id=s.SCORE_ID "
            + "    WHERE a.assessment_type = 'OSCE' "
            + "    GROUP BY aus.AUTHUSER_ID, a.assessment_type, a.assessment_mode, a.ACADEMIC_YEAR, a.PROGRAM_YEAR, s.PARENT_NAME, t.title) "
            + ") x "
            + "WHERE x.updated_date >= DATEADD(day, -30, CURRENT_TIMESTAMP) "
            + "ORDER BY x.authuser_id, x.updated_date DESC",
            nativeQuery = true)
    int insertAssessmentFeedAlerts(String progressReviewType, String assignmentType, String evaluationType,
                                   String masteryExerciseType, String weeklyExerciseType, String bellRingerType,
                                   String progressTest, String epaSummaries, String progressReviewTitle);
}
