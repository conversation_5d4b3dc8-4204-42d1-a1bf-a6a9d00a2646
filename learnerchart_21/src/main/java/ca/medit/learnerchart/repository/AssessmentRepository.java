package ca.medit.learnerchart.repository;

import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface AssessmentRepository extends JpaRepository<Assessment, Long> {

    Optional<Assessment> findByName(String name);

    @Query("SELECT a FROM Assessment a")
    List<Assessment> getListOfAssessments();

    // Method 1: Find Assessment by RemoteId and Source
    @Query("SELECT a FROM Assessment a WHERE a.remoteId = :remoteId AND a.source = :source")
    Assessment findByRemoteIdAndSource(long remoteId, FeedSourceTypeEnum source);

    // Method 2: Delete Assessment by RemoteId and Source
    @Transactional
    @Modifying
    @Query(value = """
        DELETE u FROM authuser_score u
        INNER JOIN score s ON u.score_id = s.score_id
        INNER JOIN assessment a ON s.assessment_id = a.assessment_id
        WHERE a.source = :source AND a.remote_id = :remoteId
        """, nativeQuery = true)
    void deleteAuthUserScore(long remoteId, String source);

    @Transactional
    @Modifying
    @Query(value = """
        DELETE s FROM score s
        INNER JOIN assessment a ON s.assessment_id = a.assessment_id
        WHERE a.source = :source AND a.remote_id = :remoteId
        """, nativeQuery = true)
    void deleteScores(long remoteId, String source);

    @Transactional
    @Modifying
    @Query(value = """
        DELETE a FROM assessment a
        WHERE a.source = :source AND a.remote_id = :remoteId
        """, nativeQuery = true)
    void deleteAssessment(long remoteId, String source);

    // Method 3: Get MedSis Assessment IDs Map
    @Query(value = """
        SELECT a.remote_id, a.assessment_id FROM assessment a WHERE a.source = 'MEDSIS'
        """, nativeQuery = true)
    List<Object[]> getMedsisAssessmentIdsMap();

}
