package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "CANMEDS_MAP")
public class CanmedsMap {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CANMEDS_MAP_ID")
    private Long canmedsMapId;

    @ManyToOne
    @JoinColumn(name = "CPLAN_XID_ID_CHILD", referencedColumnName = "CPLAN_XID_ID", nullable = false)
    private CplanXid cplanXidChild;

    @ManyToOne
    @JoinColumn(name = "CPLAN_XID_ID_PARENT", referencedColumnName = "CPLAN_XID_ID", nullable = false)
    private CplanXid cplanXidParent;

    @ManyToOne
    @JoinColumn(name = "CPLAN_XID_TOP", referencedColumnName = "CPLAN_XID_ID")
    private CplanXid cplanXidTop;
}
