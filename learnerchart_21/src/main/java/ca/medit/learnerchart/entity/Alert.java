package ca.medit.learnerchart.entity;

import ca.medit.learnerchart.domain.AlertType;
import ca.medit.learnerchart.domain.AssessmentMode;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.LocalDateTime;


@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "alert")
public class Alert {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "alert_id")
    private Long alertId;

    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private AlertType alertType;
    private LocalDateTime alertDate;
    private AssessmentMode assessmentMode;
    private String externalLink;
    private String message;
    private Long referenceId;
    private String title;
    private Integer yearId;
    private String type;

    @ManyToOne
    @JoinColumn(name = "USERS_ID", referencedColumnName = "USERS_ID", nullable = false)
    private User user;


}
