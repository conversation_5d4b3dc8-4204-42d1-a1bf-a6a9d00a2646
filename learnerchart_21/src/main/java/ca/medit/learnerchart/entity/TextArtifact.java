package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "text_artifact")
public class TextArtifact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "text_artifact_id")
    private Long textArtifactId;
    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private String name;
    private String text;
    @ManyToOne
    @JoinColumn(name = "USERS_ID", referencedColumnName = "USERS_ID", nullable = false)
    private User user;
    private Integer programYear;
    private Integer academicYear;
//    private Set<CplanXid> cplanXids = new HashSet<CplanXid>();
//
//    @ManyToMany(fetch = FetchType.LAZY)
//    @JoinTable(name = "TEXT_ARTIFACT_XID", joinColumns = { @JoinColumn(name = "TEXT_ARTIFACT_ID") }, inverseJoinColumns = {
//            @JoinColumn(name = "CPLAN_XID_ID") })
//    public Set<CplanXid> getCplanXids() {
//        return cplanXids;
//    }
//
//    public void setCplanXids(Set<CplanXid> cplanXids) {
//        if (cplanXids != null) {
//            this.cplanXids = cplanXids;
//        }
//    }
//
//    public void removeCplanXids() {
//        this.cplanXids.clear();
//        this.cplanXids = null;
//    }
//
//    public void addCplanXid(CplanXid cplanXid) {
//        this.cplanXids.add(cplanXid);
//    }



}
