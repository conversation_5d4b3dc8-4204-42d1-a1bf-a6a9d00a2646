package ca.medit.learnerchart.entity;


import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@ToString(exclude = "assessment") // Exclude assessment from toString
@EqualsAndHashCode(exclude = "assessment") // Exclude assessment from equals and hashCode
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "score")
public class Score {

//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @Column(name = "upload_id")
//    private Long uploadId;
//
//    private String programYear;
//    private String academicYear;
//
//    private Double score;
//
//    private Long studentNumber;
//
//    private LocalDateTime uploadedOn;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "score_id")
    private Long scoreId;
    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    @ManyToOne
    @JoinColumn(name = "CPLAN_XID_ID", referencedColumnName = "CPLAN_XID_ID", nullable = true)
    private CplanXid cplanXidObj;
    private String cplanXid;
    private String feedback;
    private Float numberOfCorrect;
    private Float numberOfItems;
    private Float pointsAvailable;
    private Float pointsEarned;
    private String rawValue;
    private String reference;
    private String scoreType;
    private String utorid;
//    @ManyToOne
//    @JoinColumn(name = "ASSESSMENT_ID", referencedColumnName = "ASSESSMENT_ID", nullable = false)
//    @ToString.Exclude
//    @JsonIgnore // Prevent serialization
//    private Assessment assessment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ASSESSMENT_ID", referencedColumnName = "ASSESSMENT_ID", nullable = false)
    @JsonIgnore // Prevent recursion during serialization
    private Assessment assessment;


    private String parentRawValue;
    private String parentName;
    private Long studentNumber;
    @Enumerated(EnumType.STRING)
    private FeedSourceTypeEnum source;

  //  @JoinTable(name = "AUTHUSER_SCORE", joinColumns = @JoinColumn(name = "SCORE_ID", nullable = true), inverseJoinColumns = @JoinColumn(name = "AUTHUSER_ID"))
    @ManyToOne
    @JoinTable(
            name = "AUTHUSER_SCORE",  // Join table name
            joinColumns = @JoinColumn(name = "score_id"),  // Column in join table related to Score
            inverseJoinColumns = @JoinColumn(name = "AUTHUSER_ID")  // Column in join table related to User
    )
    private User authUser;


}


