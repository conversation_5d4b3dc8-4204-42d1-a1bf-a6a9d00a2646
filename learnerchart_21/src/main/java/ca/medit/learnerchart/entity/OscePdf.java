package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Blob;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "osce_pdf")
public class OscePdf {

    @Id
    @Column(name = "osce_name", columnDefinition = "VARCHAR")
    private String osceName;

    private Blob osceData;

}
