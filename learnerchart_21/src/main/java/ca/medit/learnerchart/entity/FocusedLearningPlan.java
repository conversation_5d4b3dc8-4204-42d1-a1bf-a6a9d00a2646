package ca.medit.learnerchart.entity;

import ca.medit.learnerchart.domain.LearningActivityTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "focused_learning_plan")
public class FocusedLearningPlan {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "focused_learning_plan_id")
    private Long focusedLearningPlanId;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "FILE_ID", referencedColumnName = "FILE_ID", nullable = false, unique = true)
    protected File file;

    @ManyToOne
    @JoinColumn(name = "USERS_ID", referencedColumnName = "USERS_ID", nullable = false)
    protected User learner;
    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private Integer programYear;
    private Integer academicYear;
    @Enumerated(EnumType.STRING)
    private LearningActivityTypeEnum activityType;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinTable(
            name = "focused_learning_plan_xid",
            joinColumns = @JoinColumn(name = "focused_learning_plan_id"),
            inverseJoinColumns = @JoinColumn(name = "cplan_xid_id")
    )
    @ToString.Exclude
    @JsonIgnore // Prevent serialization
    private Set<CplanXid> cplanXids = new HashSet<>();


//    protected Set<CplanXid> cplanXids = new HashSet<CplanXid>();
//    public void removeCplanXids() {
//        this.cplanXids.clear();
//        this.cplanXids = null;
//    }
//    public void addCplanXid(CplanXid cplanXid) {
//        this.cplanXids.add(cplanXid);
//    }

}
