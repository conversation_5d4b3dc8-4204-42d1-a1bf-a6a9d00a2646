package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Blob;
import java.time.LocalDateTime;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "FILE_OBJECT")
public class File {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "file_id")
    private Long fileId;

    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private Blob fileData;
    private String fileName;
    private Long fileSize;

    @Transient
    public String getFileExtension() {
        if (fileName != null) {
            int index = fileName.lastIndexOf(".");
            if (index != -1) {
                return fileName.substring(index + 1);
            }
        }
        return null;
    }

}
