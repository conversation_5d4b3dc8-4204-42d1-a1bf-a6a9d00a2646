package ca.medit.learnerchart.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "users")
public class User {

    @Id
   // @GeneratedValue(strategy = GenerationType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "users_id")
    private Long usersId;
    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;

    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private Boolean accountNonExpired;
    private Boolean accountNonLocked;
    private String cohortName;
    private Boolean credentialsNonExpired;
    private String email;
    private Boolean enabled;
    private Integer failedLoginAttempts;
    private String firstName;
    private String lastName;
    private String middleName;
    private String password;
    private String salt;
    private Long userSessionId;
    private String username;
    private String utorid;
    private Long pseudoAuthuserId;
    private Long studentNumber;
    private Integer yearOfStudy;
    @Lob
    private byte[] profilePhoto;
    private String pictureName;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "group_members",
            joinColumns = @JoinColumn(name = "users_id"),
            inverseJoinColumns = @JoinColumn(name = "group_id")
    )
    @ToString.Exclude
    @JsonIgnore // Prevent serialization
    private Set<Group> groups = new HashSet<>();


    public void addGroup(Group group) {
        if (groups == null) {
            groups = new HashSet<>();
        }
        // group.addUser(this);
        groups.add(group);
    }


}
