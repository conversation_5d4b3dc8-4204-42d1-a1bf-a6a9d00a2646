package ca.medit.learnerchart.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "groups")
public class Group {

    public static final String LEARNER = "Learner";
    public static final String LEARNER_SUPPORTER = "Learner-supporter";
    public static final String SCHOLAR = "Scholar";
    public static final String PROMOTION_COMMITTEE = "Promotion-committee";
    public static final String SYSTEM_ADMINISTRATORS = "System-Administrator";
    public static final String UME_ADMINISTRATOR = "UME-Administrator";
    public static final String PSEUDO_ADMIN = "Pseudo-Admin";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "group_id")
    private Long groupId;
    private String name;
    //private Set<Authority> authorities = new HashSet<Authority>();

    //private Set<AuthUser> users = new HashSet<AuthUser>();

    @ManyToMany(mappedBy = "groups")
    @ToString.Exclude
    @JsonIgnore // Prevent serialization
    private Set<User> users = new HashSet<>();



    /**
     *
     * @param authUser
     */
    public void addUser(User authUser) {
        if (users == null) {
            users = new HashSet<>();
        }
        users.add(authUser);
    }

}
