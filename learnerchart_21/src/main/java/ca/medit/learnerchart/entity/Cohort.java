package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "COHORT")
public class Cohort {
        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        @Column(name = "cohort_id")
        private Long cohortId;
        private String cohortName;

}
