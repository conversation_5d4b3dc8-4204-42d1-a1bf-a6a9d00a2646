package ca.medit.learnerchart.entity;

import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Embeddable
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssessmentEventKey implements Serializable {

    @Column(name = "REMOTE_ID")
    private Long remoteId;

    @Enumerated(EnumType.STRING)
    private FeedSourceTypeEnum source;

    private String utorid;

    @Override
    public int hashCode() {
        return Objects.hash(remoteId, source, utorid);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AssessmentEventKey that = (AssessmentEventKey) obj;
        return Objects.equals(remoteId, that.remoteId) &&
                source == that.source &&
                Objects.equals(utorid, that.utorid);
    }
}

