package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "CPLAN_XID", uniqueConstraints = {
        @UniqueConstraint(columnNames = "VALUE")
})
public class CplanXid {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CPLAN_XID_ID")
    private Long cplanXidId;

    @Column(name = "CREATEDBY")
    private String createdBy;

    @Column(name = "CREATEDON")
    private LocalDateTime createdOn;

    @Column(name = "DELETED")
    private Boolean deleted;

    @Column(name = "OPTLOCK", nullable = false)
    private Long optLock;

    @Column(name = "UPDATEDBY")
    private String updatedBy;

    @Column(name = "UPDATEDON")
    private LocalDateTime updatedOn;

    @Column(name = "FRAMEWORK_DEF")
    private String frameworkDef;

    @Column(name = "LABEL", length = 8000)
    private String label;

    @Column(name = "REMOTE_ID")
    private Long remoteId;

    @Column(name = "VALUE", length = 8000, unique = true)
    private String value;

    public static final String FINAL_SCORE_XID = "FINAL_SCORE_XID";

}
