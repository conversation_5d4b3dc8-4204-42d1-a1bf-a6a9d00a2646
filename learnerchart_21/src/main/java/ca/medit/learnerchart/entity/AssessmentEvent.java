package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "assessment_event")
public class AssessmentEvent {

        @EmbeddedId
        private AssessmentEventKey eventKey;

        private LocalDateTime eventDate = LocalDateTime.now();

}


