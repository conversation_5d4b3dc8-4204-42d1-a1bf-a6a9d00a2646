package ca.medit.learnerchart.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "upload_record")
public class UploadRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "upload_id")
    private Long uploadId;

    private String programYear;
    private String academicYear;

    @ManyToOne
    @JoinColumn(name = "results_file_id")
    private UploadedFile resultsFile;

    @ManyToOne
    @JoinColumn(name = "zip_file_id")
    private UploadedFile zipFile;

    private LocalDateTime uploadedOn;

}
