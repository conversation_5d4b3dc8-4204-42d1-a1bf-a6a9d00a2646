package ca.medit.learnerchart.entity;


import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
//import org.joda.time.LocalDateTime;
import java.time.LocalDateTime;


import java.util.HashSet;
import java.util.Set;


@Entity
@Getter
@Setter
@ToString(exclude = "scores") // Exclude scores from toString to prevent circular reference
@EqualsAndHashCode(exclude = "scores") // Exclude scores from equals and hashCode
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "assessment")
public class Assessment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "assessment_id")
    private Long assessmentId;
    private String createdby;
    private LocalDateTime createdon;
    private Boolean deleted;
    private Long optlock;
    private String updatedby;
    private LocalDateTime updatedon;
    private String assessmentType;
    private LocalDateTime dueDate;
    private String name;
    private Integer programYear;
    private Long remoteId;
    private String courseCode;
    private String supervisorFirstName;
    private String supervisorLastName;
    private Integer academicYear;
    private LocalDateTime releaseDate;
    @Enumerated(EnumType.STRING)
    private FeedSourceTypeEnum source;
    @Enumerated(EnumType.STRING)
    private AssessmentMode assessmentMode;


    @OneToMany(mappedBy = "assessment", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference // Prevent recursion during serialization
    private Set<Score> scores = new HashSet<>();

}
