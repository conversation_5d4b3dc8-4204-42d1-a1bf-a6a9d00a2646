DROP TABLE IF EXISTS USERS;

CREATE TABLE USERS (
   users_id bigint IDENTITY(1,1) NOT NULL,
   CREATEDBY varchar(255)  NULL,
   CREATEDON datetime2 NULL,
   DELETED bit NULL,
   OPTLOCK bigint NOT NULL,
   UPDATEDBY varchar(255)  NULL,
   UPDATEDON datetime2 NULL,
   account_non_expired bit NULL,
   account_non_locked bit NULL,
   COHORT_NAME varchar(255)  NULL,
   credentials_non_expired bit NULL,
   email varchar(255)  NULL,
   enabled bit NULL,
   failed_login_attempts int NOT NULL,
   first_name varchar(255)  NULL,
   last_name varchar(255)  NULL,
   middle_name varchar(255)  NULL,
   password varchar(255) NOT NULL,
   PICTURE_NAME varchar(255) NULL,
   salt varchar(255) NULL,
   user_session_id bigint NULL,
   username varchar(255) NOT NULL,
   UTORID varchar(255)  NULL,
   PSEUDO_AUTHUSER_ID bigint NULL,
   STUDENT_NUMBER bigint NULL,
   YEAR_OF_STUDY int NULL,
   <PERSON>IMARY KEY (users_id),
   UNIQUE (username),
   UNIQUE (UTORID)
);

-- Add the column to store blob of profile photos
ALTER TABLE USERS
ADD PROFILE_PHOTO VARBINARY(MAX);

-- Drop the old/ unnecessary column
ALTER TABLE USERS
DROP COLUMN PICTURE_NAME;

DROP TABLE IF EXISTS upload_record;

DROP TABLE IF EXISTS uploaded_file;

CREATE TABLE uploaded_file (
    file_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    file_name NVARCHAR(255) NOT NULL,
    file_type NVARCHAR(50),
    data VARBINARY(MAX) NOT NULL,
    uploaded_on DATETIME2 NOT NULL
);


CREATE TABLE upload_record (
    upload_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    program_year NVARCHAR(50) NOT NULL,
    academic_year NVARCHAR(50) NOT NULL,
    results_file_id BIGINT,
    zip_file_id BIGINT,
    uploaded_on DATETIME2 NOT NULL,
    FOREIGN KEY (results_file_id) REFERENCES uploaded_file(file_id),
    FOREIGN KEY (zip_file_id) REFERENCES uploaded_file(file_id)
);