<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Dashboard</title>
</head>
<body>
<div layout:fragment="content">
    <section class="content">
        <div class="container-fluid">
            <!-- Your existing info boxes and other content -->

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Activities</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="pieChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Changed to match your layout's script fragment name -->
<th:block layout:fragment="scripts">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"></script>
    <script th:inline="javascript">
        $(document).ready(function() {
            // Get data from Thymeleaf
            var pieData = /*[[${chartData}]]*/ [];
            var pieLabels = /*[[${chartLabels}]]*/ [];

            // Define specific colors for each category
            var colors = [
                'rgba(220, 53, 69, 0.8)',  // danger - Evaluations
                'rgba(40, 167, 69, 0.8)',   // success - Assignments
                'rgba(255, 193, 7, 0.8)',   // warning - Learners
                'rgba(220, 53, 69, 0.8)'    // danger - Scholars
            ];

            // Create pie chart
            var ctx = document.getElementById('pieChart');
            if (ctx) {
                new Chart(ctx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: pieLabels,
                        datasets: [{
                            data: pieData,
                            backgroundColor: colors,
                            borderColor: colors.map(color => color.replace('0.8', '1')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 20
                            }
                        },
                        tooltips: {
                            callbacks: {
                                label: function(tooltipItem, data) {
                                    var dataset = data.datasets[tooltipItem.datasetIndex];
                                    var total = dataset.data.reduce((acc, curr) => acc + curr, 0);
                                    var currentValue = dataset.data[tooltipItem.index];
                                    var percentage = ((currentValue/total) * 100).toFixed(1);
                                    return data.labels[tooltipItem.index] + ': ' + currentValue + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</th:block>

</body>
</html>