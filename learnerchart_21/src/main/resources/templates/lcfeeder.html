<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Med IT App</title>
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.2/themes/smoothness/jquery-ui.css">

</head>
<body>
<h1 layout:fragment="page-title">EPA Summaries Upload</h1>

<div layout:fragment="content">
    <div class="container-fluid" role="main">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <!-- Card container -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>
                            Upload results for the EPA Summaries
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x mr-3"></i>
                                <div>
                                    <h5 class="alert-heading">Important Notice</h5>
                                    <p class="mb-0">All files are required to upload the results of
                                        EPA Summaries.</p>
                                </div>
                            </div>
                        </div>

                        <form id="upload-form" class="form" role="form" action="/epasummaries" method="post" enctype="multipart/form-data">
                            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

                            <div class="card mb-3">
                                <div class="card-body">

                                    <!-- Program Year -->
                                    <div class="form-group">
                                        <label for="program-year" class="font-weight-bold">
                                            <i class="fas fa-graduation-cap mr-2"></i>Program Year
                                        </label>
                                        <select name="programYear" id="program-year" class="form-control">
                                            <option value="1">Year 1</option>
                                            <option value="2">Year 2</option>
                                            <option value="3">Year 3</option>
                                            <option value="4">Year 4</option>
                                        </select>
                                    </div>

                                    <!-- Academic Year -->
                                    <div class="form-group">
                                        <label for="academic-year" class="font-weight-bold">
                                            <i class="fas fa-calendar-alt mr-2"></i>Academic Year
                                        </label>
                                        <select name="academicYear" id="academic-year" class="form-control">
                                            <option value="2022">2022-2023</option>
                                            <option value="2023" selected="selected">2023-2024</option>
                                            <option value="2024">2024-2025</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- File Upload Section -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <!-- Results File -->
                                    <div class="form-group">
                                        <label for="resultsFile" class="font-weight-bold">
                                            <i class="fas fa-file-excel mr-2"></i>Results (XLS, XLSX)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="resultsFile" name="resultsFile">
                                            <label class="custom-file-label" for="resultsFile">Choose file</label>
                                        </div>
                                    </div>

                                    <!-- Zip Files -->
                                    <div class="form-group">
                                        <label for="zipFiles" class="font-weight-bold">
                                            <i class="fas fa-file-archive mr-2"></i>Scanned Files (zip)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="zipFiles" name="zipFile">
                                            <label class="custom-file-label" for="zipFiles">Choose file</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5" id="upload-button">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>Upload Files
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add this to your scripts fragment -->
<th:block layout:fragment="scripts">
    <script>
        $(document).ready(function() {
            // Update file input labels with selected filename
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });

            // Form validation
            $('#upload-form').on('submit', function(e) {
                var resultsFile = $('#resultsFile').val();
                var zipFile = $('#zipFiles').val();

                if (!resultsFile || !zipFile) {
                    e.preventDefault();
                    alert('Please select both results and scanned files.');
                    return false;
                }

                // Disable submit button to prevent double submission
                $('#upload-button').prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...');
            });
        });
    </script>
</th:block>

</body>
</html>