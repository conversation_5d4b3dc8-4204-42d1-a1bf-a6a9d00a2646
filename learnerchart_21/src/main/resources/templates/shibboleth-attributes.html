<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Shibboleth SAML Attributes</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid black; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap; }
    </style>
</head>
<body>
<h1>Shibboleth SAML Attributes</h1>

<h2>Debug Information</h2>
<pre>
        <div th:each="line : ${debugInfo}" th:text="${line}"></div>
    </pre>

<h2>Request Attributes</h2>
<table>
    <tr>
        <th>Attribute Name</th>
        <th>Attribute Value</th>
    </tr>
    <tr th:each="entry : ${samlAttributes}">
        <td th:text="${entry.key}"></td>
        <td th:text="${entry.value}"></td>
    </tr>
</table>

<h2>HTTP Headers</h2>
<table>
    <tr>
        <th>Header Name</th>
        <th>Header Value</th>
    </tr>
    <tr th:each="entry : ${headers}">
        <td th:text="${entry.key}"></td>
        <td th:text="${entry.value}"></td>
    </tr>
</table>
</body>
</html>