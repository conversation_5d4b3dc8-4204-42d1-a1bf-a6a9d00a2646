<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- AdminLTE styles -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/css/adminlte.min.css">
</head>
<body>
<h1 layout:fragment="page-title">Dashboard</h1>

<div layout:fragment="content">

    <section class="content">
        <div class="container-fluid">
            <!-- Info boxes -->
            <div class="row">
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-tasks"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Evaluations</span>
                            <span class="info-box-number">150</span>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success elevation-1"><i class="fas fa-clipboard-list"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Assignments</span>
                            <span class="info-box-number">53</span>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-users"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Learners</span>
                            <span class="info-box-number">211</span>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-user-graduate"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Scholars</span>
                            <span class="info-box-number">65</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main row -->
            <div class="row">

                <div class="col-md-6">
                    <!-- PRODUCT LIST -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Imports</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <ul class="products-list product-list-in-card pl-2 pr-2">
                                <li class="item">
                                    <!--div class="product-img">
                                        <img th:src="@{/img/prod-1.jpg}" alt="Product Image" class="img-size-50">
                                    </div-->
                                    <div class="product-info">
                                        <a href="javascript:void(0)" class="product-title">Evaluations
                                            <span class="badge badge-info float-right">Jan 7, 2024</span></a>
                                        <span class="product-description">
                        Assessment of Demonstrated Professional Behaviours
                      </span>
                                    </div>
                                </li>
                                <li class="item">
                                    <!--div class="product-img">
                                        <img th:src="@{/img/prod-2.jpg}" alt="Product Image" class="img-size-50">
                                    </div-->
                                    <div class="product-info">
                                        <a href="javascript:void(0)" class="product-title">Evaluations
                                            <span class="badge badge-info float-right">Jan 7, 2024</span></a>
                                        <span class="product-description">
                        ICE:CS Psychiatric Unit - Mental Status Examination Report
                      </span>
                                    </div>
                                </li>
                                <li class="item">
                                    <!--div class="product-img">
                                        <img th:src="@{/img/prod-2.jpg}" alt="Product Image" class="img-size-50">
                                    </div-->
                                    <div class="product-info">
                                        <a href="javascript:void(0)" class="product-title">Assignments
                                            <span class="badge badge-info float-right">Jan 6, 2024</span></a>
                                        <span class="product-description">
                        Y2 Thematic Reflection 1 & 2 (2024-25)
                      </span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>


                <div class="col-md-6">
                    <!-- PRODUCT LIST -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Uploads</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <ul class="products-list product-list-in-card pl-2 pr-2">
                                <li class="item">
                                    <!--div class="product-img">
                                        <img th:src="@{/img/prod-1.jpg}" alt="Product Image" class="img-size-50">
                                    </div-->
                                    <div class="product-info">
                                        <a href="javascript:void(0)" class="product-title">Progress Test
                                            <span class="badge badge-info float-right">Jan 8, 2024</span></a>
                                        <span class="product-description">
                        Progress Test - November 2024 Y2
                      </span>
                                    </div>
                                </li>
                                <li class="item">
                                    <!--div class="product-img">
                                        <img th:src="@{/img/prod-2.jpg}" alt="Product Image" class="img-size-50">
                                    </div-->
                                    <div class="product-info">
                                        <a href="javascript:void(0)" class="product-title">Mastery exercise
                                            <span class="badge badge-info float-right">Jan 7, 2024</span></a>
                                        <span class="product-description">
                        MED200_Neuro2_ME_20241202
                      </span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <!-- Your existing info boxes and other content -->

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Activities <span th:text="'(' + ${NumOfScholars} + ')'"></span> </h3>
                        </div>
                        <div class="card-body">
                            <canvas id="pieChart" style="min-height: 250px; height: 250px; max-height: 250px; max-width: 100%;"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </section>

</div>

<!-- Changed to match your layout's script fragment name -->
<th:block layout:fragment="scripts">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.9.4/Chart.min.js"></script>
    <script th:inline="javascript">
        $(document).ready(function() {
            // Get data from Thymeleaf
            var pieData = /*[[${chartData}]]*/ [];
            var pieLabels = /*[[${chartLabels}]]*/ [];

            // Generate random colors for the chart
            var colors = pieData.map(() => {
                const r = Math.floor(Math.random() * 255);
                const g = Math.floor(Math.random() * 255);
                const b = Math.floor(Math.random() * 255);
                return `rgba(${r}, ${g}, ${b}, 0.8)`;
            });

            // Create pie chart
            var ctx = document.getElementById('pieChart');
            if (ctx) {
                new Chart(ctx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: pieLabels,
                        datasets: [{
                            data: pieData,
                            backgroundColor: colors,
                            borderColor: colors.map(color => color.replace('0.8', '1')),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 20
                            }
                        },
                        tooltips: {
                            callbacks: {
                                label: function(tooltipItem, data) {
                                    var dataset = data.datasets[tooltipItem.datasetIndex];
                                    var total = dataset.data.reduce((acc, curr) => acc + curr, 0);
                                    var currentValue = dataset.data[tooltipItem.index];
                                    var percentage = ((currentValue/total) * 100).toFixed(1);
                                    return data.labels[tooltipItem.index] + ': ' + currentValue + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</th:block>

</body>
</html>