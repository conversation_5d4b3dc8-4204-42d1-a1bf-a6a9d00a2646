<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Med IT App</title>
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.2/themes/smoothness/jquery-ui.css">
</head>
<body>
<h1 layout:fragment="page-title">ExamSoft Upload</h1>

<div layout:fragment="content">
    <div class="container-fluid" role="main">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>
                            Upload results for a single ExamSoft Weekly Exercise
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x mr-3"></i>
                                <div>
                                    <h5 class="alert-heading">Important Notice</h5>
                                    <p class="mb-0">All files are required to upload the results of an
                                        ExamSoft assessment.</p>
                                </div>
                            </div>
                        </div>

                        <form id="upload-form" class="form" role="form" action="http://localhost:8101/appl/admin/importStudentData" method="post" enctype="multipart/form-data">

                            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

                            <!-- Basic Information Card -->
                            <div class="card mb-3">
                                <div class="card-body">


                                    <!-- Assessment Type -->
                                    <div class="form-group">
                                        <label for="upload-type" class="font-weight-bold">
                                            <i class="fas fa-tasks mr-2"></i>Data To Upload
                                        </label>
                                        <select name="uploadType" id="upload-type" class="form-control">
                                            <option value="student">Student Data</option>
                                            <option value="evaluation">Evaluation Data</option>
                                            <option value="image">Image Data</option>

                                        </select>
                                    </div>


                                </div>
                            </div>

                            <!-- File Upload Section -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <!-- Results File -->
                                    <div class="form-group">
                                        <label class="font-weight-bold">
                                            <i class="fas fa-file-excel mr-2"></i>Feed File to Import
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="feedFile" name="feedFile">
                                            <label class="custom-file-label" for="feedFile">Choose file</label>
                                        </div>
                                    </div>


                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5" id="upload-button">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>Upload Feed File
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



</body>
</html>