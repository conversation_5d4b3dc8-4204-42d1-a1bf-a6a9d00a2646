<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<aside th:fragment="sidebar" class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a th:href="@{/}" class="brand-link">
        <img th:src="@{/img/AdminLTELogo.png}"  alt="AdminLTE Logo"
             class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">Admin Portal</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu">

                <li class="nav-item">
                    <a th:href="@{/swagger-ui.html}" class="nav-link" th:classappend="${currentPage == 'api'} ? 'active'">
                        <i class="nav-icon fas fa-file-import"></i>
                        <p>API</p>
                    </a>
                </li>

                <li class="nav-item">
                    <a th:href="@{/settings}" class="nav-link" th:classappend="${currentPage == 'settings'} ? 'active'">
                        <i class="nav-icon fas fa-cog"></i>
                        <p>Settings</p>
                    </a>
                </li>

            </ul>
        </nav>
    </div>
</aside>
</html>