<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Med IT App</title>
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.2/themes/smoothness/jquery-ui.css">
</head>
<body>
<h1 layout:fragment="page-title">ExamSoft Upload</h1>

<div layout:fragment="content">
    <div class="container-fluid" role="main">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>
                            Upload results for a single ExamSoft Weekly Exercise
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle fa-2x mr-3"></i>
                                <div>
                                    <h5 class="alert-heading">Important Notice</h5>
                                    <p class="mb-0">All files are required to upload the results of an
                                        ExamSoft assessment.</p>
                                </div>
                            </div>
                        </div>

                        <form id="upload-form" class="form" role="form" action="/epasummaries" method="post" enctype="multipart/form-data">
                            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

                            <!-- Basic Information Card -->
                            <div class="card mb-3">
                                <div class="card-body">

                                    <!-- Reassessment Checkbox -->
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="reassessment-checkbox">
                                            <label class="custom-control-label" for="reassessment-checkbox">
                                                This is a reassessment (No absentees)
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Assessment Type -->
                                    <div class="form-group">
                                        <label for="assessment-type" class="font-weight-bold">
                                            <i class="fas fa-tasks mr-2"></i>Assessment Type
                                        </label>
                                        <select name="assessmentType" id="assessment-type" class="form-control">
                                            <option value="OSCE">OSCE</option>
                                        </select>
                                    </div>

                                    <!-- Program Year -->
                                    <div class="form-group">
                                        <label for="program-year" class="font-weight-bold">
                                            <i class="fas fa-graduation-cap mr-2"></i>Program Year
                                        </label>
                                        <select name="programYear" id="program-year" class="form-control">
                                            <option value="1">Year 1</option>
                                            <option value="2">Year 2</option>
                                            <option value="3">Year 3</option>
                                            <option value="4">Year 4</option>
                                        </select>
                                    </div>

                                    <!-- Academic Year -->
                                    <div class="form-group">
                                        <label for="academic-year" class="font-weight-bold">
                                            <i class="fas fa-calendar-alt mr-2"></i>Academic Year
                                        </label>
                                        <select name="academicYear" id="academic-year" class="form-control">
                                            <option value="2022">2022-2023</option>
                                            <option value="2023" selected="selected">2023-2024</option>
                                            <option value="2024">2024-2025</option>
                                        </select>
                                    </div>



                                    <!-- Due Date -->
                                    <div class="form-group">
                                        <label for="due-date" class="font-weight-bold">
                                            <i class="fas fa-clock mr-2"></i>Due Date
                                        </label>
                                        <input type="text" id="due-date" name="dueDate" class="form-control datepicker">
                                    </div>

                                    <!-- Student Identity Type -->
                                    <div class="form-group">
                                        <label for="student-identity-type" class="font-weight-bold">
                                            <i class="fas fa-id-card mr-2"></i>Students Identified By
                                        </label>
                                        <select name="studentIdentityType" id="student-identity-type" class="form-control">
                                            <option value="STUDENT_NUMBER" selected="selected">Student Number</option>
                                            <option value="UTORID">UTORID</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- File Upload Section -->
                            <div class="card mb-3">
                                <div class="card-body">
                                    <!-- Results File -->
                                    <div class="form-group">
                                        <label class="font-weight-bold">
                                            <i class="fas fa-file-excel mr-2"></i>Exam Taker Results (XLS or XLSX)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="etResultsFile" name="etResultsFile">
                                            <label class="custom-file-label" for="etResultsFile">Choose file</label>
                                        </div>
                                    </div>

                                    <!-- Category Report -->
                                    <div class="form-group">
                                        <label class="font-weight-bold">
                                            <i class="fas fa-file-excel mr-2"></i>Category Report (XLS or XLSX)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="categoryReportFile" name="categoryReportFile">
                                            <label class="custom-file-label" for="categoryReportFile">Choose file</label>
                                        </div>
                                    </div>

                                    <!-- Absentees File -->
                                    <div class="form-group" id="absentee-list-group-item">
                                        <label class="font-weight-bold">
                                            <i class="fas fa-file-excel mr-2"></i>Absentees (XLS or XLSX)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="absenteeFile" name="absenteeFile">
                                            <label class="custom-file-label" for="absenteeFile">Choose file</label>
                                        </div>
                                    </div>

                                    <!-- Scanned Files -->
                                    <div class="form-group">
                                        <label class="font-weight-bold">
                                            <i class="fas fa-file-archive mr-2"></i>Scanned Files (zip)
                                        </label>
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="zipFiles" name="zipFile">
                                            <label class="custom-file-label" for="zipFiles">Choose file</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5" id="upload-button">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>Upload Files
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add this to your scripts fragment -->
<th:block layout:fragment="scripts">
    <script>
        $(document).ready(function() {
            // Initialize datepicker
            $("#due-date").datepicker({
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true
            });

            // Update file input labels with selected filename
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });

            // Toggle absentee file input based on reassessment checkbox
            $('#reassessment-checkbox').change(function() {
                $('#absentee-list-group-item').toggle(!this.checked);
            });

            // Form validation
            $('#upload-form').on('submit', function(e) {
                var requiredFiles = ['etResultsFile', 'categoryReportFile'];
                var missingFiles = [];

                requiredFiles.forEach(function(fileId) {
                    if (!$('#' + fileId).val()) {
                        missingFiles.push($('label[for="' + fileId + '"]').text().trim());
                    }
                });

                if (!$('#reassessment-checkbox').is(':checked') && !$('#absenteeFile').val()) {
                    missingFiles.push('Absentees File');
                }

                if (missingFiles.length > 0) {
                    e.preventDefault();
                    alert('Please select the following required files:\n- ' + missingFiles.join('\n- '));
                    return false;
                }

                // Disable submit button to prevent double submission
                $('#upload-button').prop('disabled', true)
                    .html('<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...');
            });
        });
    </script>
</th:block>

</body>
</html>