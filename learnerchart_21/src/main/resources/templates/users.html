<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/main}">
<head>
    <title>Users</title>
</head>
<body>
<h1 layout:fragment="page-title">Audit Log</h1>

<div layout:fragment="content">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Audit Log</h3>
            <div class="card-tools">
                <button type="button" class="btn btn-primary">Export the Audit Log</button>
            </div>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Activity Type</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="user : ${users}">
                    <td th:text="${user.id}">1</td>
                    <td th:text="${user.name}"><PERSON></td>
                    <td th:text="${user.email}"><EMAIL></td>
                    <td>
                        <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-sm btn-info">Edit</a>
                        <a th:href="@{/users/{id}/delete(id=${user.id})}" class="btn btn-sm btn-danger">Delete</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
</body>
</html>