package ca.medit.lcfeeder;

import ca.medit.learnerchart.GetstartApplication;
import nz.net.ultraq.thymeleaf.layoutdialect.LayoutDialect;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class GetstartApplicationTests {

	@Autowired
	private ApplicationContext context;

	private final ApplicationContextRunner contextRunner = new ApplicationContextRunner();

	@Test
	void contextLoads() {
		// Verifies that the Spring application context loads successfully
		assertNotNull(context, "Application context should not be null");
	}

	@Test
	void layoutDialectBeanIsConfigured() {
		// Verify that the LayoutDialect bean is properly configured in the context
		assertTrue(context.containsBean("layoutDialect"),
				"LayoutDialect bean should be present in the context");
		Object layoutDialect = context.getBean("layoutDialect");
		assertTrue(layoutDialect instanceof LayoutDialect,
				"Bean should be an instance of LayoutDialect");
	}

	@Test
	void mainMethodStartsApplication() {
		// Test the main method by mocking SpringApplication.run
		try (MockedStatic<SpringApplication> springApp = Mockito.mockStatic(SpringApplication.class)) {
			String[] args = {"--spring.profiles.active=test"};

			GetstartApplication.main(args);

			springApp.verify(() -> SpringApplication.run(GetstartApplication.class, args));
		}
	}

	@Test
	void applicationHasSpringBootApplicationAnnotation() {
		// Verify that the application class has the required Spring Boot annotation
		assertTrue(GetstartApplication.class.isAnnotationPresent(SpringBootApplication.class),
				"GetstartApplication should be annotated with @SpringBootApplication");
	}

	@Test
	void layoutDialectBeanCreation() {
		// Test the actual bean creation method
		GetstartApplication app = new GetstartApplication();
		LayoutDialect dialect = app.layoutDialect();

		assertNotNull(dialect, "LayoutDialect should not be null");
		assertInstanceOf(LayoutDialect.class, dialect, "Should return LayoutDialect instance");
	}

	@Configuration
	static class MinimalConfig {
		@Bean
		public LayoutDialect layoutDialect() {
			return new LayoutDialect();
		}
	}

	@Test
	void contextRunnerWithMinimalConfiguration() {
		new ApplicationContextRunner()
				.withUserConfiguration(MinimalConfig.class)
				.withPropertyValues(
						// Exclude all database auto-configuration
						"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration," +
								"org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration," +
								"org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration," +
								"org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration"
				)
				.run(context -> {
					assertNotNull(context, "Context should load successfully with minimal config");
					assertTrue(context.containsBean("layoutDialect"),
							"LayoutDialect bean should be available");
					assertEquals(1, context.getBeanNamesForType(LayoutDialect.class).length,
							"Exactly one LayoutDialect bean should be defined");
				});
	}

	@Test
	void applicationConfigurationIsValid() {
		// Test that the application can be instantiated
		GetstartApplication app = new GetstartApplication();
		assertNotNull(app, "Application instance should be created successfully");
	}
}