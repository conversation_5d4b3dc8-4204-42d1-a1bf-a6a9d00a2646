package ca.medit.learnerchart.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.joda.JodaModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import static org.junit.jupiter.api.Assertions.*;

class JacksonConfigTest {

    private AnnotationConfigApplicationContext context;

    @BeforeEach
    void setup() {
        context = new AnnotationConfigApplicationContext(JacksonConfig.class);
    }

    @DisplayName("ObjectMapper should not be null")
    @Test
    void objectMapperNotNull() {
        ObjectMapper objectMapper = context.getBean(ObjectMapper.class);
        assertNotNull(objectMapper);
    }
    @DisplayName("ObjectMapper should be able to serialize Joda DateTime")
    @Test
    void objectMapperSerializesJodaDateTime() throws Exception {
        ObjectMapper objectMapper = context.getBean(ObjectMapper.class);
        org.joda.time.DateTime now = new org.joda.time.DateTime(2024, 6, 17, 12, 30);

        String json = objectMapper.writeValueAsString(now);
        System.out.println("Serialized JSON: " + json);

        assertDoesNotThrow(() -> {
            objectMapper.writeValueAsString(new org.joda.time.DateTime());
        });

    }


}