package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.service.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ui.Model;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import ca.medit.learnerchart.common.exception.MedITException;

@ExtendWith(MockitoExtension.class)
public class UserControllerTest {

    @InjectMocks
    private UserController userController;

    @Mock
    private UserServiceImpl userService;

    @Mock
    private Model model;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createUser_ReturnsCreatedUser() {
        User newUser = new User();
        newUser.setUsername("newuser");
        when(userService.createUser(any(User.class))).thenReturn(newUser);

        User createdUser = userController.createUser(newUser);

        assertNotNull(createdUser);
        assertEquals("newuser", createdUser.getUsername());
    }

    @Test
    void updateUser_UpdatesAndReturnsUser() {
        User existingUser = new User();
        existingUser.setUsersId(1L);
        existingUser.setUsername("olduser");
        when(userService.getUser(1L)).thenReturn(existingUser);
        when(userService.updateUser(any(User.class))).thenReturn(existingUser);

        User updatedUser = new User();
        updatedUser.setUsername("updateduser");

        User result = userController.updateUser("1", updatedUser);

        assertNotNull(result);
        assertEquals("updateduser", result.getUsername());
    }

    @Test
    void updateUser_ThrowsNotFoundForNonExistentUser() {
        when(userService.getUser(1L)).thenReturn(null);

        MedITException exception = assertThrows(MedITException.class, () -> {
            userController.updateUser("1", new User());
        });

        assertEquals("Could not update the user successfully.", exception.getMessage());
    }

    @Test
    void disableUser_DisablesAndReturnsUser() {
        User existingUser = new User();
        existingUser.setUsersId(1L);
        existingUser.setDeleted(false);
        when(userService.getUser(1L)).thenReturn(existingUser);
        when(userService.updateUser(any(User.class))).thenReturn(existingUser);

        User result = userController.disableUser("1");

        assertNotNull(result);
        assertTrue(result.getDeleted());
    }

    @Test
    void disableUser_ThrowsNotFoundForNonExistentUser() {
        when(userService.getUser(1L)).thenReturn(null);

        MedITException exception = assertThrows(MedITException.class, () -> {
            userController.disableUser("1");
        });

        assertEquals("Could not update the user successfully.", exception.getMessage());
    }

    @Test
    void deleteUser_DeletesUserSuccessfully() {
        User existingUser = new User();
        existingUser.setUsersId(1L);
        when(userService.getUser(1L)).thenReturn(existingUser);

        assertDoesNotThrow(() -> userController.deleteUser("1"));
        verify(userService).deleteUser(existingUser);
    }

    @Test
    void deleteUser_ThrowsNotFoundForNonExistentUser() {
        when(userService.getUser(1L)).thenReturn(null);

        MedITException exception = assertThrows(MedITException.class, () -> {
            userController.deleteUser("1");
        });

        assertEquals("Could not delete the new user successfully.", exception.getMessage());
    }

}
