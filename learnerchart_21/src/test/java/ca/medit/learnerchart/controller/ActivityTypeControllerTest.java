package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.ActivityTypeJsonType;
import ca.medit.learnerchart.domain.AssessmentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ActivityTypeControllerTest {

    @Mock
    private HttpServletResponse httpServletResponse;

    @InjectMocks
    private ActivityTypeController activityTypeController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(activityTypeController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testGetActivityTypes_ShouldReturnListOfActivityTypes() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/activityTypes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(8)))
                .andExpect(jsonPath("$[0].id", is(AssessmentType.WEEKLY_EXERCISE.name())))
                .andExpect(jsonPath("$[1].id", is(AssessmentType.MASTERY_EXERCISE.name())))
                .andExpect(jsonPath("$[2].id", is(AssessmentType.ASSIGNMENT.name())))
                .andExpect(jsonPath("$[3].id", is(AssessmentType.EVALUATION.name())))
                .andExpect(jsonPath("$[4].id", is(AssessmentType.BELL_RINGER.name())))
                .andExpect(jsonPath("$[5].id", is(AssessmentType.OSCE.name())))
                .andExpect(jsonPath("$[6].id", is(AssessmentType.PROGRESS_TEST.name())))
                .andExpect(jsonPath("$[7].id", is(AssessmentType.EPA_SUMMARIES.name())));
    }

    @Test
    void testGetActivityTypes_ShouldReturnCorrectLabels() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/activityTypes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].attributes.name", is(AssessmentType.WEEKLY_EXERCISE.getLabel())))
                .andExpect(jsonPath("$[1].attributes.name", is(AssessmentType.MASTERY_EXERCISE.getLabel())))
                .andExpect(jsonPath("$[2].attributes.name", is(AssessmentType.ASSIGNMENT.getLabel())))
                .andExpect(jsonPath("$[3].attributes.name", is(AssessmentType.EVALUATION.getLabel())))
                .andExpect(jsonPath("$[4].attributes.name", is(AssessmentType.BELL_RINGER.getLabel())))
                .andExpect(jsonPath("$[5].attributes.name", is(AssessmentType.OSCE.getLabel())))
                .andExpect(jsonPath("$[6].attributes.name", is(AssessmentType.PROGRESS_TEST.getLabel())))
                .andExpect(jsonPath("$[7].attributes.name", is(AssessmentType.EPA_SUMMARIES.getLabel())));
    }

    @Test
    void testGetActivityTypes_ShouldReturnCorrectOrders() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/activityTypes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].attributes.order", is(AssessmentType.WEEKLY_EXERCISE.getOrder().intValue())))
                .andExpect(jsonPath("$[1].attributes.order", is(AssessmentType.MASTERY_EXERCISE.getOrder().intValue())))
                .andExpect(jsonPath("$[2].attributes.order", is(AssessmentType.ASSIGNMENT.getOrder().intValue())))
                .andExpect(jsonPath("$[3].attributes.order", is(AssessmentType.EVALUATION.getOrder().intValue())))
                .andExpect(jsonPath("$[4].attributes.order", is(AssessmentType.BELL_RINGER.getOrder().intValue())))
                .andExpect(jsonPath("$[5].attributes.order", is(AssessmentType.OSCE.getOrder().intValue())))
                .andExpect(jsonPath("$[6].attributes.order", is(AssessmentType.PROGRESS_TEST.getOrder().intValue())))
                .andExpect(jsonPath("$[7].attributes.order", is(AssessmentType.EPA_SUMMARIES.getOrder().intValue())));
    }

    @Test
    void testGetActivityTypes_ShouldReturnCorrectJsonStructure() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/activityTypes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id", notNullValue()))
                .andExpect(jsonPath("$[0].type", notNullValue()))
                .andExpect(jsonPath("$[0].attributes", notNullValue()))
                .andExpect(jsonPath("$[0].attributes.name", notNullValue()))
                .andExpect(jsonPath("$[0].attributes.namePlural", notNullValue()))
                .andExpect(jsonPath("$[0].attributes.order", notNullValue()))
                .andExpect(jsonPath("$[0].relationships", notNullValue()));
    }

    @Test
    void testGetActivityTypes_DirectMethodCall_ShouldReturnCorrectList() {
        // When
        Object result = activityTypeController.getActivityTypes(httpServletResponse);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof List<?>);

        @SuppressWarnings("unchecked")
        List<ActivityTypeJsonType> activityTypes = (List<ActivityTypeJsonType>) result;

        assertEquals(8, activityTypes.size());

        // Verify specific entries
        assertEquals(AssessmentType.WEEKLY_EXERCISE.name(), (String) activityTypes.get(0).getId());
        assertEquals(AssessmentType.MASTERY_EXERCISE.name(), (String) activityTypes.get(1).getId());
        assertEquals(AssessmentType.ASSIGNMENT.name(), (String) activityTypes.get(2).getId());
        assertEquals(AssessmentType.EVALUATION.name(), (String) activityTypes.get(3).getId());
        assertEquals(AssessmentType.BELL_RINGER.name(), (String) activityTypes.get(4).getId());
        assertEquals(AssessmentType.OSCE.name(), (String) activityTypes.get(5).getId());
        assertEquals(AssessmentType.PROGRESS_TEST.name(), (String) activityTypes.get(6).getId());
        assertEquals(AssessmentType.EPA_SUMMARIES.name(), (String) activityTypes.get(7).getId());
    }

    @Test
    void testGetActivityTypes_DirectMethodCall_ShouldReturnCorrectAttributes() {
        // When
        Object result = activityTypeController.getActivityTypes(httpServletResponse);

        // Then
        @SuppressWarnings("unchecked")
        List<ActivityTypeJsonType> activityTypes = (List<ActivityTypeJsonType>) result;

        // Test first item attributes
        ActivityTypeJsonType firstType = activityTypes.get(0);
        assertEquals(AssessmentType.WEEKLY_EXERCISE.getLabel(), firstType.getAttributes().getName());
        assertEquals(AssessmentType.WEEKLY_EXERCISE.getOrder(), firstType.getAttributes().getOrder());

        // Test another item to ensure consistency
        ActivityTypeJsonType osceType = activityTypes.stream()
                .filter(type -> AssessmentType.OSCE.name().equals((String) type.getId()))
                .findFirst()
                .orElse(null);

        assertNotNull(osceType);
        assertEquals(AssessmentType.OSCE.getLabel(), osceType.getAttributes().getName());
        assertEquals(AssessmentType.OSCE.getOrder(), osceType.getAttributes().getOrder());
    }

    @Test
    void testGetActivityTypes_ShouldIncludeAllExpectedAssessmentTypes() {
        // When
        Object result = activityTypeController.getActivityTypes(httpServletResponse);

        // Then
        @SuppressWarnings("unchecked")
        List<ActivityTypeJsonType> activityTypes = (List<ActivityTypeJsonType>) result;

        List<String> expectedTypes = List.of(
                AssessmentType.WEEKLY_EXERCISE.name(),
                AssessmentType.MASTERY_EXERCISE.name(),
                AssessmentType.ASSIGNMENT.name(),
                AssessmentType.EVALUATION.name(),
                AssessmentType.BELL_RINGER.name(),
                AssessmentType.OSCE.name(),
                AssessmentType.PROGRESS_TEST.name(),
                AssessmentType.EPA_SUMMARIES.name()
        );

        List<String> actualTypes = activityTypes.stream()
                .map(type -> (String) type.getId())
                .collect(Collectors.toList());

        assertEquals(expectedTypes.size(), actualTypes.size());
        assertTrue(actualTypes.containsAll(expectedTypes));
    }

    @Test
    void testGetActivityTypes_WithEmptyPath_ShouldReturnActivityTypes() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/activityTypes/")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(8)));
    }

    @Test
    void testGetActivityTypes_ShouldReturnConsistentResults() {
        // When - Call multiple times
        Object result1 = activityTypeController.getActivityTypes(httpServletResponse);
        Object result2 = activityTypeController.getActivityTypes(httpServletResponse);

        // Then - Results should be consistent
        assertNotNull(result1);
        assertNotNull(result2);

        @SuppressWarnings("unchecked")
        List<ActivityTypeJsonType> types1 = (List<ActivityTypeJsonType>) result1;
        @SuppressWarnings("unchecked")
        List<ActivityTypeJsonType> types2 = (List<ActivityTypeJsonType>) result2;

        assertEquals(types1.size(), types2.size());

        for (int i = 0; i < types1.size(); i++) {
            assertEquals((String) types1.get(i).getId(), (String) types2.get(i).getId());
            assertEquals(types1.get(i).getAttributes().getName(), types2.get(i).getAttributes().getName());
            assertEquals(types1.get(i).getAttributes().getOrder(), types2.get(i).getAttributes().getOrder());
        }
    }

    @Test
    void testGetActivityTypes_ShouldHaveCorrectCORSConfiguration() throws Exception {
        // When & Then - Test that CORS headers are properly configured
        mockMvc.perform(get("/appl/activityTypes")
                        .header("Origin", "http://localhost:4200")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mockMvc.perform(get("/appl/activityTypes")
                        .header("Origin", "https://kind-sky-048ac441e.6.azurestaticapps.net")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}