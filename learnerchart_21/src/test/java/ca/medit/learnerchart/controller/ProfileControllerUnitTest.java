package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.service.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ProfileControllerUnitTest {

    @Mock
    private UserServiceImpl userService;

    @InjectMocks
    private ProfileController profileController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateProfile() {
        User newUser = new User();
        newUser.setUsername("testUser");

        User createdUser = new User();
        createdUser.setUsername("testUser");

        when(userService.createUser(newUser)).thenReturn(createdUser);

        ResponseEntity<User> response = profileController.createProfile(newUser);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertEquals(createdUser, response.getBody());
    }

    @Test
    void testUpdateProfile() {
        Long userId = 1L;
        User existingUser = new User();
        existingUser.setUsersId(userId);

        User updatedUser = new User();
        updatedUser.setUsername("updatedUser");

        User savedUser = new User();
        savedUser.setUsername("updatedUser");

        when(userService.getUser(userId)).thenReturn(existingUser);
        when(userService.updateUser(any(User.class))).thenReturn(savedUser);

        ResponseEntity<User> response = profileController.updateProfile(userId.toString(), updatedUser);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(savedUser, response.getBody());
    }

    @Test
    void testUploadProfilePicture() throws IOException {
        Long userId = 1L;
        User existingUser = new User();
        existingUser.setUsersId(userId);

        MultipartFile file = mock(MultipartFile.class);
        when(file.getBytes()).thenReturn(new byte[]{1, 2, 3});
        when(userService.getUser(userId)).thenReturn(existingUser);

        ResponseEntity<String> response = profileController.uploadProfilePicture(userId.toString(), file);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Profile picture uploaded successfully", response.getBody());
    }

    @Test
    void testGetProfilePicture() {
        Long userId = 1L;
        User existingUser = new User();
        existingUser.setUsersId(userId);
        existingUser.setProfilePhoto(new byte[]{1, 2, 3});

        when(userService.getUser(userId)).thenReturn(existingUser);

        ResponseEntity<byte[]> response = profileController.getProfilePicture(userId.toString());

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertArrayEquals(new byte[]{1, 2, 3}, response.getBody());
    }
}
