package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.service.ArtifactJsonService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ArtifactControllerTest {

    @Mock
    private ArtifactJsonService jsonSvc;

    @InjectMocks
    private ArtifactController artifactController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private static final String TEST_UTOR_ID = "testuser123";
    private static final String BASE_URL = "/appl/artifacts";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(artifactController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getTextArtifactsForLearner_Success() throws Exception {
        // Arrange
        JsonResponse mockResponse = createMockJsonResponse("text artifacts");
        when(jsonSvc.getTextArtifactsForLearner(TEST_UTOR_ID)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.message").value("text artifacts"));

        verify(jsonSvc, times(1)).getTextArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getTextArtifactsForLearner_Exception() throws Exception {
        // Arrange
        Exception mockException = new RuntimeException("Database connection failed");
        when(jsonSvc.getTextArtifactsForLearner(TEST_UTOR_ID)).thenThrow(mockException);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Error getting file"));

        verify(jsonSvc, times(1)).getTextArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getTextArtifactsForLearner_WithSpecialCharacters() throws Exception {
        // Arrange
        String specialUtorId = "test@user_123";
        JsonResponse mockResponse = createMockJsonResponse("text artifacts for special user");
        when(jsonSvc.getTextArtifactsForLearner(specialUtorId)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", specialUtorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("text artifacts for special user"));

        verify(jsonSvc, times(1)).getTextArtifactsForLearner(specialUtorId);
    }

    @Test
    void getFileArtifactsForLearner_Success() throws Exception {
        // Arrange
        JsonResponse mockResponse = createMockJsonResponse("file artifacts");
        when(jsonSvc.getFileArtifactsForLearner(TEST_UTOR_ID)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.message").value("file artifacts"));

        verify(jsonSvc, times(1)).getFileArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getFileArtifactsForLearner_Exception() throws Exception {
        // Arrange
        MedITException mockException = new MedITException("Service unavailable");
        when(jsonSvc.getFileArtifactsForLearner(TEST_UTOR_ID)).thenThrow(mockException);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Error getting file"));

        verify(jsonSvc, times(1)).getFileArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getFileArtifactsForLearner_NullPointerException() throws Exception {
        // Arrange
        when(jsonSvc.getFileArtifactsForLearner(TEST_UTOR_ID)).thenThrow(new NullPointerException("Null value encountered"));

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Error getting file"));

        verify(jsonSvc, times(1)).getFileArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getLinkArtifactsForLearner_Success() throws Exception {
        // Arrange
        JsonResponse mockResponse = createMockJsonResponse("link artifacts");
        when(jsonSvc.getLinkArtifactsForLearner(TEST_UTOR_ID)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/link/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.message").value("link artifacts"));

        verify(jsonSvc, times(1)).getLinkArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getLinkArtifactsForLearner_Exception() throws Exception {
        // Arrange
        RuntimeException mockException = new RuntimeException("Timeout occurred");
        when(jsonSvc.getLinkArtifactsForLearner(TEST_UTOR_ID)).thenThrow(mockException);

        // Act & Assert
        mockMvc.perform(get(BASE_URL + "/link/learner/{utorId}", TEST_UTOR_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Error getting file"));

        verify(jsonSvc, times(1)).getLinkArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void getAllEndpoints_WithEmptyUtorId() throws Exception {
        // Test all endpoints with empty utorId
        String emptyUtorId = "";

        // Text artifacts
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", emptyUtorId))
                .andExpect(status().isNotFound());

        // File artifacts
        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", emptyUtorId))
                .andExpect(status().isNotFound());

        // Link artifacts
        mockMvc.perform(get(BASE_URL + "/link/learner/{utorId}", emptyUtorId))
                .andExpect(status().isNotFound());

    }

    @Test
    void testCorsHeaders() throws Exception {
        // Arrange
        JsonResponse mockResponse = createMockJsonResponse("test");
        when(jsonSvc.getTextArtifactsForLearner(anyString())).thenReturn(mockResponse);

        // Act & Assert - Test CORS configuration indirectly
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", TEST_UTOR_ID)
                        .header("Origin", "http://localhost:4200")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testServiceIntegration_AllMethods() throws Exception {
        // Arrange - Create different responses for each service method
        JsonResponse textResponse = createMockJsonResponse("text data");
        JsonResponse fileResponse = createMockJsonResponse("file data");
        JsonResponse linkResponse = createMockJsonResponse("link data");

        when(jsonSvc.getTextArtifactsForLearner(TEST_UTOR_ID)).thenReturn(textResponse);
        when(jsonSvc.getFileArtifactsForLearner(TEST_UTOR_ID)).thenReturn(fileResponse);
        when(jsonSvc.getLinkArtifactsForLearner(TEST_UTOR_ID)).thenReturn(linkResponse);

        // Act & Assert - Test all endpoints
        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", TEST_UTOR_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("text data"));

        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", TEST_UTOR_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("file data"));

        mockMvc.perform(get(BASE_URL + "/link/learner/{utorId}", TEST_UTOR_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("link data"));

        // Verify all service methods were called exactly once
        verify(jsonSvc, times(1)).getTextArtifactsForLearner(TEST_UTOR_ID);
        verify(jsonSvc, times(1)).getFileArtifactsForLearner(TEST_UTOR_ID);
        verify(jsonSvc, times(1)).getLinkArtifactsForLearner(TEST_UTOR_ID);
    }

    @Test
    void testExceptionHandling_WithDifferentExceptionTypes() throws Exception {
        // Test with different exception types to ensure consistent error handling

        // IllegalArgumentException
        when(jsonSvc.getTextArtifactsForLearner(TEST_UTOR_ID))
                .thenThrow(new IllegalArgumentException("Invalid argument"));

        mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", TEST_UTOR_ID))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Error getting file"));

        // MedITException
        when(jsonSvc.getFileArtifactsForLearner(TEST_UTOR_ID))
                .thenThrow(new MedITException("Custom MedIT error", new RuntimeException("Root cause")));

        mockMvc.perform(get(BASE_URL + "/file/learner/{utorId}", TEST_UTOR_ID))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.message").value("Error getting file"));
    }

    @Test
    void testPathVariableValidation() throws Exception {
        // Test with various utorId formats
        List<String> testUtorIds = Arrays.asList(
                "user123",
                "<EMAIL>",
                "user_123",
                "user-123",
                "123456789",
                "a"
        );

        JsonResponse mockResponse = createMockJsonResponse("test response");

        for (String utorId : testUtorIds) {
            when(jsonSvc.getTextArtifactsForLearner(utorId)).thenReturn(mockResponse);

            mockMvc.perform(get(BASE_URL + "/text/learner/{utorId}", utorId))
                    .andExpect(status().isOk());

            verify(jsonSvc).getTextArtifactsForLearner(utorId);
        }
    }

    /**
     * Helper method to create a mock JsonResponse for testing
     */
    private JsonResponse createMockJsonResponse(String message) {
        return new JsonResponse() {
            private String msg = message;
            private Object data = Arrays.asList("item1", "item2", "item3");

            public String getMessage() { return msg; }
            public JsonResponse setMessage(String message) { this.msg = message; return this; }
            public Object getData() { return data; }
            public JsonResponse setData(Object data) { this.data = data; return this; }
        };
    }
}