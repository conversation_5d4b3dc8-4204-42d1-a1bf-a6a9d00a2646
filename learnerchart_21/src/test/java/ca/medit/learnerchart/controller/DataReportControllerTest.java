package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.DataReport;
import ca.medit.learnerchart.service.DataReportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DataReportController Tests")
class DataReportControllerTest {

    @Mock
    private DataReportService dataReportService;

    @InjectMocks
    private DataReportController dataReportController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataReportController).build();
        objectMapper = new ObjectMapper();
    }

    // Test data creation helper methods
    private DataReport createSampleDataReport(Long assessmentId, String name, String utorid) {
        DataReport report = new DataReport();
        report.setAssessmentId(assessmentId);
        report.setName(name);
        report.setAssessmentType("QUIZ");
        report.setScoreId(100L + assessmentId);
        report.setUtorid(utorid);
        report.setPointsAvailable(100.0);
        report.setPointsEarned(85.0);
        report.setRawValue("85/100");
        report.setCplanXid("CPLAN123");
        report.setNumberOfCorrect(17);
        report.setNumberOfItems(20);
        report.setParentRawValue("Parent Raw Value");
        report.setParentName("Parent Assessment");
        report.setStudentNumber(12345L);
        report.setReference("REF001");
        return report;
    }

    private List<DataReport> createSampleDataReports() {
        return Arrays.asList(
                createSampleDataReport(1L, "Math Quiz 1", "student1"),
                createSampleDataReport(2L, "Science Test", "student2"),
                createSampleDataReport(3L, "History Exam", "student1")
        );
    }

    // Tests for /appl/reports/data endpoint

    @Test
    @DisplayName("Should return data reports successfully with valid parameters")
    void getDataReports_WithValidParameters_ShouldReturnReports() throws Exception {
        // Given
        Integer year = 2024;
        String dataType = "ASSESSMENT";
        List<DataReport> expectedReports = createSampleDataReports();

        when(dataReportService.getReportsByYearAndType(year, dataType))
                .thenReturn(expectedReports);

        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", year.toString())
                        .param("dataType", dataType)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$[0].assessmentId", is(1)))
                .andExpect(jsonPath("$[0].name", is("Math Quiz 1")))
                .andExpect(jsonPath("$[0].utorid", is("student1")))
                .andExpect(jsonPath("$[0].pointsAvailable", is(100.0)))
                .andExpect(jsonPath("$[0].pointsEarned", is(85.0)))
                .andExpect(jsonPath("$[1].assessmentId", is(2)))
                .andExpect(jsonPath("$[1].name", is("Science Test")))
                .andExpect(jsonPath("$[2].assessmentId", is(3)))
                .andExpect(jsonPath("$[2].name", is("History Exam")));

        verify(dataReportService, times(1)).getReportsByYearAndType(year, dataType);
    }

    @Test
    @DisplayName("Should return empty list when no reports found")
    void getDataReports_WhenNoReportsFound_ShouldReturnEmptyList() throws Exception {
        // Given
        Integer year = 2024;
        String dataType = "ASSESSMENT";

        when(dataReportService.getReportsByYearAndType(year, dataType))
                .thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", year.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(0)));

        verify(dataReportService, times(1)).getReportsByYearAndType(year, dataType);
    }

    @Test
    @DisplayName("Should return 400 when year parameter is missing")
    void getDataReports_WithoutYearParameter_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("dataType", "ASSESSMENT"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByYearAndType(any(), any());
    }

    @Test
    @DisplayName("Should return 400 when dataType parameter is missing")
    void getDataReports_WithoutDataTypeParameter_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", "2024"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByYearAndType(any(), any());
    }

    @Test
    @DisplayName("Should return 400 when both parameters are missing")
    void getDataReports_WithoutParameters_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/data"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByYearAndType(any(), any());
    }

    @Test
    @DisplayName("Should handle different dataType values correctly")
    void getDataReports_WithDifferentDataTypes_ShouldWork() throws Exception {
        // Given
        Integer year = 2024;
        String[] dataTypes = {"QUIZ", "EXAM", "ASSIGNMENT", "TEST"};

        for (String dataType : dataTypes) {
            when(dataReportService.getReportsByYearAndType(year, dataType))
                    .thenReturn(createSampleDataReports());

            // When & Then
            mockMvc.perform(get("/appl/reports/data")
                            .param("year", year.toString())
                            .param("dataType", dataType))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$", hasSize(3)));
        }

        verify(dataReportService, times(dataTypes.length)).getReportsByYearAndType(eq(year), any());
    }

    @Test
    @DisplayName("Should handle edge case years correctly")
    void getDataReports_WithEdgeCaseYears_ShouldWork() throws Exception {
        // Given
        Integer[] years = {1900, 2000, 2024, 2030, 9999};
        String dataType = "ASSESSMENT";

        for (Integer year : years) {
            when(dataReportService.getReportsByYearAndType(year, dataType))
                    .thenReturn(Collections.emptyList());

            // When & Then
            mockMvc.perform(get("/appl/reports/data")
                            .param("year", year.toString())
                            .param("dataType", dataType))
                    .andExpect(status().isOk());
        }

        verify(dataReportService, times(years.length)).getReportsByYearAndType(any(), eq(dataType));
    }

    // Tests for /appl/reports/dashboarddata endpoint

    @Test
    @DisplayName("Should return dashboard data reports successfully with valid parameters")
    void getDashboardDataReports_WithValidParameters_ShouldReturnReports() throws Exception {
        // Given
        Integer year = 2024;
        Integer programYear = 1;
        String dataType = "DASHBOARD";
        List<DataReport> expectedReports = createSampleDataReports();

        when(dataReportService.getReportsByProgramYearAndType(year, programYear, dataType))
                .thenReturn(expectedReports);

        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", year.toString())
                        .param("programYear", programYear.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(3)))
                .andExpect(jsonPath("$[0].assessmentId", is(1)))
                .andExpect(jsonPath("$[0].name", is("Math Quiz 1")))
                .andExpect(jsonPath("$[1].assessmentId", is(2)))
                .andExpect(jsonPath("$[2].assessmentId", is(3)));

        verify(dataReportService, times(1)).getReportsByProgramYearAndType(year, programYear, dataType);
    }

    @Test
    @DisplayName("Should return empty list when no dashboard reports found")
    void getDashboardDataReports_WhenNoReportsFound_ShouldReturnEmptyList() throws Exception {
        // Given
        Integer year = 2024;
        Integer programYear = 2;
        String dataType = "DASHBOARD";

        when(dataReportService.getReportsByProgramYearAndType(year, programYear, dataType))
                .thenReturn(Collections.emptyList());

        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", year.toString())
                        .param("programYear", programYear.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(0)));

        verify(dataReportService, times(1)).getReportsByProgramYearAndType(year, programYear, dataType);
    }

    @Test
    @DisplayName("Should return 400 when year parameter is missing for dashboard endpoint")
    void getDashboardDataReports_WithoutYearParameter_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("programYear", "1")
                        .param("dataType", "DASHBOARD"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByProgramYearAndType(any(), any(), any());
    }

    @Test
    @DisplayName("Should return 400 when programYear parameter is missing")
    void getDashboardDataReports_WithoutProgramYearParameter_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", "2024")
                        .param("dataType", "DASHBOARD"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByProgramYearAndType(any(), any(), any());
    }

    @Test
    @DisplayName("Should return 400 when dataType parameter is missing for dashboard endpoint")
    void getDashboardDataReports_WithoutDataTypeParameter_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", "2024")
                        .param("programYear", "1"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByProgramYearAndType(any(), any(), any());
    }

    @Test
    @DisplayName("Should return 400 when all parameters are missing for dashboard endpoint")
    void getDashboardDataReports_WithoutParameters_ShouldReturn400() throws Exception {
        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata"))
                .andExpect(status().isBadRequest());

        verify(dataReportService, never()).getReportsByProgramYearAndType(any(), any(), any());
    }

    @Test
    @DisplayName("Should handle different program years correctly")
    void getDashboardDataReports_WithDifferentProgramYears_ShouldWork() throws Exception {
        // Given
        Integer year = 2024;
        Integer[] programYears = {1, 2, 3, 4};
        String dataType = "DASHBOARD";

        for (Integer programYear : programYears) {
            when(dataReportService.getReportsByProgramYearAndType(year, programYear, dataType))
                    .thenReturn(createSampleDataReports());

            // When & Then
            mockMvc.perform(get("/appl/reports/dashboarddata")
                            .param("year", year.toString())
                            .param("programYear", programYear.toString())
                            .param("dataType", dataType))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$", hasSize(3)));
        }

        verify(dataReportService, times(programYears.length)).getReportsByProgramYearAndType(eq(year), any(), eq(dataType));
    }

    @Test
    @DisplayName("Should handle single report correctly")
    void getDataReports_WithSingleReport_ShouldReturnSingleItem() throws Exception {
        // Given
        Integer year = 2024;
        String dataType = "SINGLE";
        List<DataReport> singleReport = Arrays.asList(createSampleDataReport(1L, "Single Test", "student1"));

        when(dataReportService.getReportsByYearAndType(year, dataType))
                .thenReturn(singleReport);

        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", year.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].assessmentId", is(1)))
                .andExpect(jsonPath("$[0].name", is("Single Test")));

        verify(dataReportService, times(1)).getReportsByYearAndType(year, dataType);
    }

    @Test
    @DisplayName("Should handle dashboard data with single report correctly")
    void getDashboardDataReports_WithSingleReport_ShouldReturnSingleItem() throws Exception {
        // Given
        Integer year = 2024;
        Integer programYear = 1;
        String dataType = "SINGLE_DASHBOARD";
        List<DataReport> singleReport = Arrays.asList(createSampleDataReport(1L, "Single Dashboard Test", "student1"));

        when(dataReportService.getReportsByProgramYearAndType(year, programYear, dataType))
                .thenReturn(singleReport);

        // When & Then
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", year.toString())
                        .param("programYear", programYear.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].assessmentId", is(1)))
                .andExpect(jsonPath("$[0].name", is("Single Dashboard Test")));

        verify(dataReportService, times(1)).getReportsByProgramYearAndType(year, programYear, dataType);
    }

    @Test
    @DisplayName("Should verify service method is called with correct parameters")
    void getDataReports_ShouldCallServiceWithCorrectParameters() throws Exception {
        // Given
        Integer year = 2023;
        String dataType = "VERIFY_PARAMS";

        when(dataReportService.getReportsByYearAndType(year, dataType))
                .thenReturn(Collections.emptyList());

        // When
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", year.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk());

        // Then
        verify(dataReportService, times(1)).getReportsByYearAndType(year, dataType);
        verify(dataReportService, never()).getReportsByProgramYearAndType(any(), any(), any());
    }

    @Test
    @DisplayName("Should verify dashboard service method is called with correct parameters")
    void getDashboardDataReports_ShouldCallServiceWithCorrectParameters() throws Exception {
        // Given
        Integer year = 2023;
        Integer programYear = 3;
        String dataType = "VERIFY_DASHBOARD_PARAMS";

        when(dataReportService.getReportsByProgramYearAndType(year, programYear, dataType))
                .thenReturn(Collections.emptyList());

        // When
        mockMvc.perform(get("/appl/reports/dashboarddata")
                        .param("year", year.toString())
                        .param("programYear", programYear.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk());

        // Then
        verify(dataReportService, times(1)).getReportsByProgramYearAndType(year, programYear, dataType);
        verify(dataReportService, never()).getReportsByYearAndType(any(), any());
    }

    @Test
    @DisplayName("Should handle reports with null values correctly")
    void getDataReports_WithNullValues_ShouldHandleGracefully() throws Exception {
        // Given
        Integer year = 2024;
        String dataType = "NULL_VALUES";
        DataReport reportWithNulls = new DataReport();
        reportWithNulls.setAssessmentId(1L);
        reportWithNulls.setName("Test with nulls");
        // Leave other fields as null

        when(dataReportService.getReportsByYearAndType(year, dataType))
                .thenReturn(Arrays.asList(reportWithNulls));

        // When & Then
        mockMvc.perform(get("/appl/reports/data")
                        .param("year", year.toString())
                        .param("dataType", dataType))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].assessmentId", is(1)))
                .andExpect(jsonPath("$[0].name", is("Test with nulls")))
                .andExpect(jsonPath("$[0].pointsAvailable").doesNotExist());

        verify(dataReportService, times(1)).getReportsByYearAndType(year, dataType);
    }
}