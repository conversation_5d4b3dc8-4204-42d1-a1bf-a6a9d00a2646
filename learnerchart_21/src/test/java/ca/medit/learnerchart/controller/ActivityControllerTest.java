package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.dto.LearnerFile;
import ca.medit.learnerchart.service.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ActivityControllerTest {

    @Mock
    private ActivityTimelineJsonService activityJsonSvc;

    @Mock
    private AssessmentModeService assessmentModeSvc;

    @Mock
    private LearnerChartFilterService filterSvc;

    @Mock
    private ActivityService activitySvc;

    @Mock
    private HttpServletResponse httpServletResponse;

    @InjectMocks
    private ActivityController activityController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(activityController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getActivities_Success() throws Exception {
        // Arrange
        String assessmentModeKey = "WRITTEN";
        Integer yearId = 2024;
        String utorId = "testuser";

        AssessmentMode mockMode = AssessmentMode.WRITTEN;
        LearnerChartFilter mockFilter = new LearnerChartFilter(2024, 1, utorId, mockMode);
        JsonResponse mockJsonResponse = new JsonResponse();
        mockJsonResponse.setData(new ArrayList<>());

        when(assessmentModeSvc.getModeForKey(assessmentModeKey)).thenReturn(mockMode);
        when(filterSvc.getFilter(yearId, utorId, mockMode)).thenReturn(mockFilter);
        when(activityJsonSvc.getActivitiesForTimeLine(mockFilter)).thenReturn(mockJsonResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/activities/mode/{mode}/year/{yearId}/learner/{utorId}",
                        assessmentModeKey, yearId, utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(assessmentModeSvc).getModeForKey(assessmentModeKey);
        verify(filterSvc).getFilter(yearId, utorId, mockMode);
        verify(activityJsonSvc).getActivitiesForTimeLine(mockFilter);
    }

    @Test
    void getActivities_DirectCall_Success() {
        // Arrange
        String assessmentModeKey = "PERFORMANCE_BASED";
        Integer yearId = 2024;
        String utorId = "testuser";

        AssessmentMode mockMode = AssessmentMode.PERFORMANCE_BASED;
        LearnerChartFilter mockFilter = new LearnerChartFilter(2024, 1, utorId, mockMode);
        JsonResponse mockJsonResponse = new JsonResponse();
        mockJsonResponse.setData(new ArrayList<>());

        when(assessmentModeSvc.getModeForKey(assessmentModeKey)).thenReturn(mockMode);
        when(filterSvc.getFilter(yearId, utorId, mockMode)).thenReturn(mockFilter);
        when(activityJsonSvc.getActivitiesForTimeLine(mockFilter)).thenReturn(mockJsonResponse);

        // Act
        Object result = activityController.getActivities(assessmentModeKey, yearId, utorId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertEquals(mockJsonResponse, result);
        verify(assessmentModeSvc).getModeForKey(assessmentModeKey);
        verify(filterSvc).getFilter(yearId, utorId, mockMode);
        verify(activityJsonSvc).getActivitiesForTimeLine(mockFilter);
    }

    @Test
    void getActivities_InvalidAssessmentMode_ThrowsException() {
        // Arrange
        String invalidModeKey = "INVALID_MODE";
        Integer yearId = 2024;
        String utorId = "testuser";

        when(assessmentModeSvc.getModeForKey(invalidModeKey))
                .thenThrow(new MedITException("Invalid assessment mode: " + invalidModeKey));

        // Act & Assert
        assertThrows(MedITException.class, () -> {
            activityController.getActivities(invalidModeKey, yearId, utorId, httpServletResponse);
        });

        verify(assessmentModeSvc).getModeForKey(invalidModeKey);
        verify(filterSvc, never()).getFilter(any(), any(), any());
        verify(activityJsonSvc, never()).getActivitiesForTimeLine(any());
    }

    @Test
    void getActivities_FilterServiceException_ThrowsException() {
        // Arrange
        String assessmentModeKey = "EVALUATION";
        Integer yearId = 2024;
        String utorId = "testuser";

        AssessmentMode mockMode = AssessmentMode.EVALUATION;

        when(assessmentModeSvc.getModeForKey(assessmentModeKey)).thenReturn(mockMode);
        when(filterSvc.getFilter(yearId, utorId, mockMode))
                .thenThrow(new RuntimeException("Filter creation failed"));

        // Act & Assert
        assertThrows(MedITException.class, () -> {
            activityController.getActivities(assessmentModeKey, yearId, utorId, httpServletResponse);
        });

        verify(assessmentModeSvc).getModeForKey(assessmentModeKey);
        verify(filterSvc).getFilter(yearId, utorId, mockMode);
        verify(activityJsonSvc, never()).getActivitiesForTimeLine(any());
    }

    @Test
    void getActivities_ActivityJsonServiceException_ThrowsException() {
        // Arrange
        String assessmentModeKey = "ASSIGNMENT";
        Integer yearId = 2024;
        String utorId = "testuser";

        AssessmentMode mockMode = AssessmentMode.ASSIGNMENT;
        LearnerChartFilter mockFilter = new LearnerChartFilter(2024, 1, utorId, mockMode);

        when(assessmentModeSvc.getModeForKey(assessmentModeKey)).thenReturn(mockMode);
        when(filterSvc.getFilter(yearId, utorId, mockMode)).thenReturn(mockFilter);
        when(activityJsonSvc.getActivitiesForTimeLine(mockFilter))
                .thenThrow(new RuntimeException("JSON service failed"));

        // Act & Assert
        assertThrows(MedITException.class, () -> {
            activityController.getActivities(assessmentModeKey, yearId, utorId, httpServletResponse);
        });

        verify(assessmentModeSvc).getModeForKey(assessmentModeKey);
        verify(filterSvc).getFilter(yearId, utorId, mockMode);
        verify(activityJsonSvc).getActivitiesForTimeLine(mockFilter);
    }

    @Test
    void getAssignmentActivityDetail_Success() {
        // Arrange
        Long activityId = 123L;
        String utorId = "testuser";
        JsonResponse mockJsonResponse = new JsonResponse();
        mockJsonResponse.setData("test data");

        when(activityJsonSvc.getAssignmentActivityDetail(activityId, utorId)).thenReturn(mockJsonResponse);

        // Act
        Object result = activityController.getAssignmentActivityDetail(activityId, utorId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertEquals(mockJsonResponse, result);
        verify(activityJsonSvc).getAssignmentActivityDetail(activityId, utorId);
    }

    @Test
    void getAssignmentActivityDetail_ServiceException_ThrowsException() {
        // Arrange
        Long activityId = 123L;
        String utorId = "testuser";

        when(activityJsonSvc.getAssignmentActivityDetail(activityId, utorId))
                .thenThrow(new RuntimeException("Service failed"));

        // Act & Assert
        assertThrows(MedITException.class, () -> {
            activityController.getAssignmentActivityDetail(activityId, utorId, httpServletResponse);
        });

        verify(activityJsonSvc).getAssignmentActivityDetail(activityId, utorId);
    }

    @Test
    void getOsceFile_Success() throws IOException, SQLException {
        // Arrange
        Long scoreId = 456L;
        String fileName = "test-file.pdf";
        byte[] fileContent = "test file content".getBytes();
        InputStream inputStream = new ByteArrayInputStream(fileContent);

        LearnerFile mockFile = mock(LearnerFile.class);
        when(mockFile.getFileName()).thenReturn(fileName);
        when(mockFile.getInputStream()).thenReturn(inputStream);

        ServletOutputStream servletOutputStream = mock(ServletOutputStream.class);
        when(httpServletResponse.getOutputStream()).thenReturn(servletOutputStream);
        when(activitySvc.getOsceFile(scoreId)).thenReturn(mockFile);

        // Act
        Object result = activityController.getOsceFile(scoreId, httpServletResponse);

        // Assert
        assertNull(result); // Should return null for successful file download
        verify(activitySvc).getOsceFile(scoreId);
        verify(httpServletResponse).setContentType("application/pdf");
        verify(httpServletResponse).setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        verify(httpServletResponse).getOutputStream();
        verify(httpServletResponse).flushBuffer();
    }

    @Test
    void getOsceFile_ServiceException_ReturnsErrorAndSetsStatus() throws IOException, SQLException {
        // Arrange
        Long scoreId = 456L;

        when(activitySvc.getOsceFile(scoreId)).thenThrow(new RuntimeException("File not found"));

        // Act
        Object result = activityController.getOsceFile(scoreId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof MedITException);
        MedITException exception = (MedITException) result;
        assertEquals("Error getting file", exception.getMessage());

        verify(activitySvc).getOsceFile(scoreId);
        verify(httpServletResponse).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        verify(httpServletResponse, never()).setContentType(anyString());
        verify(httpServletResponse, never()).setHeader(anyString(), anyString());
    }

    @Test
    void getOsceFile_IOExceptionDuringWrite_ReturnsErrorAndSetsStatus() throws IOException, SQLException {
        // Arrange
        Long scoreId = 456L;
        String fileName = "test-file.pdf";
        byte[] fileContent = "test file content".getBytes();
        InputStream inputStream = new ByteArrayInputStream(fileContent);

        LearnerFile mockFile = mock(LearnerFile.class);
        when(mockFile.getFileName()).thenReturn(fileName);
        when(mockFile.getInputStream()).thenReturn(inputStream);

        when(activitySvc.getOsceFile(scoreId)).thenReturn(mockFile);
        when(httpServletResponse.getOutputStream()).thenThrow(new IOException("Stream error"));

        // Act
        Object result = activityController.getOsceFile(scoreId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof MedITException);
        MedITException exception = (MedITException) result;
        assertEquals("Error getting file", exception.getMessage());

        verify(activitySvc).getOsceFile(scoreId);
        verify(httpServletResponse).setContentType("application/pdf");
        verify(httpServletResponse).setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        verify(httpServletResponse).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    void getActivities_NullParameters_HandledGracefully() {
        // Arrange
        when(assessmentModeSvc.getModeForKey(null))
                .thenThrow(new MedITException("Assessment mode must not be null"));

        // Act & Assert
        assertThrows(MedITException.class, () -> {
            activityController.getActivities(null, 2024, "testuser", httpServletResponse);
        });

        verify(assessmentModeSvc).getModeForKey(null);
    }

    @Test
    void getActivities_AllAssessmentModes_Success() {
        // Test all assessment modes
        AssessmentMode[] modes = {AssessmentMode.WRITTEN, AssessmentMode.PERFORMANCE_BASED,
                AssessmentMode.EVALUATION, AssessmentMode.ASSIGNMENT};

        for (AssessmentMode mode : modes) {
            // Arrange
            String modeKey = mode.name();
            Integer yearId = 2024;
            String utorId = "testuser";

            LearnerChartFilter mockFilter = new LearnerChartFilter(2024, 1, utorId, mode);
            JsonResponse mockJsonResponse = new JsonResponse();

            when(assessmentModeSvc.getModeForKey(modeKey)).thenReturn(mode);
            when(filterSvc.getFilter(yearId, utorId, mode)).thenReturn(mockFilter);
            when(activityJsonSvc.getActivitiesForTimeLine(mockFilter)).thenReturn(mockJsonResponse);

            // Act
            Object result = activityController.getActivities(modeKey, yearId, utorId, httpServletResponse);

            // Assert
            assertNotNull(result);
            assertEquals(mockJsonResponse, result);
        }
    }
}