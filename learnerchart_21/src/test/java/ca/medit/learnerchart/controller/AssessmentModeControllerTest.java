package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.AssessmentModeAttribute;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.service.AssessmentModeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AssessmentModeControllerTest {

    @Mock
    private AssessmentModeService activityTypeSvc;

    @InjectMocks
    private AssessmentModeController assessmentModeController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(assessmentModeController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getAssessmentModes_Success() throws Exception {
        // Arrange
        List<JsonType> mockJsonTypes = createMockJsonTypeList();
        JsonResponse mockResponse = new JsonResponse().setData(mockJsonTypes);
        when(activityTypeSvc.getAssessmentModes()).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(activityTypeSvc, times(1)).getAssessmentModes();
    }

    @Test
    void getAssessmentModes_ServiceThrowsMedITException_Returns500() throws Exception {
        // Arrange
        MedITException exception = new MedITException("Service error occurred");
        when(activityTypeSvc.getAssessmentModes()).thenThrow(exception);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        verify(activityTypeSvc, times(1)).getAssessmentModes();
    }

    @Test
    void getActivityType_Success() throws Exception {
        // Arrange
        String testKey = "FORMATIVE";
        JsonType mockJsonType = createMockJsonType(testKey);
        JsonResponse mockResponse = new JsonResponse().setData(mockJsonType);
        when(activityTypeSvc.getAssessmentMode(testKey)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes/{key}", testKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(activityTypeSvc, times(1)).getAssessmentMode(testKey);
    }

    @Test
    void getActivityType_ValidKeyWithSpecialCharacters_Success() throws Exception {
        // Arrange
        String testKey = "SUMMATIVE_FINAL";
        JsonType mockJsonType = createMockJsonType(testKey);
        JsonResponse mockResponse = new JsonResponse().setData(mockJsonType);
        when(activityTypeSvc.getAssessmentMode(testKey)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes/{key}", testKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(activityTypeSvc, times(1)).getAssessmentMode(testKey);
    }

    @Test
    void getActivityType_ServiceThrowsMedITException_Returns400() throws Exception {
        // Arrange
        String invalidKey = "INVALID_MODE";
        MedITException exception = new MedITException("Invalid assessment mode: " + invalidKey);
        when(activityTypeSvc.getAssessmentMode(invalidKey)).thenThrow(exception);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes/{key}", invalidKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verify(activityTypeSvc, times(1)).getAssessmentMode(invalidKey);
    }

    @Test
    void getActivityType_ServiceThrowsMedITExceptionForNullKey_Returns400() throws Exception {
        // Arrange
        String nullKey = "null";
        MedITException exception = new MedITException("Assessment mode must not be null");
        when(activityTypeSvc.getAssessmentMode(nullKey)).thenThrow(exception);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes/{key}", nullKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verify(activityTypeSvc, times(1)).getAssessmentMode(nullKey);
    }

    @Test
    void getActivityType_WithWhitespaceKey_CallsService() throws Exception {
        // Arrange
        String whitespaceKey = " FORMATIVE ";
        JsonType mockJsonType = createMockJsonType(whitespaceKey);
        JsonResponse mockResponse = new JsonResponse().setData(mockJsonType);
        when(activityTypeSvc.getAssessmentMode(whitespaceKey)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/assessmentModes/{key}", whitespaceKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(activityTypeSvc, times(1)).getAssessmentMode(whitespaceKey);
    }

    @Test
    void controller_CrossOriginConfiguration_ShouldAllowConfiguredOrigins() throws Exception {
        // This test verifies that the CORS configuration is present
        // The actual CORS testing would require integration tests
        CrossOrigin crossOrigin = AssessmentModeController.class.getAnnotation(CrossOrigin.class);

        assert crossOrigin != null;
        assert Arrays.asList(crossOrigin.origins()).contains("http://localhost:4200");
        assert Arrays.asList(crossOrigin.origins()).contains("https://kind-sky-048ac441e.6.azurestaticapps.net");
        assert crossOrigin.allowCredentials().equals("true");
    }

    @Test
    void controller_RequestMappingConfiguration_ShouldHaveCorrectPath() {
        RequestMapping requestMapping = AssessmentModeController.class.getAnnotation(RequestMapping.class);

        assert requestMapping != null;
        assert Arrays.asList(requestMapping.value()).contains("/appl/assessmentModes");
    }

    // Unit test to verify direct method calls without MockMvc
    @Test
    void getAssessmentModes_DirectMethodCall_Success() {
        // Arrange
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        List<JsonType> mockJsonTypes = createMockJsonTypeList();
        JsonResponse expectedResponse = new JsonResponse().setData(mockJsonTypes);
        when(activityTypeSvc.getAssessmentModes()).thenReturn(expectedResponse);

        // Act
        Object result = assessmentModeController.getAssessmentModes(mockResponse);

        // Assert
        assert result != null;
        assert result instanceof JsonResponse;
        verify(activityTypeSvc, times(1)).getAssessmentModes();
        verify(mockResponse, never()).setStatus(anyInt());
    }

    @Test
    void getAssessmentModes_DirectMethodCall_ExceptionHandled() {
        // Arrange
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        MedITException exception = new MedITException("Service error");
        when(activityTypeSvc.getAssessmentModes()).thenThrow(exception);

        // Act
        Object result = assessmentModeController.getAssessmentModes(mockResponse);

        // Assert
        assert result == null; // Method returns null when exception occurs
        verify(activityTypeSvc, times(1)).getAssessmentModes();
        verify(mockResponse, times(1)).setStatus(500);
    }

    @Test
    void getActivityType_DirectMethodCall_Success() {
        // Arrange
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        String testKey = "FORMATIVE";
        JsonType mockJsonType = createMockJsonType(testKey);
        JsonResponse expectedResponse = new JsonResponse().setData(mockJsonType);
        when(activityTypeSvc.getAssessmentMode(testKey)).thenReturn(expectedResponse);

        // Act
        Object result = assessmentModeController.getActivityType(testKey, mockResponse);

        // Assert
        assert result != null;
        assert result instanceof JsonResponse;
        verify(activityTypeSvc, times(1)).getAssessmentMode(testKey);
        verify(mockResponse, never()).setStatus(anyInt());
    }

    @Test
    void getActivityType_DirectMethodCall_ExceptionHandled() {
        // Arrange
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);
        String testKey = "INVALID";
        MedITException exception = new MedITException("Invalid assessment mode");
        when(activityTypeSvc.getAssessmentMode(testKey)).thenThrow(exception);

        // Act
        Object result = assessmentModeController.getActivityType(testKey, mockResponse);

        // Assert
        assert result == null; // Method returns null when exception occurs
        verify(activityTypeSvc, times(1)).getAssessmentMode(testKey);
        verify(mockResponse, times(1)).setStatus(400);
    }

    // Helper methods for creating mock data
    private List<JsonType> createMockJsonTypeList() {
        JsonType formative = createMockJsonType("FORMATIVE");
        JsonType summative = createMockJsonType("SUMMATIVE");
        return Arrays.asList(formative, summative);
    }

    private JsonType createMockJsonType(String key) {
        JsonType jsonType = new JsonType(key, JsonType.TYPE_ASSESSMENT_MODE);
        AssessmentModeAttribute attributes = new AssessmentModeAttribute()
                .setLabel("Test Label for " + key)
                .setLabelPlural("Test Labels for " + key)
                .setHelpText("Test help text for " + key);
        jsonType.setAttributes(attributes);
        return jsonType;
    }
}