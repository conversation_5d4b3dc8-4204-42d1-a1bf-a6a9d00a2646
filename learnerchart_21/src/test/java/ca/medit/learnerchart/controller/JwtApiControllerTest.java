package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AuthorizationJsonResponse;
import ca.medit.learnerchart.dto.AES;
import ca.medit.learnerchart.dto.AuthRequest;
import ca.medit.learnerchart.security.JwtUtil;
import ca.medit.learnerchart.service.AuthorizationExportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JwtApiControllerTest {

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private AuthorizationExportService authExportSvc;

    @Mock
    private HttpServletResponse httpServletResponse;

    @InjectMocks
    private JwtApiController jwtApiController;

    private final String TEST_CLIENT_ID = "test-client-id";
    private final String TEST_CLIENT_SECRET = "test-client-secret";
    private final String TEST_TOKEN = "test-jwt-token";

    @BeforeEach
    void setUp() {
        // Set private fields using ReflectionTestUtils
        ReflectionTestUtils.setField(jwtApiController, "clientId", TEST_CLIENT_ID);
        ReflectionTestUtils.setField(jwtApiController, "clientSecret", TEST_CLIENT_SECRET);
    }

    @Test
    void getToken_ValidCredentials_ReturnsToken() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(TEST_CLIENT_ID);
        authRequest.setClientSecret(TEST_CLIENT_SECRET);

        when(jwtUtil.generateToken(TEST_CLIENT_ID)).thenReturn(TEST_TOKEN);

        // Act
        Map<String, String> result = jwtApiController.getToken(authRequest);

        // Assert
        assertNotNull(result);
        assertEquals(TEST_TOKEN, result.get("token"));
        verify(jwtUtil).generateToken(TEST_CLIENT_ID);
    }

    @Test
    void getToken_InvalidClientId_ThrowsUnauthorizedException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId("invalid-client-id");
        authRequest.setClientSecret(TEST_CLIENT_SECRET);

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> jwtApiController.getToken(authRequest));

        assertEquals(HttpStatus.UNAUTHORIZED, exception.getStatusCode());
        assertEquals("Invalid credentials", exception.getReason());
        verify(jwtUtil, never()).generateToken(any());
    }

    @Test
    void getToken_InvalidClientSecret_ThrowsUnauthorizedException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(TEST_CLIENT_ID);
        authRequest.setClientSecret("invalid-secret");

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> jwtApiController.getToken(authRequest));

        assertEquals(HttpStatus.UNAUTHORIZED, exception.getStatusCode());
        assertEquals("Invalid credentials", exception.getReason());
        verify(jwtUtil, never()).generateToken(any());
    }

    @Test
    void getToken_NullClientId_ThrowsUnauthorizedException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(null);
        authRequest.setClientSecret(TEST_CLIENT_SECRET);

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> jwtApiController.getToken(authRequest));

        assertEquals(HttpStatus.UNAUTHORIZED, exception.getStatusCode());
        assertEquals("Invalid credentials", exception.getReason());
    }

    @Test
    void getToken_NullClientSecret_ThrowsUnauthorizedException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(TEST_CLIENT_ID);
        authRequest.setClientSecret(null);

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> jwtApiController.getToken(authRequest));

        assertEquals(HttpStatus.UNAUTHORIZED, exception.getStatusCode());
        assertEquals("Invalid credentials", exception.getReason());
    }

    @Test
    void getToken_EmptyCredentials_ThrowsUnauthorizedException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId("");
        authRequest.setClientSecret("");

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> jwtApiController.getToken(authRequest));

        assertEquals(HttpStatus.UNAUTHORIZED, exception.getStatusCode());
        assertEquals("Invalid credentials", exception.getReason());
    }

    @Test
    void securedApi_ReturnsSuccessfulResponse() {
        // Act
        ResponseEntity<String> result = jwtApiController.securedApi();

        // Assert
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals("This is a secured JWT API.", result.getBody());
    }

    @Test
    void getEncryptedAuthorizationJson_Success_ReturnsEncryptedData() throws IOException {
        // Arrange
        AuthorizationJsonResponse authResponse = new AuthorizationJsonResponse();
        authResponse.addAdministrator("<EMAIL>");
        authResponse.addSupervisorForLearner("<EMAIL>", "<EMAIL>");

        when(authExportSvc.getAuthorizationJson()).thenReturn(authResponse);

        String expectedJson = "{\"administrators\":[\"<EMAIL>\"],\"supervisors\":{\"<EMAIL>\":[\"<EMAIL>\"]}}";
        String encryptedData = "encrypted-data";

        try (MockedStatic<AES> aesMock = mockStatic(AES.class)) {
            aesMock.when(() -> AES.encryptString(anyString(), any())).thenReturn(encryptedData);

            // Act
            Object result = jwtApiController.getEncryptedAuthorizationJson(httpServletResponse);

            // Assert
            assertNotNull(result);
            assertEquals(encryptedData, result);
            verify(authExportSvc).getAuthorizationJson();
            verify(httpServletResponse, never()).sendError(anyInt(), anyString());

            // Verify AES encryption was called with some JSON string
            aesMock.verify(() -> AES.encryptString(anyString(), any()), times(1));
        }
    }

    @Test
    void getEncryptedAuthorizationJson_ServiceThrowsMedITException_SendsError() throws IOException {
        // Arrange
        MedITException exception = new MedITException("Test exception");
        when(authExportSvc.getAuthorizationJson()).thenThrow(exception);

        // Act
        Object result = jwtApiController.getEncryptedAuthorizationJson(httpServletResponse);

        // Assert
        assertNull(result);
        verify(authExportSvc).getAuthorizationJson();
        verify(httpServletResponse).sendError(500, "Error getting authorizations");
    }

    @Test
    void getEncryptedAuthorizationJson_EncryptionException_PropagatesException() throws IOException {
        // Arrange
        AuthorizationJsonResponse authResponse = new AuthorizationJsonResponse();
        when(authExportSvc.getAuthorizationJson()).thenReturn(authResponse);

        // Mock AES to throw a runtime exception during encryption
        try (MockedStatic<AES> aesMock = mockStatic(AES.class)) {
            aesMock.when(() -> AES.encryptString(anyString(), any()))
                    .thenThrow(new RuntimeException("Encryption failed"));

            // Act & Assert
            // The RuntimeException should propagate since it's not caught by the controller
            assertThrows(RuntimeException.class,
                    () -> jwtApiController.getEncryptedAuthorizationJson(httpServletResponse));

            verify(authExportSvc).getAuthorizationJson();
            // HttpServletResponse.sendError should NOT be called since exception propagates
            verify(httpServletResponse, never()).sendError(anyInt(), anyString());
        }
    }

    @Test
    void getEncryptedAuthorizationJson_NullAuthorizationResponse_HandlesGracefully() throws IOException {
        // Arrange
        when(authExportSvc.getAuthorizationJson()).thenReturn(null);

        try (MockedStatic<AES> aesMock = mockStatic(AES.class)) {
            aesMock.when(() -> AES.encryptString(anyString(), any())).thenReturn("encrypted-null");

            // Act
            Object result = jwtApiController.getEncryptedAuthorizationJson(httpServletResponse);

            // Assert
            assertNotNull(result);
            assertEquals("encrypted-null", result);
            verify(authExportSvc).getAuthorizationJson();
            verify(httpServletResponse, never()).sendError(anyInt(), anyString());
        }
    }

    @Test
    void getEncryptedAuthorizationJson_EmptyAuthorizationResponse_HandlesGracefully() throws IOException {
        // Arrange
        AuthorizationJsonResponse emptyResponse = new AuthorizationJsonResponse();
        when(authExportSvc.getAuthorizationJson()).thenReturn(emptyResponse);

        try (MockedStatic<AES> aesMock = mockStatic(AES.class)) {
            aesMock.when(() -> AES.encryptString(anyString(), any())).thenReturn("encrypted-empty");

            // Act
            Object result = jwtApiController.getEncryptedAuthorizationJson(httpServletResponse);

            // Assert
            assertNotNull(result);
            assertEquals("encrypted-empty", result);
            verify(authExportSvc).getAuthorizationJson();
            verify(httpServletResponse, never()).sendError(anyInt(), anyString());
        }
    }

    @Test
    void getEncryptedAuthorizationJson_HttpServletResponseThrowsIOException_PropagatesException() throws IOException {
        // Arrange
        MedITException medItException = new MedITException("Service exception");
        when(authExportSvc.getAuthorizationJson()).thenThrow(medItException);
        doThrow(new IOException("Response error")).when(httpServletResponse).sendError(anyInt(), anyString());

        // Act & Assert
        assertThrows(IOException.class,
                () -> jwtApiController.getEncryptedAuthorizationJson(httpServletResponse));

        verify(authExportSvc).getAuthorizationJson();
        verify(httpServletResponse).sendError(500, "Error getting authorizations");
    }

    @Test
    void getToken_JwtUtilThrowsException_PropagatesException() {
        // Arrange
        AuthRequest authRequest = new AuthRequest();
        authRequest.setClientId(TEST_CLIENT_ID);
        authRequest.setClientSecret(TEST_CLIENT_SECRET);

        when(jwtUtil.generateToken(TEST_CLIENT_ID)).thenThrow(new RuntimeException("JWT generation failed"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> jwtApiController.getToken(authRequest));
        verify(jwtUtil).generateToken(TEST_CLIENT_ID);
    }
}