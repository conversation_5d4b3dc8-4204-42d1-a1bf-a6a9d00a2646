package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.controller.MedITErrorController;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

class MedITErrorControllerTest {

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private MedITErrorController medITErrorController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void handleErrorThrowsMedITExceptionWithStatusCode() {
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(404);

        assertThrows(MedITException.class, () -> medITErrorController.handleError(request));
    }

    @Test
    void handleErrorThrowsMedITExceptionWithUnknownErrorWhenStatusIsNull() {
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(null);

        assertThrows(MedITException.class, () -> medITErrorController.handleError(request));
    }
}