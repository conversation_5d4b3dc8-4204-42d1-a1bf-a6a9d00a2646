package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.domain.ProgramYearAttribute;
import ca.medit.learnerchart.service.ProgramYearService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class YearControllerTest {

    @Mock
    private ProgramYearService programYearService;

    @Mock
    private HttpServletResponse httpServletResponse;

    @InjectMocks
    private YearController yearController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(yearController).build();
        objectMapper = new ObjectMapper();
    }

    // Test data setup methods
    private JsonResponse createMockProgramYearsResponse() {
        List<JsonType> years = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            JsonType year = new JsonType(i, "years");
            year.setAttributes(new ProgramYearAttribute("Year " + i));
            years.add(year);
        }
        return new JsonResponse().setData(years);
    }

    private JsonResponse createMockLearnerYearsResponse() {
        List<JsonType> years = new ArrayList<>();
        JsonType year1 = new JsonType(1, "years");
        year1.setAttributes(new ProgramYearAttribute("Year 1"));
        JsonType year2 = new JsonType(2, "years");
        year2.setAttributes(new ProgramYearAttribute("Year 2"));
        years.add(year1);
        years.add(year2);
        return new JsonResponse().setData(years);
    }

    // Tests for getProgramYears() endpoint
    @Test
    void testGetProgramYears_Success() throws Exception {
        // Arrange
        JsonResponse mockResponse = createMockProgramYearsResponse();
        when(programYearService.getProgramYears()).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/years")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(4))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].type").value("years"))
                .andExpect(jsonPath("$.data[0].attributes.label").value("Year 1"))
                .andExpect(jsonPath("$.data[3].attributes.label").value("Year 4"));

        verify(programYearService, times(1)).getProgramYears();
    }

    @Test
    void testGetProgramYears_ServiceThrowsException() throws Exception {
        // Arrange
        when(programYearService.getProgramYears()).thenThrow(new MedITException("Database error"));

        // Act & Assert
        mockMvc.perform(get("/appl/years")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

        verify(programYearService, times(1)).getProgramYears();
    }

    @Test
    void testGetProgramYears_DirectMethodCall_Success() {
        // Arrange
        JsonResponse mockResponse = createMockProgramYearsResponse();
        when(programYearService.getProgramYears()).thenReturn(mockResponse);

        // Act
        Object result = yearController.getProgramYears(httpServletResponse);

        // Assert
        assertNotNull(result);
        assertEquals(mockResponse, result);
        verify(programYearService, times(1)).getProgramYears();
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void testGetProgramYears_DirectMethodCall_Exception() {
        // Arrange
        when(programYearService.getProgramYears()).thenThrow(new MedITException("Service error"));

        // Act
        Object result = yearController.getProgramYears(httpServletResponse);

        // Assert
        assertNull(result);
        verify(programYearService, times(1)).getProgramYears();
        verify(httpServletResponse, times(1)).setStatus(400);
    }

    // Tests for getCurrentAcadYear() endpoint
    @Test
    void testGetCurrentAcadYear_Success_BeforeJuly() throws Exception {
        // This test simulates current date before July (academic year should be previous year)
        mockMvc.perform(get("/appl/years/acadYear")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.matchesPattern("\\d{4}-\\d{2}")));
    }

    @Test
    void testGetCurrentAcadYear_DirectMethodCall_Success() {
        // Act
        Object result = yearController.getCurrentAcadYear(httpServletResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof ResponseEntity);
        ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());

        String acadYear = (String) responseEntity.getBody();
        assertNotNull(acadYear);
        assertTrue(acadYear.matches("\\d{4}-\\d{2}"));

        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void testGetCurrentAcadYear_AcademicYearLogic() {
        // Test the academic year calculation logic
        LocalDate testDate = LocalDate.now();
        int expectedYear = testDate.getMonthValue() >= 7 ? testDate.getYear() : testDate.getYear() - 1;
        String expectedAcadYear = expectedYear + "-" + String.format("%02d", (expectedYear + 1) % 100);

        // Act
        Object result = yearController.getCurrentAcadYear(httpServletResponse);

        // Assert
        ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
        assertEquals(expectedAcadYear, responseEntity.getBody());
    }

    // Tests for getLearnerYearList() endpoint
    @Test
    void testGetLearnerYearList_Success() throws Exception {
        // Arrange
        String learnerId = "testuser123";
        JsonResponse mockResponse = createMockLearnerYearsResponse();
        when(programYearService.getLearnerYearList(learnerId)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/years/{learnerId}", learnerId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].type").value("years"))
                .andExpect(jsonPath("$.data[0].attributes.label").value("Year 1"))
                .andExpect(jsonPath("$.data[1].attributes.label").value("Year 2"));

        verify(programYearService, times(1)).getLearnerYearList(learnerId);
    }

    @Test
    void testGetLearnerYearList_ServiceThrowsException() throws Exception {
        // Arrange
        String learnerId = "testuser123";
        when(programYearService.getLearnerYearList(learnerId)).thenThrow(new MedITException("User not found"));

        // Act & Assert
        mockMvc.perform(get("/appl/years/{learnerId}", learnerId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        verify(programYearService, times(1)).getLearnerYearList(learnerId);
    }

    @Test
    void testGetLearnerYearList_DirectMethodCall_Success() {
        // Arrange
        String learnerId = "testuser123";
        JsonResponse mockResponse = createMockLearnerYearsResponse();
        when(programYearService.getLearnerYearList(learnerId)).thenReturn(mockResponse);

        // Act
        Object result = yearController.getLearnerYearList(learnerId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertEquals(mockResponse, result);
        verify(programYearService, times(1)).getLearnerYearList(learnerId);
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void testGetLearnerYearList_DirectMethodCall_Exception() {
        // Arrange
        String learnerId = "testuser123";
        when(programYearService.getLearnerYearList(learnerId)).thenThrow(new MedITException("Database error"));

        // Act
        Object result = yearController.getLearnerYearList(learnerId, httpServletResponse);

        // Assert
        assertNull(result);
        verify(programYearService, times(1)).getLearnerYearList(learnerId);
        verify(httpServletResponse, times(1)).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    void testGetLearnerYearList_WithSpecialCharactersInLearnerId() throws Exception {
        // Arrange
        String learnerId = "<EMAIL>";
        JsonResponse mockResponse = createMockLearnerYearsResponse();
        when(programYearService.getLearnerYearList(learnerId)).thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/years/{learnerId}", learnerId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(programYearService, times(1)).getLearnerYearList(learnerId);
    }

    // Integration-style tests
    @Test
    void testGetProgramYears_EmptyResponse() throws Exception {
        // Arrange
        JsonResponse emptyResponse = new JsonResponse().setData(new ArrayList<>());
        when(programYearService.getProgramYears()).thenReturn(emptyResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/years")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    @Test
    void testGetLearnerYearList_EmptyResponse() throws Exception {
        // Arrange
        String learnerId = "newuser";
        JsonResponse emptyResponse = new JsonResponse().setData(new ArrayList<>());
        when(programYearService.getLearnerYearList(learnerId)).thenReturn(emptyResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/years/{learnerId}", learnerId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(0));
    }

    // Cross-Origin tests
    @Test
    void testCrossOriginConfiguration() throws Exception {
        // This test verifies that the CORS configuration is properly set
        mockMvc.perform(get("/appl/years")
                        .header("Origin", "http://localhost:4200")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    // Edge case tests
    @Test
    void testGetLearnerYearList_NullLearnerId() {
        // Act
        Object result = yearController.getLearnerYearList(null, httpServletResponse);

        // Assert
        verify(programYearService, times(1)).getLearnerYearList(null);
    }

    @Test
    void testGetLearnerYearList_WithEmptyLearnerIdDirectCall() {
        // Test direct method call with empty string
        // Arrange
        String learnerId = "";
        JsonResponse mockResponse = createMockLearnerYearsResponse();
        when(programYearService.getLearnerYearList(learnerId)).thenReturn(mockResponse);

        // Act
        Object result = yearController.getLearnerYearList(learnerId, httpServletResponse);

        // Assert
        assertNotNull(result);
        assertEquals(mockResponse, result);
        verify(programYearService, times(1)).getLearnerYearList(learnerId);
    }

    @Test
    void testServiceMethodsCalled() {
        // Verify that the correct service methods are called
        yearController.getProgramYears(httpServletResponse);
        verify(programYearService, times(1)).getProgramYears();

        yearController.getCurrentAcadYear(httpServletResponse);
        // getCurrentAcadYear doesn't call service, so no verification needed

        yearController.getLearnerYearList("testuser", httpServletResponse);
        verify(programYearService, times(1)).getLearnerYearList("testuser");
    }
}