package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.entity.UserImage;
import ca.medit.learnerchart.service.LearnerJsonService;
import ca.medit.learnerchart.service.UserService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LearnerControllerTest {

    @Mock
    private LearnerJsonService learnerJsonService;

    @Mock
    private UserService userService;

    @Mock
    private HttpServletResponse httpServletResponse;

    @Mock
    private ServletOutputStream servletOutputStream;

    @InjectMocks
    private LearnerController learnerController;

    private JsonResponse mockLearnersResponse;
    private JsonResponse mockAlertsResponse;
    private JsonResponse mockEmptyResponse;
    private UserImage mockUserImage;

    @BeforeEach
    void setUp() {
        // Create mock JsonResponse objects - adjust based on your actual JsonResponse structure
        mockLearnersResponse = mock(JsonResponse.class);
        mockAlertsResponse = mock(JsonResponse.class);
        mockEmptyResponse = mock(JsonResponse.class);

        mockUserImage = new UserImage();
        mockUserImage.setImageName("test-image.jpg");
        mockUserImage.setImageData("test image data".getBytes());
    }

    @Test
    void getLearners_Success() {
        // Given
        when(learnerJsonService.getLearners()).thenReturn(mockLearnersResponse);

        // When
        Object result = learnerController.getLearners(httpServletResponse);

        // Then
        assertNotNull(result);
        assertEquals(mockLearnersResponse, result);
        verify(learnerJsonService, times(1)).getLearners();
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void getLearners_Exception() {
        // Given
        when(learnerJsonService.getLearners()).thenThrow(new RuntimeException("Database error"));

        // When
        Object result = learnerController.getLearners(httpServletResponse);

        // Then
        assertNull(result);
        verify(learnerJsonService, times(1)).getLearners();
        verify(httpServletResponse, times(1)).setStatus(400);
    }

    @Test
    void getLearnerAlerts_Success() {
        // Given
        String learnerId = "learner123";
        when(learnerJsonService.getLearnerAlerts(learnerId)).thenReturn(mockAlertsResponse);

        // When
        Object result = learnerController.getLearnerAlerts(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertEquals(mockAlertsResponse, result);
        verify(learnerJsonService, times(1)).getLearnerAlerts(learnerId);
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void getLearnerAlerts_Exception() {
        // Given
        String learnerId = "learner123";
        when(learnerJsonService.getLearnerAlerts(learnerId)).thenThrow(new RuntimeException("Service error"));

        // When
        Object result = learnerController.getLearnerAlerts(learnerId, httpServletResponse);

        // Then
        assertNull(result);
        verify(learnerJsonService, times(1)).getLearnerAlerts(learnerId);
        verify(httpServletResponse, times(1)).setStatus(400);
    }

    @Test
    void getLearnerAlerts_EmptyLearnerId() {
        // Given
        String learnerId = "";
        when(learnerJsonService.getLearnerAlerts(learnerId)).thenReturn(mockEmptyResponse);

        // When
        Object result = learnerController.getLearnerAlerts(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertEquals(mockEmptyResponse, result);
        verify(learnerJsonService, times(1)).getLearnerAlerts(learnerId);
    }

    @Test
    void getLearnerPicture_Success() throws IOException {
        // Given
        String learnerId = "learner123";
        when(userService.getLearnerPicture(learnerId)).thenReturn(mockUserImage);
        when(httpServletResponse.getOutputStream()).thenReturn(servletOutputStream);

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNull(result); // Should return null when successful
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setContentType("image/jpeg");
        verify(httpServletResponse, times(1)).setHeader("Content-Disposition", "attachment; filename=\"test-image.jpg\"");
        verify(httpServletResponse, times(1)).getOutputStream();
        verify(httpServletResponse, times(1)).flushBuffer();
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void getLearnerPicture_UserImageNotFound() {
        // Given
        String learnerId = "learner123";
        when(userService.getLearnerPicture(learnerId)).thenReturn(null);

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNull(result);
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, never()).setContentType(anyString());
        verify(httpServletResponse, never()).setHeader(anyString(), anyString());
        verify(httpServletResponse, never()).setStatus(anyInt());
    }

    @Test
    void getLearnerPicture_ServiceException() {
        // Given
        String learnerId = "learner123";
        when(userService.getLearnerPicture(learnerId)).thenThrow(new RuntimeException("Database connection failed"));

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof MedITException);
        MedITException exception = (MedITException) result;
        assertEquals("Error getting file", exception.getMessage());
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    void getLearnerPicture_IOExceptionOnOutputStream() throws IOException {
        // Given
        String learnerId = "learner123";
        when(userService.getLearnerPicture(learnerId)).thenReturn(mockUserImage);
        when(httpServletResponse.getOutputStream()).thenThrow(new IOException("Output stream error"));

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof MedITException);
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    void getLearnerPicture_IOExceptionOnFlushBuffer() throws IOException {
        // Given
        String learnerId = "learner123";
        when(userService.getLearnerPicture(learnerId)).thenReturn(mockUserImage);
        when(httpServletResponse.getOutputStream()).thenReturn(servletOutputStream);
        doThrow(new IOException("Flush buffer error")).when(httpServletResponse).flushBuffer();

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertTrue(result instanceof MedITException);
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }

    @Test
    void getLearnerPicture_WithNullImageName() throws IOException {
        // Given
        String learnerId = "learner123";
        mockUserImage.setImageName(null);
        when(userService.getLearnerPicture(learnerId)).thenReturn(mockUserImage);
        when(httpServletResponse.getOutputStream()).thenReturn(servletOutputStream);

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNull(result);
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setContentType("image/jpeg");
        verify(httpServletResponse, times(1)).setHeader("Content-Disposition", "attachment; filename=\"null\"");
    }

    @Test
    void getLearnerPicture_WithEmptyImageData() throws IOException {
        // Given
        String learnerId = "learner123";
        mockUserImage.setImageData(new byte[0]);
        when(userService.getLearnerPicture(learnerId)).thenReturn(mockUserImage);
        when(httpServletResponse.getOutputStream()).thenReturn(servletOutputStream);

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNull(result);
        verify(userService, times(1)).getLearnerPicture(learnerId);
        verify(httpServletResponse, times(1)).setContentType("image/jpeg");
        verify(httpServletResponse, times(1)).flushBuffer();
    }

    @Test
    void getLearnerAlerts_NullLearnerId() {
        // Given
        String learnerId = null;
        when(learnerJsonService.getLearnerAlerts(learnerId)).thenReturn(mockEmptyResponse);

        // When
        Object result = learnerController.getLearnerAlerts(learnerId, httpServletResponse);

        // Then
        assertNotNull(result);
        assertEquals(mockEmptyResponse, result);
        verify(learnerJsonService, times(1)).getLearnerAlerts(learnerId);
    }

    @Test
    void getLearnerPicture_NullLearnerId() {
        // Given
        String learnerId = null;
        when(userService.getLearnerPicture(learnerId)).thenReturn(null);

        // When
        Object result = learnerController.getLearnerPicture(learnerId, httpServletResponse);

        // Then
        assertNull(result);
        verify(userService, times(1)).getLearnerPicture(learnerId);
    }

    // Integration-style test for cross-origin annotation
    @Test
    void controllerShouldHaveCrossOriginAnnotation() {
        // Given
        Class<LearnerController> controllerClass = LearnerController.class;

        // When
        boolean hasCrossOriginAnnotation = controllerClass.isAnnotationPresent(org.springframework.web.bind.annotation.CrossOrigin.class);

        // Then
        assertTrue(hasCrossOriginAnnotation, "Controller should have @CrossOrigin annotation");
    }

    // Test for request mapping annotation
    @Test
    void controllerShouldHaveRequestMappingAnnotation() {
        // Given
        Class<LearnerController> controllerClass = LearnerController.class;

        // When
        boolean hasRequestMappingAnnotation = controllerClass.isAnnotationPresent(org.springframework.web.bind.annotation.RequestMapping.class);

        // Then
        assertTrue(hasRequestMappingAnnotation, "Controller should have @RequestMapping annotation");
    }
}