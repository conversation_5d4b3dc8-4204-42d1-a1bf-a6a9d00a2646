package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.service.CanMedsRoleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CanMedsRoleControllerTest {

    @Mock
    private CanMedsRoleService canMedsRoleService;

    @InjectMocks
    private CanMedsRoleController canMedsRoleController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(canMedsRoleController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testGetCanMedsRoles_Success() throws Exception {
        // Arrange
        JsonResponse expectedResponse = createMockJsonResponse();
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(expectedResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/canMedsRoles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(canMedsRoleService, times(1)).getCanMedsRoles();
    }

    @Test
    void testGetCanMedsRoles_ServiceThrowsException() throws Exception {
        // Arrange
        when(canMedsRoleService.getCanMedsRoles()).thenThrow(new RuntimeException("Database connection error"));

        // Act & Assert
        mockMvc.perform(get("/appl/canMedsRoles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        verify(canMedsRoleService, times(1)).getCanMedsRoles();
    }

    @Test
    void testGetCanMedsRoles_DirectMethodCall_Success() {
        // Arrange
        JsonResponse expectedResponse = createMockJsonResponse();
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(expectedResponse);
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);

        // Act
        Object result = canMedsRoleController.getCanMedsRoles(mockResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof JsonResponse);
        assertEquals(expectedResponse, result);
        verify(canMedsRoleService, times(1)).getCanMedsRoles();
        verify(mockResponse, never()).setStatus(anyInt());
    }

    @Test
    void testGetCanMedsRoles_DirectMethodCall_ServiceThrowsException() {
        // Arrange
        when(canMedsRoleService.getCanMedsRoles()).thenThrow(new RuntimeException("Service error"));
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);

        // Act
        Object result = canMedsRoleController.getCanMedsRoles(mockResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof JsonResponse);
        verify(canMedsRoleService, times(1)).getCanMedsRoles();
        verify(mockResponse, times(1)).setStatus(500);
    }

    @Test
    void testGetCanMedsRoles_EmptyResponse() throws Exception {
        // Arrange
        JsonResponse emptyResponse = new JsonResponse();
        emptyResponse.setData(new ArrayList<>());
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(emptyResponse);

        // Act & Assert
        mockMvc.perform(get("/appl/canMedsRoles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(canMedsRoleService, times(1)).getCanMedsRoles();
    }

    @Test
    void testGetCanMedsRoles_NullResponse() {
        // Arrange
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(null);
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);

        // Act
        Object result = canMedsRoleController.getCanMedsRoles(mockResponse);

        // Assert
        assertNull(result);
        verify(canMedsRoleService, times(1)).getCanMedsRoles();
        verify(mockResponse, never()).setStatus(anyInt());
    }

    @Test
    void testGetCanMedsRoles_WithDifferentExceptionTypes() {
        // Test with different exception types
        testExceptionHandling(new IllegalArgumentException("Invalid argument"));
        testExceptionHandling(new NullPointerException("Null pointer"));
        testExceptionHandling(new RuntimeException("Runtime error"));
    }

    private void testExceptionHandling(Exception exception) {
        // Arrange
        reset(canMedsRoleService);
        when(canMedsRoleService.getCanMedsRoles()).thenThrow(exception);
        HttpServletResponse mockResponse = mock(HttpServletResponse.class);

        // Act
        Object result = canMedsRoleController.getCanMedsRoles(mockResponse);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof JsonResponse);
        verify(mockResponse, times(1)).setStatus(500);
    }

    @Test
    void testCorsConfiguration() throws Exception {
        // Test that CORS headers are properly configured
        JsonResponse expectedResponse = createMockJsonResponse();
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(expectedResponse);

        mockMvc.perform(get("/appl/canMedsRoles")
                        .header("Origin", "http://localhost:4200")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testRequestMapping() throws Exception {
        // Test the correct request mapping
        JsonResponse expectedResponse = createMockJsonResponse();
        when(canMedsRoleService.getCanMedsRoles()).thenReturn(expectedResponse);

        // Should work with the correct path
        mockMvc.perform(get("/appl/canMedsRoles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // Should not work with incorrect paths
        mockMvc.perform(get("/appl/canMedsRole")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());

        mockMvc.perform(get("/canMedsRoles")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    /**
     * Helper method to create a mock JsonResponse with sample CanMedsMapItem data
     */
    private JsonResponse createMockJsonResponse() {
        JsonResponse jsonResponse = new JsonResponse();
        List<CanMedsMapItem> items = createMockCanMedsMapItems();
        jsonResponse.setData(items);
        return jsonResponse;
    }

    /**
     * Helper method to create sample CanMedsMapItem objects for testing
     */
    private List<CanMedsMapItem> createMockCanMedsMapItems() {
        List<CanMedsMapItem> items = new ArrayList<>();

        CanMedsMapItem item1 = new CanMedsMapItem();
        item1.setParentCplanXid(1L);
        item1.setParentFrameworkDef("Medical Expert");
        item1.setParentLabel("Medical Expert Role");
        item1.setParentValue("ME");
        item1.setChildCplanXid(101L);
        item1.setChildFrameworkDef("Clinical Skills");
        item1.setChildLabel("Clinical Skills Competency");
        item1.setChildValue("CS");
        item1.setLevel(2);
        items.add(item1);

        CanMedsMapItem item2 = new CanMedsMapItem();
        item2.setParentCplanXid(2L);
        item2.setParentFrameworkDef("Communicator");
        item2.setParentLabel("Communicator Role");
        item2.setParentValue("COM");
        item2.setChildCplanXid(102L);
        item2.setChildFrameworkDef("Patient Communication");
        item2.setChildLabel("Patient Communication Competency");
        item2.setChildValue("PC");
        item2.setLevel(2);
        items.add(item2);

        CanMedsMapItem item3 = new CanMedsMapItem();
        item3.setParentCplanXid(3L);
        item3.setParentFrameworkDef("Collaborator");
        item3.setParentLabel("Collaborator Role");
        item3.setParentValue("COL");
        item3.setChildCplanXid(103L);
        item3.setChildFrameworkDef("Team Work");
        item3.setChildLabel("Team Work Competency");
        item3.setChildValue("TW");
        item3.setLevel(2);
        items.add(item3);

        return items;
    }
}