package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.service.ArtifactJsonService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class FocusLearningPlanControllerTest {

    @Mock
    private ArtifactJsonService jsonSvc;

    @InjectMocks
    private FocusLearningPlanController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void getFileArtifactsForLearner_Success() throws Exception {
        // Given
        String utorId = "testUser123";
        JsonResponse mockResponse = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_WithSpecialCharactersInUtorId() throws Exception {
        // Given
        String utorId = "<EMAIL>";
        JsonResponse mockResponse = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_ServiceThrowsException() throws Exception {
        // Given
        String utorId = "testUser123";
        String errorMessage = "Database connection failed";
        RuntimeException serviceException = new RuntimeException(errorMessage);

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenThrow(serviceException);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(result -> {
                    String content = result.getResponse().getContentAsString();
                    MedITException exception = objectMapper.readValue(content, MedITException.class);
                    assert exception.getMessage().contains("Error getting file");
                });

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_ServiceThrowsMedITException() throws Exception {
        // Given
        String utorId = "testUser123";
        MedITException medITException = new MedITException("Custom error message");

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenThrow(medITException);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_EmptyUtorId() throws Exception {
        // Given
        String utorId = "";
        JsonResponse mockResponse = createMockJsonResponse();


        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());

    }

    @Test
    void getFileArtifactsForLearner_NullResponseFromService() throws Exception {
        // Given
        String utorId = "testUser123";

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(""));

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_LongUtorId() throws Exception {
        // Given
        String utorId = "a".repeat(255); // Very long utorId
        JsonResponse mockResponse = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId);
    }

    @Test
    void getFileArtifactsForLearner_VerifyHttpResponseStatusOnError() throws Exception {
        // This test specifically verifies that the HTTP status is set correctly in error scenarios
        String utorId = "testUser123";
        RuntimeException serviceException = new RuntimeException("Service error");

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenThrow(serviceException);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId))
                .andExpect(status().isInternalServerError())
                .andExpect(result -> {
                    // Verify that the response status was explicitly set to 500
                    assert result.getResponse().getStatus() == HttpServletResponse.SC_INTERNAL_SERVER_ERROR;
                });
    }

    @Test
    void getFileArtifactsForLearner_VerifyCorrectServiceMethodCalled() throws Exception {
        // Given
        String utorId = "specificUser456";
        JsonResponse mockResponse = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(mockResponse);

        // When
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId));

        // Then - verify the exact method was called with exact parameter
        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(eq(utorId));
        // Verify no other calls were made with different parameters
        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(anyString());
        verifyNoMoreInteractions(jsonSvc);
    }

    @Test
    void getFileArtifactsForLearner_MultipleCalls() throws Exception {
        // Given
        String utorId1 = "user1";
        String utorId2 = "user2";
        JsonResponse mockResponse1 = createMockJsonResponse();
        JsonResponse mockResponse2 = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId1)).thenReturn(mockResponse1);
        when(jsonSvc.getFocusedLearningPlansForLearner(utorId2)).thenReturn(mockResponse2);

        // When & Then
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId1))
                .andExpect(status().isOk());

        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId2))
                .andExpect(status().isOk());

        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId1);
        verify(jsonSvc, times(1)).getFocusedLearningPlansForLearner(utorId2);
        verify(jsonSvc, times(2)).getFocusedLearningPlansForLearner(anyString());
    }

    @Test
    void controller_CrossOriginConfiguration() throws Exception {
        // This test verifies that the controller accepts requests from configured origins
        String utorId = "testUser123";
        JsonResponse mockResponse = createMockJsonResponse();

        when(jsonSvc.getFocusedLearningPlansForLearner(utorId)).thenReturn(mockResponse);

        // When & Then - test with origin header
        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .header("Origin", "http://localhost:4200"))
                .andExpect(status().isOk());

        mockMvc.perform(get("/appl/artifacts/learningPlan/file/learner/{utorId}", utorId)
                        .header("Origin", "https://kind-sky-048ac441e.6.azurestaticapps.net"))
                .andExpect(status().isOk());
    }

    /**
     * Helper method to create a mock JsonResponse for testing
     * Adjust this method based on your actual JsonResponse class structure
     */
    private JsonResponse createMockJsonResponse() {
        // Create a mock JsonResponse - adjust based on your actual JsonResponse implementation
        JsonResponse response = mock(JsonResponse.class);
        // If JsonResponse has these methods, uncomment and adjust:
        // when(response.isSuccess()).thenReturn(true);
        // when(response.getMessage()).thenReturn("Success");
        return response;

        // Alternative: if JsonResponse is a simple POJO, use:
        // JsonResponse response = new JsonResponse();
        // response.setSuccess(true);  // Only if this method exists
        // response.setMessage("Success");  // Only if this method exists
        // return response;
    }
}