package ca.medit.learnerchart.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.ui.Model;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ShibbolethAttributesControllerTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private Model model;

    @InjectMocks
    private ShibbolethAttributesController shibbolethAttributesController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void listSamlAttributesAddsAllRequestAttributesToModel() {
        Map<String, String> mockAttributes = Map.of("attr1", "value1", "attr2", "value2");
        when(request.getAttribute("allRequestAttributes")).thenReturn(mockAttributes);

        String viewName = shibbolethAttributesController.listSamlAttributes(request, model);

        assertEquals("shibboleth-attributes", viewName);
        verify(model).addAttribute("samlAttributes", mockAttributes);
    }

    @Test
    void listSamlAttributesHandlesNullRequestAttributesGracefully() {
        when(request.getAttribute("allRequestAttributes")).thenReturn(null);

        String viewName = shibbolethAttributesController.listSamlAttributes(request, model);

        assertEquals("shibboleth-attributes", viewName);
        verify(model).addAttribute(eq("samlAttributes"), eq(Collections.emptyMap()));
    }

    @Test
    void listSamlAttributesAddsAllHeadersToModel() {
        Enumeration<String> headerNames = Collections.enumeration(List.of("header1", "header2"));
        when(request.getHeaderNames()).thenReturn(headerNames);
        when(request.getHeader("header1")).thenReturn("value1");
        when(request.getHeader("header2")).thenReturn("value2");

        String viewName = shibbolethAttributesController.listSamlAttributes(request, model);

        assertEquals("shibboleth-attributes", viewName);
        verify(model).addAttribute(eq("headers"), eq(Map.of("header1", "value1", "header2", "value2")));
    }

    @Test
    void listSamlAttributesAddsDebugInfoToModel() {
        List<String> mockFilterDebugInfo = List.of("Filter debug info");
        when(request.getAttribute("filterDebugInfo")).thenReturn(mockFilterDebugInfo);

        String viewName = shibbolethAttributesController.listSamlAttributes(request, model);

        assertEquals("shibboleth-attributes", viewName);
        verify(model).addAttribute(eq("debugInfo"), argThat(debugInfo -> ((List<?>) debugInfo).containsAll(mockFilterDebugInfo)));
    }

    @Test
    void listSamlAttributesHandlesEmptyHeadersAndAttributes() {
        when(request.getAttribute("allRequestAttributes")).thenReturn(null);
        when(request.getHeaderNames()).thenReturn(Collections.emptyEnumeration());

        String viewName = shibbolethAttributesController.listSamlAttributes(request, model);

        assertEquals("shibboleth-attributes", viewName);
        verify(model).addAttribute(eq("samlAttributes"), eq(Collections.emptyMap()));
        verify(model).addAttribute(eq("headers"), eq(Collections.emptyMap()));
    }
}