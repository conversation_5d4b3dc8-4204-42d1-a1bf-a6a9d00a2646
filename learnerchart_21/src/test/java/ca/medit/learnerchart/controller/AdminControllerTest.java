package ca.medit.learnerchart.controller;

import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.FeedSourceTypeEnum;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.service.AssessmentService;
import ca.medit.learnerchart.service.MedSISTaskService;
import ca.medit.learnerchart.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "medsis.file.base=/tmp/test",
        "medsis.file.student=students.json"
})
class AdminControllerTest {

    @Mock
    private MedSISTaskService medSISTaskService;

    @Mock
    private UserService userService;

    @Mock
    private AssessmentService assessmentService;

    @Mock
    private Model model;

    @InjectMocks
    private AdminController adminController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(adminController).build();
        objectMapper = new ObjectMapper();

        // Set up test properties
        ReflectionTestUtils.setField(adminController, "SAVE_DIR", "/tmp/test");
        ReflectionTestUtils.setField(adminController, "FILENAME", "students.json");
    }

    // Dashboard Tests
    @Test
    void testDashboard() {
        // When
        String result = adminController.dashboard(model);

        // Then
        assertEquals("dashboard", result);
        verify(model).addAttribute("currentPage", "dashboard");
        verify(model).addAttribute(eq("chartData"), any(List.class));
        verify(model).addAttribute(eq("chartLabels"), any(List.class));
    }

    @Test
    void testDashboard2() {
        // When
        String result = adminController.dashboard2(model);

        // Then
        assertEquals("dashboard2", result);
        verify(model).addAttribute(eq("chartData"), any(List.class));
        verify(model).addAttribute(eq("chartLabels"), any(List.class));
    }

    @Test
    void testUsers() {
        // When
        String result = adminController.users(model);

        // Then
        assertEquals("users", result);
        verify(model).addAttribute("currentPage", "users");
    }

    @Test
    void testJsonUpload() {
        // When
        String result = adminController.jsonUpload(model);

        // Then
        assertEquals("jsonUpload", result);
        verify(model).addAttribute("jsonUpload", "jsonUpload");
    }

    @Test
    void testIndex() {
        // When
        String result = adminController.index();

        // Then
        assertEquals("index", result);
    }

    // Student Data Import Tests
    @Test
    void testImportStudentData() {
        // When
        String result = adminController.importStudentData(model);

        // Then
        assertEquals("settings", result);
        verify(medSISTaskService).runStudentDataImport();
        verify(model).addAttribute("currentPage", "settings");
        verify(model).addAttribute("msgToBeRendered", "Imported the student data.");
    }

    @Test
    void testImportStudentDataFile_Success() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "test content".getBytes()
        );

        // When
        ResponseEntity<?> response = adminController.importStudentDataFile(file);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        verify(medSISTaskService).runStudentDataImportFile(file);

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Successfully imported student data"));
    }

    @Test
    void testImportStudentDataFile_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "feedFile",
                "",
                MediaType.APPLICATION_JSON_VALUE,
                new byte[0]
        );

        // When
        ResponseEntity<?> response = adminController.importStudentDataFile(emptyFile);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(medSISTaskService, never()).runStudentDataImportFile(any());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertEquals("Please select a file to upload", body.get("message"));
    }

    @Test
    void testImportStudentDataFile_Exception() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "test content".getBytes()
        );

        doThrow(new RuntimeException("Import failed")).when(medSISTaskService).runStudentDataImportFile(file);

        // When
        ResponseEntity<?> response = adminController.importStudentDataFile(file);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Failed to import file"));
    }

    // File Upload Tests
    @Test
    void testUploadFeedData_Success() throws IOException {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "test content".getBytes()
        );

        // Create test directory
        Path testDir = Paths.get("/tmp/test");
        if (!Files.exists(testDir)) {
            Files.createDirectories(testDir);
        }

        // When
        String result = adminController.uploadFeedData(file, "student", model);

        // Then
        assertEquals("dashboard", result);
        verify(model).addAttribute("message", "File uploaded successfully.");

        // Cleanup
        Files.deleteIfExists(Paths.get("/tmp/test/students.json"));
        Files.deleteIfExists(testDir);
    }

    @Test
    void testUploadFeedData_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "feedFile",
                "",
                MediaType.APPLICATION_JSON_VALUE,
                new byte[0]
        );

        // When
        String result = adminController.uploadFeedData(emptyFile, "student", model);

        // Then
        assertEquals("error", result);
        verify(model).addAttribute("error", "No file selected. Please upload a valid file.");
    }

    @Test
    void testUploadFeedData_InvalidUploadType() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "test content".getBytes()
        );

        // When
        String result = adminController.uploadFeedData(file, "invalid", model);

        // Then
        assertEquals("error", result);
        verify(model).addAttribute("error", "Invalid upload type. Only 'Student Data' is supported.");
    }

    // Evaluation Data Import Tests
    @Test
    void testImportStudentEvaluationData() {
        // When
        String result = adminController.importStudentEvaluationData(model);

        // Then
        assertEquals("settings", result);
        verify(medSISTaskService).runEvaluationDataImport();
        verify(model).addAttribute("currentPage", "settings");
        verify(model).addAttribute("msgToBeRendered", "Imported the evaluation data.");
    }

    @Test
    void testImportStudentEvaluationDataFile_Success() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "evaluation.json",
                MediaType.APPLICATION_JSON_VALUE,
                "evaluation content".getBytes()
        );

        // When
        ResponseEntity<?> response = adminController.importStudentEvaluationDataFile(file, model);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        verify(medSISTaskService).runEvaluationDataImportFile(file);

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Successfully imported evaluation data"));
    }

    @Test
    void testImportStudentEvaluationDataFile_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "feedFile",
                "",
                MediaType.APPLICATION_JSON_VALUE,
                new byte[0]
        );

        // When
        ResponseEntity<?> response = adminController.importStudentEvaluationDataFile(emptyFile, model);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(medSISTaskService, never()).runEvaluationDataImportFile(any());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertEquals("Please select a file to upload", body.get("message"));
    }

    @Test
    void testImportStudentEvaluationDataFile_Exception() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "evaluation.json",
                MediaType.APPLICATION_JSON_VALUE,
                "evaluation content".getBytes()
        );

        doThrow(new RuntimeException("Evaluation import failed")).when(medSISTaskService).runEvaluationDataImportFile(file);

        // When
        ResponseEntity<?> response = adminController.importStudentEvaluationDataFile(file, model);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Failed to import file"));
    }

    // Image Import Tests
    @Test
    void testImportStudentImageImport() {
        // When
        String result = adminController.importStudentImageImport(model);

        // Then
        assertEquals("settings", result);
        verify(medSISTaskService).runStudentImageImport();
        verify(model).addAttribute("currentPage", "settings");
        verify(model).addAttribute("msgToBeRendered", "Imported the image data.");
    }

    @Test
    void testImportStudentImageImportFile_Success() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "images.zip",
                "application/zip",
                "zip content".getBytes()
        );

        // When
        ResponseEntity<?> response = adminController.importStudentImageImportFile(file, model);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        verify(medSISTaskService).runStudentImageImportFile(file);

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertTrue((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Successfully imported student-image data"));
    }

    @Test
    void testImportStudentImageImportFile_EmptyFile() {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "feedFile",
                "",
                "application/zip",
                new byte[0]
        );

        // When
        ResponseEntity<?> response = adminController.importStudentImageImportFile(emptyFile, model);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(medSISTaskService, never()).runStudentImageImportFile(any());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertEquals("Please select a file to upload", body.get("message"));
    }

    @Test
    void testImportStudentImageImportFile_Exception() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "images.zip",
                "application/zip",
                "zip content".getBytes()
        );

        doThrow(new RuntimeException("Image import failed")).when(medSISTaskService).runStudentImageImportFile(file);

        // When
        ResponseEntity<?> response = adminController.importStudentImageImportFile(file, model);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

        Map<String, Object> body = (Map<String, Object>) response.getBody();
        assertFalse((Boolean) body.get("success"));
        assertTrue(body.get("message").toString().contains("Failed to import file"));
    }

    // Access Denied Tests
    @Test
    void testRenderAccessDeniedError() {
        // When
        String result = adminController.renderAccessDeniedError(model);

        // Then
        assertEquals("access-denied", result);
        verify(medSISTaskService).runStudentImageImport();
        verify(model).addAttribute("currentPage", "access-denied");
        verify(model).addAttribute("msgToBeRendered", "Access Denied");
        verify(model).addAttribute(eq("msgToBeRenderedInDetails"), contains("403 - You do not have access"));
    }

    // Settings Tests
    @Test
    void testSettings_Success() {
        // When
        String result = adminController.settings(model);

        // Then
        assertEquals("settings", result);
        verify(model).addAttribute("currentPage", "settings");
        verify(assessmentService).saveAssessment(any(Assessment.class));
    }

    @Test
    void testSettings_ExceptionHandling() {
        // Given
        doThrow(new RuntimeException("Assessment save failed")).when(assessmentService).saveAssessment(any(Assessment.class));

        // When
        String result = adminController.settings(model);

        // Then
        assertEquals("settings", result);
        verify(model).addAttribute("currentPage", "settings");
        verify(assessmentService).saveAssessment(any(Assessment.class));
    }

    // Integration Tests using MockMvc
    @Test
    void testDashboardEndpoint() throws Exception {
        mockMvc.perform(get("/dashboard"))
                .andExpect(status().isOk());
    }

    @Test
    void testUsersEndpoint() throws Exception {
        mockMvc.perform(get("/userss"))
                .andExpect(status().isOk());
    }

    @Test
    void testImportEvaluationDataEndpoint() throws Exception {
        mockMvc.perform(get("/importStudentEvaluationData"))
                .andExpect(status().isOk());

        verify(medSISTaskService).runEvaluationDataImport();
    }

    @Test
    void testImportImageDataEndpoint() throws Exception {
        mockMvc.perform(get("/importImageData"))
                .andExpect(status().isOk());

        verify(medSISTaskService).runStudentImageImport();
    }

    @Test
    void testSettingsEndpoint() throws Exception {
        mockMvc.perform(get("/settings"))
                .andExpect(status().isOk());
    }

    // Test Assessment Creation in Settings
    @Test
    void testAssessmentCreationInSettings() {
        // When
        adminController.settings(model);

        // Then
        verify(assessmentService).saveAssessment(argThat(assessment -> {
            assertEquals("Clinical Assessment", assessment.getAssessmentType());
            assertEquals("Clinical Assessment", assessment.getName());
            assertEquals(Integer.valueOf(3), assessment.getProgramYear());
            assertEquals(Long.valueOf(28017096L), assessment.getRemoteId());
            assertEquals("ANS 310Y", assessment.getCourseCode());
            assertEquals("John", assessment.getSupervisorFirstName());
            assertEquals("Doe", assessment.getSupervisorLastName());
            assertEquals(Integer.valueOf(2024), assessment.getAcademicYear());
            assertEquals(FeedSourceTypeEnum.MEDSIS, assessment.getSource());
            assertEquals(AssessmentMode.EVALUATION, assessment.getAssessmentMode());
            assertEquals(Long.valueOf(0L), assessment.getOptlock());
            assertNotNull(assessment.getDueDate());
            assertNotNull(assessment.getUpdatedon());
            assertNotNull(assessment.getCreatedon());
            assertNotNull(assessment.getScores());
            assertEquals(3, assessment.getScores().size());
            return true;
        }));
    }

    // Test Score Creation in Settings
    @Test
    void testScoreCreationInSettings() {
        // When
        adminController.settings(model);

        // Then
        verify(assessmentService).saveAssessment(argThat(assessment -> {
            Set<Score> scores = assessment.getScores();
            assertEquals(3, scores.size());

            boolean hasCommScore = scores.stream().anyMatch(score ->
                    "COMM".equals(score.getCplanXid()) &&
                            Float.valueOf(3.0f).equals(score.getNumberOfItems()) &&
                            "MEET_REQUIREMENTS".equals(score.getRawValue()) &&
                            "braytess".equals(score.getUtorid()) &&
                            FeedSourceTypeEnum.MEDSIS.equals(score.getSource())
            );

            boolean hasAdvScore = scores.stream().anyMatch(score ->
                    "ADV".equals(score.getCplanXid()) &&
                            Float.valueOf(2.0f).equals(score.getNumberOfItems()) &&
                            "MEET_REQUIREMENTS".equals(score.getRawValue()) &&
                            "braytess".equals(score.getUtorid()) &&
                            FeedSourceTypeEnum.MEDSIS.equals(score.getSource())
            );

            boolean hasFinalScore = scores.stream().anyMatch(score ->
                    "FINAL_SCORE_XID".equals(score.getCplanXid()) &&
                            Float.valueOf(11.0f).equals(score.getNumberOfItems()) &&
                            "MEET_REQUIREMENTS".equals(score.getRawValue()) &&
                            "braytess".equals(score.getUtorid()) &&
                            FeedSourceTypeEnum.MEDSIS.equals(score.getSource())
            );

            return hasCommScore && hasAdvScore && hasFinalScore;
        }));
    }

    // Test File Upload with Different Content Types
    @Test
    void testUploadFeedDataWithDifferentContentTypes() throws IOException {
        // Given
        MockMultipartFile csvFile = new MockMultipartFile(
                "feedFile",
                "test.csv",
                "text/csv",
                "csv content".getBytes()
        );

        // Create test directory
        Path testDir = Paths.get("/tmp/test");
        if (!Files.exists(testDir)) {
            Files.createDirectories(testDir);
        }

        // When
        String result = adminController.uploadFeedData(csvFile, "student", model);

        // Then
        assertEquals("dashboard", result);
        verify(model).addAttribute("message", "File uploaded successfully.");

        // Cleanup
        Files.deleteIfExists(Paths.get("/tmp/test/students.json"));
        Files.deleteIfExists(testDir);
    }

    // Test Multipart File Upload API Endpoints
    @Test
    void testMultipartFileUploadEndpoints() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "feedFile",
                "test.json",
                MediaType.APPLICATION_JSON_VALUE,
                "test content".getBytes()
        );

        // Test student data import endpoint
        mockMvc.perform(multipart("/appl/admin/importStudentData").file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // Test evaluation data import endpoint
        mockMvc.perform(multipart("/appl/admin/importEvaluationData").file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // Test image data import endpoint
        mockMvc.perform(multipart("/appl/admin/importImageData").file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
}