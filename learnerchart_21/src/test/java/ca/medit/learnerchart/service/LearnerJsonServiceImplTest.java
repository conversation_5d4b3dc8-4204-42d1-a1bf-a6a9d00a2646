package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.*;
import ca.medit.learnerchart.dto.*;
import ca.medit.learnerchart.entity.Group;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.repository.UserRepository;
import ca.medit.learnerchart.service.converter.TransformerAlertJson;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LearnerJsonServiceImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private UserRepository userRepository;

    @Mock
    private Query query;

    @InjectMocks
    private LearnerJsonServiceImpl learnerJsonService;

    private User testUser;
    private LearnerDTO testLearnerDTO;
    private UserDTO testUserDTO;
    private GroupDTO testGroupDTO;
    private AlertDTO testAlertDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        testUser = new User();
        testUser.setUsersId(1L);
        testUser.setUtorid("testuser");
        testUser.setFirstName("Test");
        testUser.setLastName("User");
        testUser.setEmail("<EMAIL>");

        testUserDTO = new UserDTO();
        testUserDTO.setId(1L);
        testUserDTO.setUtorId("testuser");
        testUserDTO.setFirstName("Test");
        testUserDTO.setLastName("User");
        testUserDTO.setEmail("<EMAIL>");

        testGroupDTO = new GroupDTO();
        testGroupDTO.setId(1L);
        testGroupDTO.setName("Test Group");

        testLearnerDTO = new LearnerDTO();
        testLearnerDTO.setAuthUser(testUserDTO);
        testLearnerDTO.setCohortName("1T9");
        testLearnerDTO.setPictureName("test.jpg");
        testLearnerDTO.addGroup(testGroupDTO);

        testAlertDTO = new AlertDTO();
        testAlertDTO.setId(1L);
        testAlertDTO.setType("TEST");
        testAlertDTO.setTitle("Test Alert");
        testAlertDTO.setAlertType(AlertType.INFO);
        testAlertDTO.setDateTime(new LocalDateTime());
        testAlertDTO.setYearId(2024);

        List<AlertDTO> alerts = Arrays.asList(testAlertDTO);
        testLearnerDTO.setAlerts(alerts);
    }

    @Test
    void testGetLearners_Success() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // Create mock row matching the SQL query structure:
        // u.users_id, u.username, u.first_name, u.last_name, u.email, u.cohort_name, u.picture_name,
        // g.GROUP_ID, g.NAME, sc.users_id, sc.username, sc.first_name, sc.last_name, sc.email,
        // su.users_id, su.username, su.first_name, su.last_name, su.email
        Object[] mockRow = new Object[]{
                BigInteger.valueOf(1L), "testuser", "Test", "User", "<EMAIL>", "1T9", "test.jpg",
                BigInteger.valueOf(1L), "Test Group",
                BigInteger.valueOf(2L), "scholar1", "Scholar", "One", "<EMAIL>",
                BigInteger.valueOf(3L), "supporter1", "Supporter", "One", "<EMAIL>"
        };

        List<Object[]> mockRows = new ArrayList<>();
        mockRows.add(mockRow);
        when(query.getResultList()).thenReturn(mockRows);

        // Mock alert query
        Query alertQuery = mock(Query.class);
        when(entityManager.createNativeQuery(contains("SELECT alert_id"))).thenReturn(alertQuery);
        when(alertQuery.setParameter("userId", 1L)).thenReturn(alertQuery);

        Object[] alertRow = new Object[]{
                BigInteger.valueOf(1L), BigInteger.valueOf(100L), "TEST", "Test Alert",
                "WRITTEN", "INFO", new Timestamp(System.currentTimeMillis()), 20242
        };

        List<Object[]> alertRows = new ArrayList<>();
        alertRows.add(alertRow);
        when(alertQuery.getResultList()).thenReturn(alertRows);

        // Act
        JsonResponse result = learnerJsonService.getLearners();

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof List);

        @SuppressWarnings("unchecked")
        List<JsonType> learners = (List<JsonType>) result.getData();
        assertFalse(learners.isEmpty());

        JsonType learner = learners.get(0);
        assertEquals(JsonType.TYPE_LEARNER, learner.getType());
        assertNotNull(learner.getAttributes());
        assertNotNull(learner.getRelationships());
    }

    @Test
    void testGetLearners_ExceptionHandling() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            learnerJsonService.getLearners();
        });

        assertEquals("Database error", exception.getMessage());
    }

    @Test
    void testGetAllLearners_Success() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(eq("group"), eq(Group.LEARNER))).thenReturn(query);

        Object[] mockRow = new Object[]{
                BigInteger.valueOf(1L), "testuser", "Test", "User", "<EMAIL>", "1T9", "test.jpg",
                BigInteger.valueOf(1L), "Test Group",
                BigInteger.valueOf(2L), "scholar1", "Scholar", "One", "<EMAIL>",
                BigInteger.valueOf(3L), "supporter1", "Supporter", "One", "<EMAIL>"
        };

        List<Object[]> mockRows = new ArrayList<>();
        mockRows.add(mockRow);
        when(query.getResultList()).thenReturn(mockRows);

        // Mock alert query
        Query alertQuery = mock(Query.class);
        when(entityManager.createNativeQuery(contains("SELECT alert_id"))).thenReturn(alertQuery);
        when(alertQuery.setParameter("userId", 1L)).thenReturn(alertQuery);
        when(alertQuery.getResultList()).thenReturn(Collections.emptyList());

        // Act
        List<LearnerDTO> result = learnerJsonService.getAllLearners();

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());

        LearnerDTO learner = result.get(0);
        assertEquals("testuser", learner.getUtorId());
        assertEquals("Test", learner.getFirstName());
        assertEquals("User", learner.getLastName());
    }

    @Test
    void testGetAllLearners_EmptyResult() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(Collections.emptyList());

        // Act
        List<LearnerDTO> result = learnerJsonService.getAllLearners();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetAllLearners_RuntimeException() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Connection failed"));

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            learnerJsonService.getAllLearners();
        });

        assertEquals("Connection failed", exception.getMessage());
    }

    @Test
    void testGetNotificationsForUser_Success() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter("userId", 1L)).thenReturn(query);

        Object[] alertRow = new Object[]{
                BigInteger.valueOf(1L), BigInteger.valueOf(100L), "TEST", "Test Alert",
                "WRITTEN", "INFO", new Timestamp(System.currentTimeMillis()), 2024
        };

        List<Object[]> alertRows = new ArrayList<>();
        alertRows.add(alertRow);
        when(query.getResultList()).thenReturn(alertRows);

        // Act
        List<AlertDTO> result = learnerJsonService.getNotificationsForUser(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        AlertDTO alert = result.get(0);
        assertEquals(1L, alert.getId());
        assertEquals(100L, alert.getReferenceId());
        assertEquals("TEST", alert.getType());
        assertEquals("Test Alert", alert.getTitle());
        assertEquals(AssessmentMode.WRITTEN, alert.getAssessmentMode());
        assertEquals(AlertType.INFO, alert.getAlertType());
        assertEquals(2024, alert.getYearId());
        assertNotNull(alert.getDateTime());
    }

    @Test
    void testGetNotificationsForUser_WithNullValues() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter("userId", 1L)).thenReturn(query);

        Object[] alertRow = new Object[]{
                BigInteger.valueOf(1L), null, "TEST", "Test Alert",
                null, "INFO", new Timestamp(System.currentTimeMillis()), 2024
        };

        List<Object[]> alertRows = new ArrayList<>();
        alertRows.add(alertRow);
        when(query.getResultList()).thenReturn(alertRows);

        // Act
        List<AlertDTO> result = learnerJsonService.getNotificationsForUser(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        AlertDTO alert = result.get(0);
        assertEquals(1L, alert.getId());
        assertEquals(0,alert.getReferenceId());
        assertNull(alert.getAssessmentMode());
        assertEquals(AlertType.INFO, alert.getAlertType());
    }

    @Test
    void testGetNotificationsForUser_RuntimeException() {
        // Arrange
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter("userId", 1L)).thenThrow(new RuntimeException("Query failed"));

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            learnerJsonService.getNotificationsForUser(1L);
        });

        assertTrue(exception.getMessage().contains("Query failed"));
    }

    @Test
    void testConvertToLocalDateTime_WithTimestamp() {
        // Arrange
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        // Act
        LocalDateTime result = LearnerJsonServiceImpl.convertToLocalDateTime(timestamp);

        // Assert
        assertNotNull(result);
    }

    @Test
    void testConvertToLocalDateTime_WithLocalDateTime() {
        // Arrange
        LocalDateTime originalDateTime = new LocalDateTime();

        // Act
        LocalDateTime result = LearnerJsonServiceImpl.convertToLocalDateTime(originalDateTime);

        // Assert
        assertNotNull(result);
        assertEquals(originalDateTime, result);
    }

    @Test
    void testConvertToLocalDateTime_WithString() {
        // Arrange
        String dateString = "2024-01-15 10:30:45.123";

        // Act
        LocalDateTime result = LearnerJsonServiceImpl.convertToLocalDateTime(dateString);

        // Assert
        assertNotNull(result);
    }

    @Test
    void testConvertToLocalDateTime_WithInvalidString() {
        // Arrange
        String invalidDateString = "invalid-date";

        // Act
        LocalDateTime result = LearnerJsonServiceImpl.convertToLocalDateTime(invalidDateString);

        // Assert
        assertNull(result);
    }

    @Test
    void testConvertToLocalDateTime_WithNull() {
        // Act
        LocalDateTime result = LearnerJsonServiceImpl.convertToLocalDateTime(null);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetLearnerAlerts_Success() {
        // Arrange
        String utorId = "testuser";
        when(userRepository.findByUtorid(utorId)).thenReturn(Optional.of(testUser));

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter("userId", 1L)).thenReturn(query);

        Object[] alertRow = new Object[]{
                BigInteger.valueOf(1L), BigInteger.valueOf(100L), "TEST", "Test Alert",
                "WRITTEN", "INFO", new Timestamp(System.currentTimeMillis()), 20242
        };

        List<Object[]> alertRows = new ArrayList<>();
        alertRows.add(alertRow);
        when(query.getResultList()).thenReturn(alertRows);

        // Mock static method
        try (MockedStatic<TransformerAlertJson> mockedTransformer = mockStatic(TransformerAlertJson.class)) {
            JsonResponse mockResponse = new JsonResponse();
            mockedTransformer.when(() -> TransformerAlertJson.toAlertJson(any(List.class)))
                    .thenReturn(mockResponse);

            // Act
            JsonResponse result = learnerJsonService.getLearnerAlerts(utorId);

            // Assert
            assertNotNull(result);
            assertEquals(mockResponse, result);
        }
    }

    @Test
    void testGetLearnerAlerts_UserNotFound() {
        // Arrange
        String utorId = "nonexistent";
        when(userRepository.findByUtorid(utorId)).thenReturn(Optional.empty());

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            learnerJsonService.getLearnerAlerts(utorId);
        });

        assertTrue(exception.getMessage().contains("User not found with UtorId"));
        assertTrue(exception.getCause() instanceof UsernameNotFoundException);
    }

    @Test
    void testGetLearnerAlerts_DatabaseException() {
        // Arrange
        String utorId = "testuser";
        when(userRepository.findByUtorid(utorId)).thenReturn(Optional.of(testUser));
        when(entityManager.createNativeQuery(anyString())).thenThrow(new RuntimeException("DB Error"));

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            learnerJsonService.getLearnerAlerts(utorId);
        });
        assertTrue(exception.getMessage().contains("DB Error"));
    }

    @Test
    void testGetLearnerYearList_ReturnsNull() {
        // Act
        JsonResponse result = learnerJsonService.getLearnerYearList("testuser");

        // Assert
        assertNull(result);
    }

    @Test
    void testCreateLearnerAttributes() throws Exception {
        // Arrange
        UserDTO scholar = new UserDTO();
        scholar.setUtorId("scholar1");
        scholar.setFirstName("Scholar");
        scholar.setLastName("One");
        scholar.setEmail("<EMAIL>");

        testLearnerDTO.addScholar(scholar);

        // Use reflection to access private method
        java.lang.reflect.Method method = LearnerJsonServiceImpl.class
                .getDeclaredMethod("createLearnerAttributes", LearnerDTO.class);
        method.setAccessible(true);

        try (MockedStatic<TransformerAlertJson> mockedTransformer = mockStatic(TransformerAlertJson.class)) {
            JsonResponse mockAlertResponse = new JsonResponse();
            mockedTransformer.when(() -> TransformerAlertJson.toAlertJson(any(List.class)))
                    .thenReturn(mockAlertResponse);

            // Act
            LearnerAttribute result = (LearnerAttribute) method.invoke(learnerJsonService, testLearnerDTO);

            // Assert
            assertNotNull(result);
            assertEquals("Test", result.getFirstName());
            assertEquals("User", result.getLastName());
            assertEquals("<EMAIL>", result.getEmail());
            assertEquals("testuser", result.getUtorid());
            assertEquals("1T9", result.getCohortCode());
            assertEquals("/appl/learners/testuser/image", result.getImageLink());
            assertNotNull(result.getAlerts());
        }
    }

    @Test
    void testCreateLearnerAttributes_WithNullPicture() throws Exception {
        // Arrange
        testLearnerDTO.setPictureName(null);

        // Use reflection to access private method
        java.lang.reflect.Method method = LearnerJsonServiceImpl.class
                .getDeclaredMethod("createLearnerAttributes", LearnerDTO.class);
        method.setAccessible(true);

        try (MockedStatic<TransformerAlertJson> mockedTransformer = mockStatic(TransformerAlertJson.class)) {
            JsonResponse mockAlertResponse = new JsonResponse();
            mockedTransformer.when(() -> TransformerAlertJson.toAlertJson(any(List.class)))
                    .thenReturn(mockAlertResponse);

            // Act
            LearnerAttribute result = (LearnerAttribute) method.invoke(learnerJsonService, testLearnerDTO);

            // Assert
            assertNotNull(result);
            assertNull(result.getImageLink());
        }
    }

    @Test
    void testCreateJsonResponseFromAuthUsers() throws Exception {
        // Arrange
        UserDTO user1 = new UserDTO();
        user1.setUtorId("user1");
        user1.setFirstName("User");
        user1.setLastName("One");
        user1.setEmail("<EMAIL>");

        UserDTO user2 = new UserDTO();
        user2.setUtorId("user2");
        user2.setFirstName("User");
        user2.setLastName("Two");
        user2.setEmail("<EMAIL>");

        List<UserDTO> users = Arrays.asList(user1, user2);

        // Use reflection to access private method
        java.lang.reflect.Method method = LearnerJsonServiceImpl.class
                .getDeclaredMethod("createJsonResponseFromAuthUsers", List.class);
        method.setAccessible(true);

        // Act
        JsonResponse result = (JsonResponse) method.invoke(learnerJsonService, users);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof List);

        @SuppressWarnings("unchecked")
        List<JsonType> jsonTypes = (List<JsonType>) result.getData();
        assertEquals(2, jsonTypes.size());

        JsonType firstUser = jsonTypes.get(0);
        assertEquals("user1", firstUser.getId());
        assertEquals(JsonType.TYPE_USER, firstUser.getType());
        assertNotNull(firstUser.getAttributes());
    }

    @Test
    void testCreateJsonResponseFromAuthUsers_WithNullList() throws Exception {
        // Use reflection to access private method
        java.lang.reflect.Method method = LearnerJsonServiceImpl.class
                .getDeclaredMethod("createJsonResponseFromAuthUsers", List.class);
        method.setAccessible(true);

        // Act
        JsonResponse result = (JsonResponse) method.invoke(learnerJsonService, (List<UserDTO>) null);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof List);

        @SuppressWarnings("unchecked")
        List<JsonType> jsonTypes = (List<JsonType>) result.getData();
        assertTrue(jsonTypes.isEmpty());
    }

    @Test
    void testCreateGroupRxpsForLearner() throws Exception {
        // Arrange
        GroupDTO group1 = new GroupDTO();
        group1.setId(2L);
        group1.setName("Group One");

        GroupDTO group2 = new GroupDTO();
        group2.setId(3L);
        group2.setName("Group Two");

        testLearnerDTO.addGroup(group1);
        testLearnerDTO.addGroup(group2);

        // Use reflection to access private method
        java.lang.reflect.Method method = LearnerJsonServiceImpl.class
                .getDeclaredMethod("createGroupRxpsForLearner", LearnerDTO.class);
        method.setAccessible(true);

        // Act
        JsonResponse result = (JsonResponse) method.invoke(learnerJsonService, testLearnerDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof List);

        @SuppressWarnings("unchecked")
        List<JsonType> groups = (List<JsonType>) result.getData();
        assertEquals(3, groups.size()); // Original testGroupDTO plus 2 added

        JsonType firstGroup = groups.get(0);
        assertEquals(JsonType.TYPE_GROUP, firstGroup.getType());
        assertNotNull(firstGroup.getAttributes());
    }
}