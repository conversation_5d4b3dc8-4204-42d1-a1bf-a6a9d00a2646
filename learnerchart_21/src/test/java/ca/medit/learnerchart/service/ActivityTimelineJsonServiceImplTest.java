package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.ActivityFileAttribute;
import ca.medit.learnerchart.domain.ActivityRelationshipWithFeedbacks;
import ca.medit.learnerchart.domain.ActivityScoreEnum;
import ca.medit.learnerchart.domain.ActivityTimelineJsonData;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.dto.CohortNumericScoreAverageMap;
import ca.medit.learnerchart.dto.SubmissionFeedback;
import ca.medit.learnerchart.dto.SubmissionFileInfo;
import ca.medit.learnerchart.dto.TimelineActivity;
import ca.medit.learnerchart.entity.Assessment;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.repository.AssessmentRepository;
import ca.medit.learnerchart.service.converter.TransformerActivityFeedbackJson;
import ca.medit.learnerchart.service.converter.TransformerAssignmentActivityJson;
import ca.medit.learnerchart.service.converter.TransformerTimelineActivityJson;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActivityTimelineJsonServiceImplTest {

    @Mock
    private ActivityService activityService;

    @Mock
    private AssessmentRepository assessmentRepository;

    @Mock
    private OasesService oasesService;

    @Mock
    private TransformerTimelineActivityJson timelineTransformer;

    @Mock
    private TransformerAssignmentActivityJson assignmentTransformer;

    @InjectMocks
    private ActivityTimelineJsonServiceImpl activityTimelineJsonService;

    private LearnerChartFilter testFilter;
    private Assessment testAssessment;
    private List<CanMedsMapItem> testCanMedsRoles;

    @BeforeEach
    void setUp() {
        testFilter = new LearnerChartFilter(2024, 1, "testuser", AssessmentMode.WRITTEN);
        testFilter.setLearnerId(123L);

        testAssessment = new Assessment();
        testAssessment.setAssessmentId(1L);
        testAssessment.setName("Test Assessment");

        testCanMedsRoles = createTestCanMedsRoles();
        ReflectionTestUtils.setField(activityTimelineJsonService, "canMedsRoles", testCanMedsRoles);
    }

    private List<CanMedsMapItem> createTestCanMedsRoles() {
        CanMedsMapItem role1 = new CanMedsMapItem(1L, "framework1", "Medical Expert", "ME",
                2L, "subframework1", "Medical Knowledge", "MK", 2);
        CanMedsMapItem role2 = new CanMedsMapItem(3L, "framework2", "Communicator", "COM",
                4L, "subframework2", "Patient Communication", "PC", 2);
        return Arrays.asList(role1, role2);
    }

    @Test
    void getActivitiesForTimeLine_Success() {
        // Arrange
        List<TimelineActivity> activities = createTestTimelineActivities();
        CohortNumericScoreAverageMap averageMap = new CohortNumericScoreAverageMap();
        ActivityTimelineJsonData expectedData = new ActivityTimelineJsonData();

        when(activityService.getTimelineActivities(testFilter)).thenReturn(activities);
        when(activityService.getActivityAverageMap(testFilter)).thenReturn(averageMap);

        try (MockedConstruction<TransformerTimelineActivityJson> mockedConstruction =
                     mockConstruction(TransformerTimelineActivityJson.class, (mock, context) -> {
                         when(mock.getData()).thenReturn(expectedData);
                     })) {

            // Act
            JsonResponse result = activityTimelineJsonService.getActivitiesForTimeLine(testFilter);

            // Assert
            assertNotNull(result);
            assertEquals(expectedData, result.getData());
            verify(activityService).getTimelineActivities(testFilter);
            verify(activityService).getActivityAverageMap(testFilter);

            // Verify constructor was called with correct parameters
            List<TransformerTimelineActivityJson> constructed = mockedConstruction.constructed();
            assertEquals(1, constructed.size());
        }
    }
    @Test
    void getActivitiesForTimeLine_EmptyActivities() {
        // Arrange
        List<TimelineActivity> emptyActivities = Collections.emptyList();
        CohortNumericScoreAverageMap averageMap = new CohortNumericScoreAverageMap();
        ActivityTimelineJsonData expectedData = new ActivityTimelineJsonData();

        when(activityService.getTimelineActivities(testFilter)).thenReturn(emptyActivities);
        when(activityService.getActivityAverageMap(testFilter)).thenReturn(averageMap);

        try (MockedConstruction<TransformerTimelineActivityJson> mockedConstruction =
                     mockConstruction(TransformerTimelineActivityJson.class, (mock, context) -> {
                         when(mock.getData()).thenReturn(expectedData);
                     })) {

            // Act
            JsonResponse result = activityTimelineJsonService.getActivitiesForTimeLine(testFilter);

            // Assert
            assertNotNull(result);
            assertEquals(expectedData, result.getData());

            // Verify constructor was called
            List<TransformerTimelineActivityJson> constructed = mockedConstruction.constructed();
            assertEquals(1, constructed.size());
        }
    }

    @Test
    void getAssignmentActivityDetail_Success_WithSubmissionAndFeedbacks() {
        // Arrange
        long activityId = 1L;
        String username = "testuser";
        List<Score> scores = createTestScores();
        SubmissionFileInfo submissionFileInfo = createTestSubmissionFileInfo();
        List<SubmissionFeedback> feedbacks = createTestSubmissionFeedbacks();

        // Create proper mocks using your existing helper methods
        JsonType skeletonJsonType = createMockSkeletonJsonType(true);
        ActivityRelationshipWithFeedbacks relationships = (ActivityRelationshipWithFeedbacks) skeletonJsonType.getRelationships();
        JsonResponse filesResponse = relationships.getFiles();
        List<JsonType> files = (List<JsonType>) filesResponse.getData();

        // Setup repository and service mocks
        when(assessmentRepository.getById(activityId)).thenReturn(testAssessment);
        when(activityService.getAsessmentScoresForLearner(activityId, username)).thenReturn(scores);
        when(oasesService.getSubmissionFileInfo(1L)).thenReturn(submissionFileInfo);
        when(oasesService.getSubmissionFeedbacks(submissionFileInfo.getSubmissionId())).thenReturn(feedbacks);

        try (MockedConstruction<TransformerAssignmentActivityJson> mockedConstruction =
                     mockConstruction(TransformerAssignmentActivityJson.class, (mock, context) -> {
                         when(mock.toAssignmentActivitySkeletonJsonType(testAssessment, scores, testCanMedsRoles))
                                 .thenReturn(skeletonJsonType);
                     });
             MockedStatic<TransformerActivityFeedbackJson> mockedFeedbackTransformer = mockStatic(TransformerActivityFeedbackJson.class)) {

            JsonResponse feedbackJsonResponse = new JsonResponse();
            mockedFeedbackTransformer.when(() -> TransformerActivityFeedbackJson.toActivityFeedbackJson(feedbacks))
                    .thenReturn(feedbackJsonResponse);

            // Act
            JsonResponse result = activityTimelineJsonService.getAssignmentActivityDetail(activityId, username);

            // Assert
            assertNotNull(result);
            assertEquals(skeletonJsonType, result.getData());
            verify(assessmentRepository).getById(activityId);
            verify(activityService).getAsessmentScoresForLearner(activityId, username);
            verify(oasesService).getSubmissionFileInfo(1L);
            verify(oasesService).getSubmissionFeedbacks(submissionFileInfo.getSubmissionId());
            verify(relationships).setFeedbacks(feedbackJsonResponse);

            // Verify that the constructor was called with the correct parameters
            List<TransformerAssignmentActivityJson> constructed = mockedConstruction.constructed();
            assertEquals(1, constructed.size());
        }
    }

    @Test
    void getAssignmentActivityDetail_AssessmentNotFound() {
        // Arrange
        long activityId = 999L;
        String username = "testuser";

        when(assessmentRepository.getById(activityId)).thenReturn(null);

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            activityTimelineJsonService.getAssignmentActivityDetail(activityId, username);
        });

        assertEquals("Assessment not found. Activity ID = " + activityId, exception.getMessage());
        verify(assessmentRepository).getById(activityId);
        verify(activityService, never()).getAsessmentScoresForLearner(anyLong(), anyString());
    }

    @Test
    void getAssignmentActivityDetail_ServiceException() {
        // Arrange
        long activityId = 1L;
        String username = "testuser";

        when(assessmentRepository.getById(activityId)).thenReturn(testAssessment);
        when(activityService.getAsessmentScoresForLearner(activityId, username))
                .thenThrow(new MedITException("Database error"));

        // Act & Assert
        MedITException exception = assertThrows(MedITException.class, () -> {
            activityTimelineJsonService.getAssignmentActivityDetail(activityId, username);
        });

        assertEquals("Database error", exception.getMessage());
    }

    // Helper methods for creating test data
    private List<TimelineActivity> createTestTimelineActivities() {
        LocalDateTime now = LocalDateTime.now();

        TimelineActivity activity1 = new TimelineActivity(1L, "Test Activity 1", now, ActivityScoreEnum.NUMERIC);
        activity1.setAssessmentType("ASSIGNMENT");
        activity1.setPointsAvailable(100.0);
        activity1.setPointsEarned(85.0);
        activity1.setQuestionCount(10);
        activity1.setRawScore("85.0");

        TimelineActivity activity2 = new TimelineActivity(2L, "Test Activity 2", now.plusDays(1), ActivityScoreEnum.NUMERIC);
        activity2.setAssessmentType("QUIZ");
        activity2.setPointsAvailable(50.0);
        activity2.setPointsEarned(45.0);
        activity2.setQuestionCount(5);
        activity2.setRawScore("A-");

        return Arrays.asList(activity1, activity2);
    }

    private List<Score> createTestScores() {
        Score score1 = new Score();
        score1.setScoreId(1L);
        score1.setPointsEarned(85.0f);
        score1.setPointsAvailable(100.0f);
        score1.setRawValue("85.0");

        Score score2 = new Score();
        score2.setScoreId(2L);
        score2.setPointsEarned(92.0f);
        score2.setPointsAvailable(100.0f);
        score2.setRawValue("92.0");

        return Arrays.asList(score1, score2);
    }

    private SubmissionFileInfo createTestSubmissionFileInfo() {
        return new SubmissionFileInfo(1L, 1024L, "test.pdf",
                LocalDateTime.now(), 100L);
    }

    private List<SubmissionFeedback> createTestSubmissionFeedbacks() {
        SubmissionFeedback feedback1 = new SubmissionFeedback();
        feedback1.setAssessmentId(1L);
        feedback1.setAssessmentType("Assignment");
        feedback1.setGrade("A");
        feedback1.setFeedback("Excellent work");

        SubmissionFeedback feedback2 = new SubmissionFeedback();
        feedback2.setAssessmentId(1L);
        feedback2.setAssessmentType("Assignment");
        feedback2.setGrade("B+");
        feedback2.setFeedback("Good effort");

        return Arrays.asList(feedback1, feedback2);
    }

    // Updated helper methods to fix the mocking issues

    private JsonType createMockSkeletonJsonType(boolean withFiles) {
        JsonType jsonType = mock(JsonType.class);
        ActivityRelationshipWithFeedbacks relationships = createMockRelationships(withFiles);
        when(jsonType.getRelationships()).thenReturn(relationships);
        return jsonType;
    }

    private ActivityRelationshipWithFeedbacks createMockRelationships(boolean withFiles) {
        ActivityRelationshipWithFeedbacks relationships = mock(ActivityRelationshipWithFeedbacks.class);
        if (withFiles) {
            JsonResponse filesResponse = mock(JsonResponse.class);
            List<JsonType> files = createMockFiles();
            when(filesResponse.getData()).thenReturn(files);
            when(relationships.getFiles()).thenReturn(filesResponse);
        }
        return relationships;
    }

    private List<JsonType> createMockFiles() {
        JsonType file1 = mock(JsonType.class);
        when(file1.getId()).thenReturn(1L);
        ActivityFileAttribute attributes1 = mock(ActivityFileAttribute.class);
        when(file1.getAttributes()).thenReturn(attributes1);

        return Arrays.asList(file1);
    }

    private List<JsonType> createMockMultipleFiles() {
        JsonType file1 = mock(JsonType.class);
        when(file1.getId()).thenReturn(1L);
        ActivityFileAttribute attributes1 = mock(ActivityFileAttribute.class);
        when(file1.getAttributes()).thenReturn(attributes1);

        JsonType file2 = mock(JsonType.class);
        when(file2.getId()).thenReturn(2L);
        ActivityFileAttribute attributes2 = mock(ActivityFileAttribute.class);
        when(file2.getAttributes()).thenReturn(attributes2);

        return Arrays.asList(file1, file2);
    }
}