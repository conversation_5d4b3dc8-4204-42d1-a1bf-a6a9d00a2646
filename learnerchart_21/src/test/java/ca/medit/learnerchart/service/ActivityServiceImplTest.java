package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.AssessmentMode;
import ca.medit.learnerchart.dto.*;
import ca.medit.learnerchart.entity.OscePdf;
import ca.medit.learnerchart.entity.Score;
import ca.medit.learnerchart.entity.User;
import ca.medit.learnerchart.repository.OscePdfRepository;
import ca.medit.learnerchart.repository.ScoreRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.joda.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigInteger;
import java.sql.Blob;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ActivityServiceImpl Tests")
class ActivityServiceImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private ScoreRepository scoreRepository;

    @Mock
    private OscePdfRepository oscePdfRepository;

    @Mock
    private Query query;

    @InjectMocks
    private ActivityServiceImpl activityService;

    private LearnerChartFilter testFilter;
    private List<Object[]> mockQueryResults;

    @BeforeEach
    void setUp() {
        testFilter = new LearnerChartFilter(2024, 1, "testuser", AssessmentMode.WRITTEN);
        mockQueryResults = createMockQueryResults();
    }

    // Test getTimelineActivities method
    @Test
    @DisplayName("Should return timeline activities successfully")
    void getTimelineActivities_Success() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockQueryResults);

        // When
        List<TimelineActivity> result = activityService.getTimelineActivities(testFilter);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(entityManager).createNativeQuery(anyString());
        verify(query, times(4)).setParameter(anyString(), any());
    }

    @Test
    @DisplayName("Should throw exception when filter is null")
    void getTimelineActivities_NullFilter_ThrowsException() {
        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getTimelineActivities(null));
        assertEquals("Client request was not complete. Filter is required", exception.getMessage());
    }

    @Test
    @DisplayName("Should return empty list when no scores found")
    void getTimelineActivities_NoScores_ReturnsEmptyList() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(Collections.emptyList());

        // When
        List<TimelineActivity> result = activityService.getTimelineActivities(testFilter);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle database exception")
    void getTimelineActivities_DatabaseException_ThrowsMedITException() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database error"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getTimelineActivities(testFilter));
        assertTrue(exception.getMessage().contains("Failed to get scores"));
    }

    // Test getActivityAverageMap method
    @Test
    @DisplayName("Should return activity average map for WRITTEN mode")
    void getActivityAverageMap_WrittenMode_Success() {
        // Given
        LearnerChartFilter writtenFilter = new LearnerChartFilter(2024, 1, "testuser", AssessmentMode.WRITTEN);
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(createMockAverageQueryResults());

        // When
        CohortNumericScoreAverageMap result = activityService.getActivityAverageMap(writtenFilter);

        // Then
        assertNotNull(result);
        verify(entityManager).createNativeQuery(anyString());
    }

    @Test
    @DisplayName("Should return null for non-WRITTEN mode")
    void getActivityAverageMap_NonWrittenMode_ReturnsNull() {
        // Given
        LearnerChartFilter performanceFilter = new LearnerChartFilter(2024, 1, "testuser", AssessmentMode.PERFORMANCE_BASED);

        // When
        CohortNumericScoreAverageMap result = activityService.getActivityAverageMap(performanceFilter);

        // Then
        assertNull(result);
        verifyNoInteractions(entityManager);
    }

    @Test
    @DisplayName("Should handle exception in getActivityAverageMap")
    void getActivityAverageMap_Exception_ThrowsMedITException() {
        // Given
        LearnerChartFilter writtenFilter = new LearnerChartFilter(2024, 1, "testuser", AssessmentMode.WRITTEN);
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenThrow(new RuntimeException("Database error"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getActivityAverageMap(writtenFilter));
        assertEquals("Error getting average scores for Written assessments", exception.getMessage());
    }

    // Test getScoresForTimeline method
    @Test
    @DisplayName("Should get scores for timeline successfully")
    void getScoresForTimeline_Success() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockQueryResults);

        // When
        List<TimelineActivity> result = activityService.getScoresForTimeline(testFilter);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(query).setParameter("utorid", "testuser");
        verify(query).setParameter("academicYear", 2024);
        verify(query).setParameter("programYear", 1);
        verify(query).setParameter("assessmentMode", "WRITTEN");
    }

    @Test
    @DisplayName("Should throw exception when filter is null in getScoresForTimeline")
    void getScoresForTimeline_NullFilter_ThrowsException() {
        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getScoresForTimeline(null));
        assertEquals("Filter must not be null", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle runtime exception in getScoresForTimeline")
    void getScoresForTimeline_RuntimeException_ThrowsMedITException() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenThrow(new RuntimeException("Query failed"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getScoresForTimeline(testFilter));
        assertTrue(exception.getMessage().contains("Failed to get scores for Program Year 1"));
    }

    // Test getAsessmentScoresForLearner method
    @Test
    @DisplayName("Should get assessment scores for learner successfully")
    void getAssessmentScoresForLearner_Success() {
        // Given
        Long assessmentId = 123L;
        String username = "testuser";
        List<Object[]> mockScoreResults = createMockScoreResults();

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockScoreResults);

        // When
        List<Score> result = activityService.getAsessmentScoresForLearner(assessmentId, username);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        Score score = result.get(0);
        assertEquals(1L, score.getScoreId());
        assertEquals("85.5", score.getRawValue());
        assertEquals("TOTAL_SCORE_XID", score.getCplanXid());
        assertEquals("ref123", score.getReference());

        verify(query).setParameter("username", username);
        verify(query).setParameter("assessmentId", assessmentId);
    }

    @Test
    @DisplayName("Should handle runtime exception in getAssessmentScoresForLearner")
    void getAssessmentScoresForLearner_RuntimeException_ThrowsMedITException() {
        // Given
        Long assessmentId = 123L;
        String username = "testuser";
        when(entityManager.createNativeQuery(anyString())).thenThrow(new RuntimeException("Query failed"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getAsessmentScoresForLearner(assessmentId, username));
        assertTrue(exception.getMessage().contains("Failed to get the scores for assessment id = 123"));
    }

    // Test getCohortAverageNumericScoreMap method
    @Test
    @DisplayName("Should get cohort average numeric score map successfully")
    void getCohortAverageNumericScoreMap_Success() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(createMockAverageQueryResults());

        // When
        CohortNumericScoreAverageMap result = activityService.getCohortAverageNumericScoreMap(testFilter);

        // Then
        assertNotNull(result);
        verify(query).setParameter("totalScoreXid", "TOTAL_SCORE_XID");
        verify(query).setParameter("absenteeXid", "ABSENTEE_XID");
        verify(query).setParameter("username", "testuser");
        verify(query).setParameter("academicYear", 2024);
        verify(query).setParameter("programYear", 1);
        verify(query).setParameter("assessmentMode", "WRITTEN");
    }

    @Test
    @DisplayName("Should handle Long assessmentId in getCohortAverageNumericScoreMap")
    void getCohortAverageNumericScoreMap_LongAssessmentId_Success() {
        // Given
        List<Object[]> mockResults = new ArrayList<>();
        mockResults.add(new Object[]{
                100.0, // pointsAvailable
                85.0,  // pointsEarned
                123L,  // assessmentId as Long
                "Test Assessment", // name
                new Timestamp(System.currentTimeMillis()) // date
        });

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockResults);

        // When
        CohortNumericScoreAverageMap result = activityService.getCohortAverageNumericScoreMap(testFilter);

        // Then
        assertNotNull(result);
        AverageNumericScore score = result.get(123L);
        assertNotNull(score);
        assertEquals(123L, score.getId());
    }

    @Test
    @DisplayName("Should handle IllegalArgumentException for unknown assessmentId type")
    void getCohortAverageNumericScoreMap_UnknownAssessmentIdType_ThrowsException() {
        // Given
        List<Object[]> mockResults = new ArrayList<>();
        mockResults.add(new Object[]{
                100.0, // pointsAvailable
                85.0,  // pointsEarned
                "123", // assessmentId as String (unexpected type)
                "Test Assessment", // name
                new Timestamp(System.currentTimeMillis()) // date
        });

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockResults);

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getCohortAverageNumericScoreMap(testFilter));
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
    }

    @Test
    @DisplayName("Should handle runtime exception in getCohortAverageNumericScoreMap")
    void getCohortAverageNumericScoreMap_RuntimeException_ThrowsMedITException() {
        // Given
        when(entityManager.createNativeQuery(anyString())).thenThrow(new RuntimeException("Query failed"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getCohortAverageNumericScoreMap(testFilter));
        assertEquals("Unable to get the cohort scores", exception.getMessage());
    }

    // Test convertToLocalDateTime method
    @Test
    @DisplayName("Should convert LocalDateTime object")
    void convertToLocalDateTime_LocalDateTime_Success() {
        // Given
        LocalDateTime expected = LocalDateTime.now();

        // When
        LocalDateTime result = ActivityServiceImpl.convertToLocalDateTime(expected);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("Should convert Timestamp object")
    void convertToLocalDateTime_Timestamp_Success() {
        // Given
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        // When
        LocalDateTime result = ActivityServiceImpl.convertToLocalDateTime(timestamp);

        // Then
        assertNotNull(result);
    }

    @Test
    @DisplayName("Should convert String object")
    void convertToLocalDateTime_String_Success() {
        // Given
        String dateString = "2024-01-15 10:30:00.000";

        // When
        LocalDateTime result = ActivityServiceImpl.convertToLocalDateTime(dateString);

        // Then
        assertNotNull(result);
    }

    @Test
    @DisplayName("Should return null for null input")
    void convertToLocalDateTime_Null_ReturnsNull() {
        // When
        LocalDateTime result = ActivityServiceImpl.convertToLocalDateTime(null);

        // Then
        assertNull(result);
    }

    // Test getOsceFile method
    @Test
    @DisplayName("Should get OSCE file successfully")
    void getOsceFile_Success() throws SQLException {
        // Given
        Long scoreId = 123L;
        Score mockScore = createMockScore();
        OscePdf mockOscePdf = createMockOscePdf();

        when(scoreRepository.getById(scoreId)).thenReturn(mockScore);
        when(oscePdfRepository.getById(mockScore.getReference())).thenReturn(mockOscePdf);

        // When
        LearnerFile result = activityService.getOsceFile(scoreId);

        // Then
        assertNotNull(result);
        assertEquals("test-reference", result.getFileName());
        assertNotNull(result.getInputStream());

        verify(scoreRepository).getById(scoreId);
        verify(oscePdfRepository).getById("test-reference");
    }

    @Test
    @DisplayName("Should throw exception when score not found")
    void getOsceFile_ScoreNotFound_ThrowsException() {
        // Given
        Long scoreId = 123L;
        when(scoreRepository.getById(scoreId)).thenReturn(null);

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getOsceFile(scoreId));
        assertEquals("Score not found for ID = 123", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception when PDF not found")
    void getOsceFile_PdfNotFound_ThrowsException() {
        // Given
        Long scoreId = 123L;
        Score mockScore = createMockScore();

        when(scoreRepository.getById(scoreId)).thenReturn(mockScore);
        when(oscePdfRepository.getById(mockScore.getReference())).thenReturn(null);

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getOsceFile(scoreId));
        assertEquals("Pdf not found for file name = test-reference", exception.getMessage());
    }

    @Test
    @DisplayName("Should handle SQL exception in getOsceFile")
    void getOsceFile_SQLException_ThrowsMedITException() {
        // Given
        Long scoreId = 123L;
        when(scoreRepository.getById(scoreId)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        MedITException exception = assertThrows(MedITException.class, () ->
                activityService.getOsceFile(scoreId));
        assertTrue(exception.getMessage().contains("Database error"));
    }

    // Helper methods for creating mock data
    private List<Object[]> createMockQueryResults() {
        List<Object[]> results = new ArrayList<>();
        Object[] row = new Object[22];
        row[0] = "85.5"; // RAW_VALUE
        row[1] = 100.0; // POINTS_AVAILABLE
        row[2] = 85.5; // POINTS_EARNED
        row[3] = 50; // NUMBER_OF_ITEMS
        row[4] = 123L; // ASSESSMENT_ID
        row[5] = 456L; // REMOTE_ID
        row[6] = "Test Assessment"; // NAME
        row[7] = "EXAM"; // ASSESSMENT_TYPE
        row[8] = new Timestamp(System.currentTimeMillis()); // DUE_DATE
        row[9] = "TOTAL_SCORE_XID"; // CPLAN_XID
        row[10] = 1L; // CPLAN_XID_ID
        row[11] = "Medical Expert"; // LABEL
        row[12] = "Parent Assessment"; // PARENT_NAME
        row[13] = "90.0"; // PARENT_RAW_VALUE
        row[14] = 789L; // SCORE_ID
        row[15] = "ref123"; // reference
        row[16] = "MED101"; // COURSE_CODE
        row[17] = "John"; // SUPERVISOR_FIRST_NAME
        row[18] = "Doe"; // SUPERVISOR_LAST_NAME
        row[19] = 2024; // ACADEMIC_YEAR
        row[20] = 1; // PROGRAM_YEAR
        row[21] = "MEDSIS"; // SOURCE
        results.add(row);
        return results;
    }

    private List<Object[]> createMockAverageQueryResults() {
        List<Object[]> results = new ArrayList<>();
        Object[] row = new Object[5];
        row[0] = 100.0; // POINTS_AVAILABLE
        row[1] = 85.0; // POINTS_EARNED
        row[2] = BigInteger.valueOf(123L); // ASSESSMENT_ID
        row[3] = "Test Assessment"; // NAME
        row[4] = new Timestamp(System.currentTimeMillis()); // DUE_DATE
        results.add(row);
        return results;
    }

    private List<Object[]> createMockScoreResults() {
        List<Object[]> results = new ArrayList<>();
        Object[] row = new Object[4];
        row[0] = BigInteger.valueOf(1L); // score_id
        row[1] = "85.5"; // raw_value
        row[2] = "TOTAL_SCORE_XID"; // cplan_xid
        row[3] = "ref123"; // reference
        results.add(row);
        return results;
    }

    private Score createMockScore() {
        Score score = new Score();
        score.setScoreId(123L);
        score.setReference("test-reference");
        score.setRawValue("85.5");

        User authUser = new User();
        authUser.setUtorid("testuser");
        score.setAuthUser(authUser);

        return score;
    }

    private OscePdf createMockOscePdf() throws SQLException {
        OscePdf oscePdf = new OscePdf();
        oscePdf.setOsceName("test-reference");

        Blob mockBlob = mock(Blob.class);
        when(mockBlob.getBinaryStream()).thenReturn(new java.io.ByteArrayInputStream("test data".getBytes()));
        oscePdf.setOsceData(mockBlob);

        return oscePdf;
    }
}