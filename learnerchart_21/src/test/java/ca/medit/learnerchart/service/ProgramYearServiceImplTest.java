package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.domain.JsonType;
import ca.medit.learnerchart.domain.ProgramYearAttribute;
import ca.medit.learnerchart.dto.YearDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProgramYearServiceImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    @InjectMocks
    private ProgramYearServiceImpl programYearService;

    @BeforeEach
    void setUp() {
        // Inject the mocked EntityManager using reflection since @PersistenceContext is used
        ReflectionTestUtils.setField(programYearService, "entityManager", entityManager);
    }

    @Test
    void testGetProgramYears_ShouldReturnAllFourYears() {
        // When
        JsonResponse result = programYearService.getProgramYears();

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof List);

        @SuppressWarnings("unchecked")
        List<JsonType> years = (List<JsonType>) result.getData();
        assertEquals(4, years.size());

        // Verify each year
        for (int i = 0; i < 4; i++) {
            JsonType yearType = years.get(i);
            assertEquals(i + 1, yearType.getId());
            assertEquals(JsonType.TYPE_PROGRAM_YEAR, yearType.getType());
            assertNotNull(yearType.getAttributes());
            assertTrue(yearType.getAttributes() instanceof ProgramYearAttribute);
            ProgramYearAttribute attr = (ProgramYearAttribute) yearType.getAttributes();
            assertEquals("Year " + (i + 1), attr.getLabel());
        }
    }

    @Test
    void testGetProgramYear_ValidYearId_ShouldReturnCorrectYear() {
        // Given
        Integer yearId = 2;

        // When
        JsonResponse result = programYearService.getProgramYear(yearId);

        // Then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData() instanceof JsonType);

        JsonType yearType = (JsonType) result.getData();
        assertEquals(2, yearType.getId());
        assertEquals(JsonType.TYPE_PROGRAM_YEAR, yearType.getType());
        assertNotNull(yearType.getAttributes());

        ProgramYearAttribute attr = (ProgramYearAttribute) yearType.getAttributes();
        assertEquals("Year 2", attr.getLabel());
    }

    @Test
    void testGetProgramYear_InvalidYearIdTooLow_ShouldThrowException() {
        // Given
        Integer yearId = 0;

        // When & Then
        MedITException exception = assertThrows(MedITException.class,
                () -> programYearService.getProgramYear(yearId));

        assertEquals("Invalid program year: 0", exception.getMessage());
    }

    @Test
    void testGetProgramYear_InvalidYearIdTooHigh_ShouldThrowException() {
        // Given
        Integer yearId = 5;

        // When & Then
        MedITException exception = assertThrows(MedITException.class,
                () -> programYearService.getProgramYear(yearId));

        assertEquals("Invalid program year: 5", exception.getMessage());
    }

    @Test
    void testGetLearnerYearList_ValidUtorId_ShouldReturnTransformedYears() {
        // Given
        String utorId = "testuser";
        List<Object[]> mockRows = Arrays.asList(
                new Object[]{2023, 1},
                new Object[]{2023, 2}
        );

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockRows);

        // When
        JsonResponse result = programYearService.getLearnerYearList(utorId);

        // Then
        assertNotNull(result);
        verify(entityManager).createNativeQuery(anyString());
        verify(query).setParameter("utorId", utorId);
        verify(query).getResultList();
    }

    @Test
    void testGetLearnerYearList_DatabaseException_ShouldThrowMedITException() {
        // Given
        String utorId = "testuser";
        RuntimeException dbException = new RuntimeException("Database error");

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenThrow(dbException);

        // When & Then
        MedITException exception = assertThrows(MedITException.class,
                () -> programYearService.getLearnerYearList(utorId));

        assertNotNull(exception.getCause());
    }

    @Test
    void testGetYearsForUser_ValidUtorId_ShouldReturnYearDTOList() {
        // Given
        String utorId = "testuser";
        List<Object[]> mockRows = Arrays.asList(
                new Object[]{2023, 1},
                new Object[]{2023, 2},
                new Object[]{2024, 1}
        );

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(mockRows);

        // When
        List<YearDTO> result = programYearService.getYearsForUser(utorId);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify first year
        YearDTO firstYear = result.get(0);
        assertEquals(2023, firstYear.getAcademicYear());
        assertEquals(1, firstYear.getProgramYear());
        assertEquals(20231, firstYear.getYearId());

        // Verify second year
        YearDTO secondYear = result.get(1);
        assertEquals(2023, secondYear.getAcademicYear());
        assertEquals(2, secondYear.getProgramYear());
        assertEquals(20232, secondYear.getYearId());

        // Verify third year
        YearDTO thirdYear = result.get(2);
        assertEquals(2024, thirdYear.getAcademicYear());
        assertEquals(1, thirdYear.getProgramYear());
        assertEquals(20241, thirdYear.getYearId());

        // Verify query parameters
        verify(entityManager).createNativeQuery(anyString());
        verify(query).setParameter("utorId", utorId);
        verify(query).getResultList();
    }

    @Test
    void testGetYearsForUser_EmptyResult_ShouldReturnEmptyList() {
        // Given
        String utorId = "nonexistentuser";
        List<Object[]> emptyRows = new ArrayList<>();

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(emptyRows);

        // When
        List<YearDTO> result = programYearService.getYearsForUser(utorId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(entityManager).createNativeQuery(anyString());
        verify(query).setParameter("utorId", utorId);
        verify(query).getResultList();
    }

    @Test
    void testGetYearsForUser_DatabaseException_ShouldThrowDataAccessException() {
        // Given
        String utorId = "testuser";
        RuntimeException dbException = new RuntimeException("Connection failed");

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenThrow(dbException);

        // When & Then
        DataAccessException exception = assertThrows(DataAccessException.class,
                () -> programYearService.getYearsForUser(utorId));

        assertEquals("Error when getting years for user " + utorId, exception.getMessage());
        assertEquals(dbException, exception.getCause());
    }

    @Test
    void testGetYearsForUser_NullUtorId_ShouldStillExecuteQuery() {
        // Given
        String utorId = null;
        List<Object[]> emptyRows = new ArrayList<>();

        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getResultList()).thenReturn(emptyRows);

        // When
        List<YearDTO> result = programYearService.getYearsForUser(utorId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(query).setParameter("utorId", utorId);
    }

    @Test
    void testGetYearsForUser_VerifyCorrectSQLQuery() {
        // Given
        String utorId = "testuser";
        String expectedSQL = "SELECT DISTINCT a.ACADEMIC_YEAR, a.PROGRAM_YEAR " +
                "FROM assessment a, authuser_score aus, score s, users u " +
                "WHERE s.SCORE_ID = aus.SCORE_ID " +
                "AND u.USERS_ID = aus.AUTHUSER_ID " +
                "AND a.ASSESSMENT_ID = s.ASSESSMENT_ID " +
                "AND a.ASSESSMENT_TYPE <> 'PROGRESS_REVIEW' " +
                "AND u.UTORID = :utorId " +
                "ORDER BY a.ACADEMIC_YEAR ASC, a.PROGRAM_YEAR ASC";

        when(entityManager.createNativeQuery(expectedSQL)).thenReturn(query);
        when(query.getResultList()).thenReturn(new ArrayList<>());

        // When
        programYearService.getYearsForUser(utorId);

        // Then
        verify(entityManager).createNativeQuery(expectedSQL);
    }
    @Test
    void testCreateYearJsonType_BoundaryValues() {
        // Test year 1 (minimum valid)
        JsonResponse result1 = programYearService.getProgramYear(1);
        JsonType yearType1 = (JsonType) result1.getData();
        assertEquals(1, yearType1.getId());

        // Test year 4 (maximum valid)
        JsonResponse result4 = programYearService.getProgramYear(4);
        JsonType yearType4 = (JsonType) result4.getData();
        assertEquals(4, yearType4.getId());
    }
}