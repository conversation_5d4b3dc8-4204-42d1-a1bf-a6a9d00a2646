package ca.medit.learnerchart.service;

import ca.medit.learnerchart.domain.JsonResponse;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.repository.CanmedsMapRepository;
import ca.medit.learnerchart.service.converter.TransformerCanMedsRoleFilter;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CanMedsRoleServiceImplTest {

    @Mock
    private CanmedsMapRepository canmedsMapRepository;

    @Mock
    private EntityManager entityManager;

    @Mock
    private TypedQuery<Object[]> typedQuery;

    @InjectMocks
    private CanMedsRoleServiceImpl canMedsRoleService;

    private List<Object[]> mockQueryResults;
    private List<CanMedsMapItem> expectedItems;

    @BeforeEach
    void setUp() {
        // Setup mock query results
        mockQueryResults = createMockQueryResults();
        expectedItems = createExpectedItems();
    }

    @Test
    void testGetCanMedsRolesItems_Success() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockQueryResults);

        // Act
        List<CanMedsMapItem> result = canMedsRoleService.getCanMedsRolesItems();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify first item
        CanMedsMapItem firstItem = result.get(0);
        assertEquals(Long.valueOf(1L), firstItem.getParentCplanXid());
        assertEquals("CANMED_ROLE", firstItem.getParentFrameworkDef());
        assertEquals("Medical Expert", firstItem.getParentLabel());
        assertEquals("ME", firstItem.getParentValue());
        assertEquals(Long.valueOf(2L), firstItem.getChildCplanXid());
        assertEquals("PROGRAM_OBJECTIVES", firstItem.getChildFrameworkDef());
        assertEquals("Competency 1", firstItem.getChildLabel());
        assertEquals("C1", firstItem.getChildValue());
        assertEquals(2, firstItem.getLevel());

        // Verify second item
        CanMedsMapItem secondItem = result.get(1);
        assertEquals(Long.valueOf(3L), secondItem.getParentCplanXid());
        assertEquals("CANMED_ROLE", secondItem.getParentFrameworkDef());
        assertEquals("Communicator", secondItem.getParentLabel());
        assertEquals("COM", secondItem.getParentValue());
        assertEquals(Long.valueOf(4L), secondItem.getChildCplanXid());
        assertEquals("PROGRAM_OBJECTIVES", secondItem.getChildFrameworkDef());
        assertEquals("Competency 2", secondItem.getChildLabel());
        assertEquals("C2", secondItem.getChildValue());
        assertEquals(2, secondItem.getLevel());

        // Verify query was called with correct parameters
        String expectedQuery = "SELECT x.cplanXidId as parent_id, x.frameworkDef as parent_def, x.label as parent_label, x.value as parent_value, "
                + "y.cplanXidId as child_id, y.frameworkDef as child_def, y.label as child_label, y.value as child_value "
                + "FROM CanmedsMap m JOIN m.cplanXidParent x JOIN m.cplanXidChild y "
                + "WHERE m.cplanXidParent = m.cplanXidTop "
                + "AND x.cplanXidId <> y.cplanXidId";

        verify(entityManager).createQuery(expectedQuery, Object[].class);
        verify(typedQuery).getResultList();
    }

    @Test
    void testGetCanMedsRolesItems_EmptyResults() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(new ArrayList<>());

        // Act
        List<CanMedsMapItem> result = canMedsRoleService.getCanMedsRolesItems();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(entityManager).createQuery(anyString(), eq(Object[].class));
        verify(typedQuery).getResultList();
    }

    @Test
    void testGetCanMedsRolesItems_NullResults() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(null);

        // Act
        List<CanMedsMapItem> result = canMedsRoleService.getCanMedsRolesItems();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(entityManager).createQuery(anyString(), eq(Object[].class));
        verify(typedQuery).getResultList();
    }

    @Test
    void testGetCanMedsRolesItems_RuntimeException() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> canMedsRoleService.getCanMedsRolesItems());

        assertEquals("Unable to get Key Competencies", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("Database connection failed", exception.getCause().getMessage());

        verify(entityManager).createQuery(anyString(), eq(Object[].class));
    }

    @Test
    void testGetCanMedsRoles_Success() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockQueryResults);

        Object mockTransformedData = "Transformed data";

        try (MockedStatic<TransformerCanMedsRoleFilter> mockedTransformer = mockStatic(TransformerCanMedsRoleFilter.class)) {
            mockedTransformer.when(() -> TransformerCanMedsRoleFilter.toCanMedRole(any(List.class)))
                    .thenReturn(mockTransformedData);

            // Act
            JsonResponse result = canMedsRoleService.getCanMedsRoles();

            // Assert
            assertNotNull(result);
            assertEquals(mockTransformedData, result.getData());

            // Verify transformer was called with correct items
            mockedTransformer.verify(() -> TransformerCanMedsRoleFilter.toCanMedRole(argThat(items ->
                    items != null && items.size() == 2
            )));
        }

        verify(entityManager).createQuery(anyString(), eq(Object[].class));
        verify(typedQuery).getResultList();
    }

    @Test
    void testGetCanMedsRoles_EmptyResults() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(new ArrayList<>());

        Object mockTransformedData = new ArrayList<>();

        try (MockedStatic<TransformerCanMedsRoleFilter> mockedTransformer = mockStatic(TransformerCanMedsRoleFilter.class)) {
            mockedTransformer.when(() -> TransformerCanMedsRoleFilter.toCanMedRole(any(List.class)))
                    .thenReturn(mockTransformedData);

            // Act
            JsonResponse result = canMedsRoleService.getCanMedsRoles();

            // Assert
            assertNotNull(result);
            assertEquals(mockTransformedData, result.getData());

            // Verify transformer was called with empty list
            mockedTransformer.verify(() -> TransformerCanMedsRoleFilter.toCanMedRole(argThat(List::isEmpty)));
        }
    }

    @Test
    void testGetCanMedsRoles_RuntimeException() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class)))
                .thenThrow(new RuntimeException("Query execution failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> canMedsRoleService.getCanMedsRoles());

        assertEquals("Unable to get Key Competencies", exception.getMessage());
        assertNotNull(exception.getCause());
        assertEquals("Query execution failed", exception.getCause().getMessage());

        verify(entityManager).createQuery(anyString(), eq(Object[].class));
    }

    @Test
    void testGetCanMedsRoles_TransformerException() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockQueryResults);

        try (MockedStatic<TransformerCanMedsRoleFilter> mockedTransformer = mockStatic(TransformerCanMedsRoleFilter.class)) {
            mockedTransformer.when(() -> TransformerCanMedsRoleFilter.toCanMedRole(any(List.class)))
                    .thenThrow(new RuntimeException("Transformation failed"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class,
                    () -> canMedsRoleService.getCanMedsRoles());

            assertEquals("Unable to get Key Competencies", exception.getMessage());
            assertNotNull(exception.getCause());
            assertEquals("Transformation failed", exception.getCause().getMessage());
        }
    }

    @Test
    void testGetCanMedsRoles_QueryReturnsCorrectStructure() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockQueryResults);

        try (MockedStatic<TransformerCanMedsRoleFilter> mockedTransformer = mockStatic(TransformerCanMedsRoleFilter.class)) {
            mockedTransformer.when(() -> TransformerCanMedsRoleFilter.toCanMedRole(any(List.class)))
                    .thenReturn("mock data");

            // Act
            canMedsRoleService.getCanMedsRoles();

            // Assert - Verify the correct query structure is used
            String expectedQuery = "SELECT x.cplanXidId as parent_id, x.frameworkDef as parent_def, x.label as parent_label, x.value as parent_value, "
                    + "y.cplanXidId as child_id, y.frameworkDef as child_def, y.label as child_label, y.value as child_value "
                    + "FROM CanmedsMap m JOIN m.cplanXidParent x JOIN m.cplanXidChild y "
                    + "WHERE m.cplanXidParent = m.cplanXidTop "
                    + "AND x.cplanXidId <> y.cplanXidId";

            verify(entityManager).createQuery(expectedQuery, Object[].class);
        }
    }

    @Test
    void testGetCanMedsRoles_ResultItemsHaveCorrectLevel() {
        // Arrange
        when(entityManager.createQuery(anyString(), eq(Object[].class))).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockQueryResults);

        try (MockedStatic<TransformerCanMedsRoleFilter> mockedTransformer = mockStatic(TransformerCanMedsRoleFilter.class)) {
            mockedTransformer.when(() -> TransformerCanMedsRoleFilter.toCanMedRole(argThat(items -> {
                // Verify all items have level set to 2
                return items.stream().allMatch(item -> ((CanMedsMapItem) item).getLevel() == 2);
            }))).thenReturn("mock data");

            // Act
            canMedsRoleService.getCanMedsRoles();

            // Assert - Verification is done in the argument matcher above
            mockedTransformer.verify(() -> TransformerCanMedsRoleFilter.toCanMedRole(any(List.class)));
        }
    }

    // Helper methods to create test data
    private List<Object[]> createMockQueryResults() {
        List<Object[]> results = new ArrayList<>();

        // First row
        Object[] row1 = new Object[]{
                1L,                    // parent_id
                "CANMED_ROLE",         // parent_def
                "Medical Expert",      // parent_label
                "ME",                  // parent_value
                2L,                    // child_id
                "PROGRAM_OBJECTIVES",  // child_def
                "Competency 1",        // child_label
                "C1"                   // child_value
        };

        // Second row
        Object[] row2 = new Object[]{
                3L,                    // parent_id
                "CANMED_ROLE",         // parent_def
                "Communicator",        // parent_label
                "COM",                 // parent_value
                4L,                    // child_id
                "PROGRAM_OBJECTIVES",  // child_def
                "Competency 2",        // child_label
                "C2"                   // child_value
        };

        results.add(row1);
        results.add(row2);

        return results;
    }

    private List<CanMedsMapItem> createExpectedItems() {
        List<CanMedsMapItem> items = new ArrayList<>();

        CanMedsMapItem item1 = new CanMedsMapItem();
        item1.setParentCplanXid(1L);
        item1.setParentFrameworkDef("CANMED_ROLE");
        item1.setParentLabel("Medical Expert");
        item1.setParentValue("ME");
        item1.setChildCplanXid(2L);
        item1.setChildFrameworkDef("PROGRAM_OBJECTIVES");
        item1.setChildLabel("Competency 1");
        item1.setChildValue("C1");
        item1.setLevel(2);

        CanMedsMapItem item2 = new CanMedsMapItem();
        item2.setParentCplanXid(3L);
        item2.setParentFrameworkDef("CANMED_ROLE");
        item2.setParentLabel("Communicator");
        item2.setParentValue("COM");
        item2.setChildCplanXid(4L);
        item2.setChildFrameworkDef("PROGRAM_OBJECTIVES");
        item2.setChildLabel("Competency 2");
        item2.setChildValue("C2");
        item2.setLevel(2);

        items.add(item1);
        items.add(item2);

        return items;
    }
}