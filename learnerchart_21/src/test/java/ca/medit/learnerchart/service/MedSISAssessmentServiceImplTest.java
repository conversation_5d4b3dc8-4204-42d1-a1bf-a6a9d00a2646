package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.common.exception.MedSisEvaluationImportException;
import ca.medit.learnerchart.dto.AssessmentHelper;
import ca.medit.learnerchart.dto.EvaluationReport;
import ca.medit.learnerchart.dto.StudentHelper;
import ca.medit.learnerchart.dto.StudentReport;
import ca.medit.learnerchart.entity.*;
import ca.medit.learnerchart.repository.CohortRepository;
import ca.medit.learnerchart.repository.GroupRepository;
import ca.medit.learnerchart.repository.UserImageRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;
import java.io.ByteArrayOutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MedSISAssessmentServiceImplTest {

    @Mock
    private TransactionService transactionService;

    @Mock
    private HouseKeepingService houseKeepingService;

    @Mock
    private GroupRepository groupRepository;

    @Mock
    private UserService userService;

    @Mock
    private CohortRepository cohortRepository;

    @Mock
    private ScoreService scoreService;

    @Mock
    private UserImageRepository userImageRepository;

    @InjectMocks
    private MedSISAssessmentServiceImpl medSISAssessmentService;

    @Mock
    private ObjectMapper objectMapper;

    private Group learnerGroup;
    private User testUser;
    private StudentReport testStudentReport;
    private EvaluationReport testEvaluationReport;

    @BeforeEach
    void setUp() {
        // Setup test data
        learnerGroup = new Group();
        learnerGroup.setName(Group.LEARNER);

        testUser = new User();
        testUser.setUsername("testuser");
        testUser.setCohortName("TestCohort");

        // Setup StudentReport
        testStudentReport = new StudentReport();
        testStudentReport.reportData = new ArrayList<>();
        StudentReport.ReportData reportData = new StudentReport.ReportData();
        testStudentReport.reportData.add(reportData);

        // Setup EvaluationReport
        testEvaluationReport = new EvaluationReport();
        testEvaluationReport.reportData = new ArrayList<>();
        EvaluationReport.ReportData evalReportData = new EvaluationReport.ReportData();
        evalReportData.formName = "TestForm";
        evalReportData.evaluations = new ArrayList<>();
        evalReportData.canceledEvaluations = new ArrayList<>();
        testEvaluationReport.reportData.add(evalReportData);
    }

    @Test
    void saveStudentUsers_Success() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        when(groupRepository.findByName(Group.LEARNER)).thenReturn(Optional.of(learnerGroup));

        try (MockedStatic<StudentHelper> studentHelperMock = mockStatic(StudentHelper.class)) {
            studentHelperMock.when(() -> StudentHelper.fromStudentReportData(any()))
                    .thenReturn(Arrays.asList(testUser));

            when(userService.checkUserAlreadyExists("testuser")).thenReturn(false);
            when(userService.saveStudent(any(User.class))).thenReturn(testUser);

            // Use spy to mock the private methods
            MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
            doReturn(testStudentReport).when(spy).convertToStudentReport(inputStream);
            doReturn(Arrays.asList(testUser)).when(spy).convertToStudentUsers(testStudentReport);

            // Act
            spy.saveStudentUsers(inputStream);

            // Assert
            verify(userService).saveStudent(any(User.class));
            verify(cohortRepository).deleteAll();
            verify(cohortRepository).save(any(Cohort.class));
            verify(scoreService).claimOrphanedScores();
        }
    }

    @Test
    void saveStudentUsers_LearnerGroupNotFound() {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        when(groupRepository.findByName(Group.LEARNER)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> {
            medSISAssessmentService.saveStudentUsers(inputStream);
        });
    }

    @Test
    void saveStudentUsers_UserAlreadyExists() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        when(groupRepository.findByName(Group.LEARNER)).thenReturn(Optional.of(learnerGroup));
        when(userService.checkUserAlreadyExists("testuser")).thenReturn(true);

        try (MockedStatic<StudentHelper> studentHelperMock = mockStatic(StudentHelper.class)) {
            studentHelperMock.when(() -> StudentHelper.fromStudentReportData(any()))
                    .thenReturn(Arrays.asList(testUser));

            MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
            doReturn(testStudentReport).when(spy).convertToStudentReport(inputStream);
            doReturn(Arrays.asList(testUser)).when(spy).convertToStudentUsers(testStudentReport);

            // Act
            spy.saveStudentUsers(inputStream);

            // Assert
            verify(userService, never()).saveStudent(any(User.class));
            verify(scoreService).claimOrphanedScores();
        }
    }

    @Test
    void convertToStudentReport_IOException() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("invalid json".getBytes());

        // Act & Assert
        assertThrows(IOException.class, () -> {
            medSISAssessmentService.convertToStudentReport(inputStream);
        });
    }

    @Test
    void convertToStudentUsers_Success() {
        // Arrange
        try (MockedStatic<StudentHelper> studentHelperMock = mockStatic(StudentHelper.class)) {
            studentHelperMock.when(() -> StudentHelper.fromStudentReportData(any()))
                    .thenReturn(Arrays.asList(testUser));

            // Act
            List<User> result = medSISAssessmentService.convertToStudentUsers(testStudentReport);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("testuser", result.get(0).getUsername());
        }
    }

    @Test
    void convertToStudentUsers_EmptyReport() {
        // Arrange
        StudentReport emptyReport = new StudentReport();
        emptyReport.reportData = new ArrayList<>();

        // Act
        List<User> result = medSISAssessmentService.convertToStudentUsers(emptyReport);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void saveAssessments_Success() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        Assessment testAssessment = new Assessment();
        testAssessment.setRemoteId(1L);

        MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
        doReturn(testEvaluationReport).when(spy).convertToEvaluationReport(inputStream);
        doReturn(Arrays.asList(testAssessment)).when(spy).convertToAssessments(testEvaluationReport);

        when(transactionService.saveAssessment(any(Assessment.class))).thenReturn(testAssessment);

        // Act
        spy.saveAssessments(inputStream);

        // Assert
        verify(transactionService).saveAssessment(testAssessment);
        verify(houseKeepingService).keep(Arrays.asList(testAssessment));
    }

    @Test
    void saveAssessments_WithException() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        Assessment testAssessment = new Assessment();
        testAssessment.setRemoteId(1L);

        MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
        doReturn(testEvaluationReport).when(spy).convertToEvaluationReport(inputStream);
        doReturn(Arrays.asList(testAssessment)).when(spy).convertToAssessments(testEvaluationReport);

        when(transactionService.saveAssessment(any(Assessment.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        spy.saveAssessments(inputStream);

        // Assert
        verify(houseKeepingService).keep(Collections.emptyList());
    }

    @Test
    void saveStudentImages_Success() throws IOException {
        // Arrange
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        zos.putNextEntry(new ZipEntry("test.jpg"));
        zos.write("test image data".getBytes());
        zos.closeEntry();
        zos.close();

        InputStream zipInputStream = new ByteArrayInputStream(baos.toByteArray());
        when(userImageRepository.findByImageName("test.jpg")).thenReturn(Optional.empty());

        // Act
        medSISAssessmentService.saveStudentImages(zipInputStream);

        // Assert
        verify(userImageRepository).save(any(UserImage.class));
    }

    @Test
    void saveStudentImages_ImageAlreadyExists() throws IOException {
        // Arrange
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        zos.putNextEntry(new ZipEntry("existing.jpg"));
        zos.write("test image data".getBytes());
        zos.closeEntry();
        zos.close();

        InputStream zipInputStream = new ByteArrayInputStream(baos.toByteArray());
        UserImage existingImage = new UserImage();
        when(userImageRepository.findByImageName("existing.jpg")).thenReturn(Optional.of(existingImage));

        // Act
        medSISAssessmentService.saveStudentImages(zipInputStream);

        // Assert
        verify(userImageRepository, never()).save(any(UserImage.class));
    }

    @Test
    void convertToEvaluationReport_Success() throws IOException {
        // Arrange
        String validJson = """
            {
                "reportData": []
            }
            """;
        InputStream inputStream = new ByteArrayInputStream(validJson.getBytes());

        // Act
        EvaluationReport result = medSISAssessmentService.convertToEvaluationReport(inputStream);

        // Assert
        assertNotNull(result);
        assertNotNull(result.reportData);
    }

    @Test
    void convertToAssessments_Success() {
        // Arrange
        EvaluationReport.Evaluation evaluation = new EvaluationReport.Evaluation();
        evaluation.evalId = "eval123";
        testEvaluationReport.reportData.get(0).evaluations.add(evaluation);

        EvaluationReport.EvaluationBase canceledEvaluation = new EvaluationReport.EvaluationBase();
        canceledEvaluation.evalId = "canceled123";
        testEvaluationReport.reportData.get(0).canceledEvaluations.add(canceledEvaluation);

        Assessment testAssessment = new Assessment();
        Assessment canceledAssessment = new Assessment();

        try (MockedStatic<AssessmentHelper> assessmentHelperMock = mockStatic(AssessmentHelper.class)) {
            assessmentHelperMock.when(() -> AssessmentHelper.fromEvaluation(eq("TestForm"), any()))
                    .thenReturn(testAssessment);
            assessmentHelperMock.when(() -> AssessmentHelper.fromEvaluationBase(eq("TestForm"), any()))
                    .thenReturn(canceledAssessment);

            // Act
            List<Assessment> result = medSISAssessmentService.convertToAssessments(testEvaluationReport);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
        }
    }

    @Test
    void convertToAssessments_WithImportException() {
        // Arrange
        EvaluationReport.Evaluation evaluation = new EvaluationReport.Evaluation();
        evaluation.evalId = "eval123";
        testEvaluationReport.reportData.get(0).evaluations.add(evaluation);

        try (MockedStatic<AssessmentHelper> assessmentHelperMock = mockStatic(AssessmentHelper.class)) {
            assessmentHelperMock.when(() -> AssessmentHelper.fromEvaluation(eq("TestForm"), any()))
                    .thenThrow(new MedSisEvaluationImportException("Import error", evaluation));

            // Act
            List<Assessment> result = medSISAssessmentService.convertToAssessments(testEvaluationReport);

            // Assert
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void convertToAssessments_EmptyReport() {
        // Arrange
        EvaluationReport emptyReport = new EvaluationReport();
        emptyReport.reportData = new ArrayList<>();

        // Act
        List<Assessment> result = medSISAssessmentService.convertToAssessments(emptyReport);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void convertToAssessments_NullEvaluations() {
        // Arrange
        EvaluationReport.ReportData reportData = new EvaluationReport.ReportData();
        reportData.formName = "TestForm";
        reportData.evaluations = null;
        reportData.canceledEvaluations = null;
        testEvaluationReport.reportData.clear();
        testEvaluationReport.reportData.add(reportData);

        // Act
        List<Assessment> result = medSISAssessmentService.convertToAssessments(testEvaluationReport);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void nullSafe_WithNullCollection() {
        // Act
        Collection<String> result = MedSISAssessmentServiceImpl.nullSafe(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void nullSafe_WithValidCollection() {
        // Arrange
        List<String> validList = Arrays.asList("item1", "item2");

        // Act
        Collection<String> result = MedSISAssessmentServiceImpl.nullSafe(validList);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("item1"));
        assertTrue(result.contains("item2"));
    }

    @Test
    void saveStudentUsers_EmptyCohortNames() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        when(groupRepository.findByName(Group.LEARNER)).thenReturn(Optional.of(learnerGroup));

        try (MockedStatic<StudentHelper> studentHelperMock = mockStatic(StudentHelper.class)) {
            studentHelperMock.when(() -> StudentHelper.fromStudentReportData(any()))
                    .thenReturn(Collections.emptyList());

            MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
            doReturn(testStudentReport).when(spy).convertToStudentReport(inputStream);
            doReturn(Collections.emptyList()).when(spy).convertToStudentUsers(testStudentReport);

            // Act
            spy.saveStudentUsers(inputStream);

            // Assert
            verify(cohortRepository, never()).deleteAll();
            verify(cohortRepository, never()).save(any(Cohort.class));
            verify(scoreService).claimOrphanedScores();
        }
    }

    @Test
    void saveStudentImages_IOExceptionHandling() throws IOException {
        // Arrange - Create an invalid zip stream
        InputStream invalidInputStream = new ByteArrayInputStream("not a zip".getBytes());

        // Act & Assert - Should not throw exception, but handle gracefully
        assertDoesNotThrow(() -> {
            medSISAssessmentService.saveStudentImages(invalidInputStream);
        });
    }

    @Test
    void saveAssessments_NullAssessmentFromTransaction() throws IOException {
        // Arrange
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());
        Assessment testAssessment = new Assessment();
        testAssessment.setRemoteId(1L);

        MedSISAssessmentServiceImpl spy = spy(medSISAssessmentService);
        doReturn(testEvaluationReport).when(spy).convertToEvaluationReport(inputStream);
        doReturn(Arrays.asList(testAssessment)).when(spy).convertToAssessments(testEvaluationReport);

        when(transactionService.saveAssessment(any(Assessment.class))).thenReturn(null);

        // Act
        spy.saveAssessments(inputStream);

        // Assert
        verify(transactionService).saveAssessment(testAssessment);
        verify(houseKeepingService).keep(Collections.emptyList());
    }
}