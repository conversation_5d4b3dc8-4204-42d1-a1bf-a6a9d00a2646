package ca.medit.learnerchart.service;

import ca.medit.learnerchart.common.exception.MedITException;
import ca.medit.learnerchart.entity.CanMedsMapItem;
import ca.medit.learnerchart.entity.CplanXid;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CplanXidServiceImplTest {

    @Mock
    private CanMedsRoleService canMedsRoleService;

    @InjectMocks
    private CplanXidServiceImpl cplanXidService;

    private List<CanMedsMapItem> mockCanMedsMapItems;
    private CanMedsMapItem canMedsMapItem1;
    private CanMedsMapItem canMedsMapItem2;
    private CanMedsMapItem canMedsMapItem3;

    @BeforeEach
    void setUp() {
        // Create test data
        canMedsMapItem1 = new CanMedsMapItem();
        canMedsMapItem1.setParentCplanXid(1L);
        canMedsMapItem1.setParentFrameworkDef("Medical Expert");
        canMedsMapItem1.setParentLabel("Medical Expert Role");
        canMedsMapItem1.setParentValue("medical_expert");
        canMedsMapItem1.setChildCplanXid(11L);
        canMedsMapItem1.setChildFrameworkDef("Clinical Skills");
        canMedsMapItem1.setChildLabel("Clinical Skills Competency");
        canMedsMapItem1.setChildValue("clinical_skills");
        canMedsMapItem1.setLevel(2);

        canMedsMapItem2 = new CanMedsMapItem();
        canMedsMapItem2.setParentCplanXid(2L);
        canMedsMapItem2.setParentFrameworkDef("Communicator");
        canMedsMapItem2.setParentLabel("Communicator Role");
        canMedsMapItem2.setParentValue("communicator");
        canMedsMapItem2.setChildCplanXid(21L);
        canMedsMapItem2.setChildFrameworkDef("Patient Communication");
        canMedsMapItem2.setChildLabel("Patient Communication Skills");
        canMedsMapItem2.setChildValue("patient_communication");
        canMedsMapItem2.setLevel(2);

        canMedsMapItem3 = new CanMedsMapItem();
        canMedsMapItem3.setParentCplanXid(3L);
        canMedsMapItem3.setParentFrameworkDef("Collaborator");
        canMedsMapItem3.setParentLabel("Collaborator Role");
        canMedsMapItem3.setParentValue("collaborator");
        canMedsMapItem3.setChildCplanXid(31L);
        canMedsMapItem3.setChildFrameworkDef("Team Work");
        canMedsMapItem3.setChildLabel("Team Work Skills");
        canMedsMapItem3.setChildValue("team_work");
        canMedsMapItem3.setLevel(2);

        mockCanMedsMapItems = Arrays.asList(canMedsMapItem1, canMedsMapItem2, canMedsMapItem3);
    }

    @Test
    void testGetCanMedsRolesCplanXids_Success() {
        // Given
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(mockCanMedsMapItems);

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify first CplanXid
        CplanXid firstCplanXid = result.get(0);
        assertEquals(canMedsMapItem1.getParentCplanXid(), firstCplanXid.getCplanXidId());
        assertEquals(canMedsMapItem1.getParentLabel(), firstCplanXid.getLabel());
        assertEquals(canMedsMapItem1.getParentValue(), firstCplanXid.getValue());

        // Verify second CplanXid
        CplanXid secondCplanXid = result.get(1);
        assertEquals(canMedsMapItem2.getParentCplanXid(), secondCplanXid.getCplanXidId());
        assertEquals(canMedsMapItem2.getParentLabel(), secondCplanXid.getLabel());
        assertEquals(canMedsMapItem2.getParentValue(), secondCplanXid.getValue());

        // Verify third CplanXid
        CplanXid thirdCplanXid = result.get(2);
        assertEquals(canMedsMapItem3.getParentCplanXid(), thirdCplanXid.getCplanXidId());
        assertEquals(canMedsMapItem3.getParentLabel(), thirdCplanXid.getLabel());
        assertEquals(canMedsMapItem3.getParentValue(), thirdCplanXid.getValue());

        // Verify service method was called
        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_EmptyList() {
        // Given
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(new ArrayList<>());

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_SingleItem() {
        // Given
        List<CanMedsMapItem> singleItemList = Arrays.asList(canMedsMapItem1);
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(singleItemList);

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        CplanXid cplanXid = result.get(0);
        assertEquals(canMedsMapItem1.getParentCplanXid(), cplanXid.getCplanXidId());
        assertEquals(canMedsMapItem1.getParentLabel(), cplanXid.getLabel());
        assertEquals(canMedsMapItem1.getParentValue(), cplanXid.getValue());

        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_WithNullValues() {
        // Given
        CanMedsMapItem itemWithNulls = new CanMedsMapItem();
        itemWithNulls.setParentCplanXid(null);
        itemWithNulls.setParentLabel(null);
        itemWithNulls.setParentValue(null);
        itemWithNulls.setLevel(2);

        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(Arrays.asList(itemWithNulls));

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        CplanXid cplanXid = result.get(0);
        assertNull(cplanXid.getCplanXidId());
        assertNull(cplanXid.getLabel());
        assertNull(cplanXid.getValue());

        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_MedITExceptionThrown() {
        // Given
        MedITException originalException = new MedITException("Database connection failed");
        when(canMedsRoleService.getCanMedsRolesItems()).thenThrow(originalException);

        // When & Then
        MedITException thrownException = assertThrows(MedITException.class, () -> {
            cplanXidService.getCanMedsRolesCplanXids();
        });

        assertEquals("Unable to get CanMedsRoles CplanXids", thrownException.getMessage());
        assertEquals(originalException, thrownException.getCause());
        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }
    @Test
    void testGetCanMedsRolesCplanXids_LargeDataSet() {
        // Given
        List<CanMedsMapItem> largeDataSet = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            CanMedsMapItem item = new CanMedsMapItem();
            item.setParentCplanXid((long) i);
            item.setParentLabel("Label " + i);
            item.setParentValue("value_" + i);
            item.setLevel(2);
            largeDataSet.add(item);
        }
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(largeDataSet);

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(100, result.size());

        // Verify first and last items
        assertEquals(1L, result.get(0).getCplanXidId());
        assertEquals("Label 1", result.get(0).getLabel());
        assertEquals("value_1", result.get(0).getValue());

        assertEquals(100L, result.get(99).getCplanXidId());
        assertEquals("Label 100", result.get(99).getLabel());
        assertEquals("value_100", result.get(99).getValue());

        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_DataIntegrity() {
        // Given
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(mockCanMedsMapItems);

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(mockCanMedsMapItems.size(), result.size());

        // Verify that only parent data is mapped (not child data)
        for (int i = 0; i < result.size(); i++) {
            CplanXid cplanXid = result.get(i);
            CanMedsMapItem originalItem = mockCanMedsMapItems.get(i);

            // Parent data should be mapped
            assertEquals(originalItem.getParentCplanXid(), cplanXid.getCplanXidId());
            assertEquals(originalItem.getParentLabel(), cplanXid.getLabel());
            assertEquals(originalItem.getParentValue(), cplanXid.getValue());

            // Other fields should be null (not set by the service)
            assertNull(cplanXid.getFrameworkDef());
            assertNull(cplanXid.getCreatedBy());
            assertNull(cplanXid.getCreatedOn());
            assertNull(cplanXid.getDeleted());
            assertNull(cplanXid.getOptLock());
            assertNull(cplanXid.getUpdatedBy());
            assertNull(cplanXid.getUpdatedOn());
            assertNull(cplanXid.getRemoteId());
        }

        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
    }

    @Test
    void testGetCanMedsRolesCplanXids_VerifyTransactionalBehavior() {
        // Given
        when(canMedsRoleService.getCanMedsRolesItems()).thenReturn(mockCanMedsMapItems);

        // When
        List<CplanXid> result = cplanXidService.getCanMedsRolesCplanXids();

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify that the method is called exactly once (transactional behavior)
        verify(canMedsRoleService, times(1)).getCanMedsRolesItems();
        verifyNoMoreInteractions(canMedsRoleService);
    }
}