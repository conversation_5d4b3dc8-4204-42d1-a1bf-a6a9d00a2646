# Maven
 # Build your Java project and run tests with Apache Maven.
 # Add steps that analyze code, save build artifacts, deploy, and more:
 # https://docs.microsoft.com/azure/devops/pipelines/languages/java
 
 trigger:
 - main
 
 stages:
   # Build Stage
   - stage: Build
     jobs:
       - job: Job_BuildProcess
         pool:
           vmImage: ubuntu-latest
 
         steps:
           - task: JavaToolInstaller@0
             inputs:
               versionSpec: '21'
               jdkArchitectureOption: 'x64'
               jdkSourceOption: 'PreInstalled'
           - task: Maven@3
             inputs:
               mavenPomFile: 'pom.xml'
               mavenOptions: '-Xmx3072m'
               javaHomeOption: 'JDKVersion'
               jdkVersionOption: '21'
               jdkArchitectureOption: 'x64'
               publishJUnitResults: true
               testResultsFiles: '**/surefire-reports/TEST-*.xml'
               goals: 'verify'  # Changed from 'package' to 'verify'
               options: '-Pcoverage'  # Added coverage profile
 
           # Add task to publish code coverage results
           - task: PublishCodeCoverageResults@2
             inputs:
               codeCoverageTool: 'JaCoCo'
               summaryFileLocation: '$(Build.SourcesDirectory)/**/site/jacoco/jacoco.xml'
               reportDirectory: '$(Build.SourcesDirectory)/**/site/jacoco'
               failIfCoverageEmpty: true
 
           # Rename JAR file after build
           - script: |
               mv $(Build.SourcesDirectory)/target/getstart-0.0.1-SNAPSHOT.jar $(Build.SourcesDirectory)/target/learnerchart-app-8101.jar
             displayName: "Rename JAR file"
 
           # Move JAR to Artifact Staging Directory
           - task: CopyFiles@2
             inputs:
               SourceFolder: '$(Build.SourcesDirectory)/target'
               Contents: 'learnerchart-app-8101.jar'
               TargetFolder: '$(Build.ArtifactStagingDirectory)/learnerchart-app-8101'
 
           # Publish JAR as an Artifact
           - task: PublishBuildArtifacts@1
             inputs:
               pathToPublish: '$(Build.ArtifactStagingDirectory)/learnerchart-app-8101'
               artifactName: 'learnerchart-app-8101'
 
   # Stage 2: Deploy to QA Servers (CD)
   - stage: DeployToQA
     jobs:
       - job: DeployToSelfHostedAgent
         pool:
           name: 'SDAPP-server-Pool-QA'

         steps:
           # 1. Download build artifacts
           - task: DownloadBuildArtifacts@0
             inputs:
               buildType: 'current'
               downloadType: 'single'
               artifactName: 'learnerchart-app-8101'
               downloadPath: '$(Pipeline.Workspace)/deploy'
             displayName: 'Download Build Artifacts'
           
           # 2. Extract and deploy files
           - powershell: |
               # Define paths
               $artifactPath = "$(Pipeline.Workspace)/deploy/learnerchart-app-8101/learnerchart-app-8101.jar"
               $wwwRootDir = "F:\data\www"
               $releaseRootDir = "$wwwRootDir\learnerchart.qa.medicine.utoronto.ca\java-apps"
               $currentReleaseDir = "$releaseRootDir\current"
               $newReleaseDir = "$releaseRootDir\release-$(Build.BuildId)-$(BUILD.BUILDNUMBER)"
           
               if (-Not (Test-Path -Path $newReleaseDir)) {
                   New-Item -ItemType Directory -Path $newReleaseDir
               }
           
               Write-Output "Copying JAR from $artifactPath to $newReleaseDir"
               Copy-Item -Path $artifactPath -Destination $newReleaseDir -Force
           
               Write-Output "Stopping the Learnerchart service"
               E:\server\nssm\latest\win64\nssm.exe stop "MEDDN - Learnerchart App - 8101"
               
               # Update symbolic link to point to the new release
               Remove-Item "$releaseRootDir\current" -Recurse -Force
               New-Item -ItemType SymbolicLink -Path "$releaseRootDir\current" -Value "$newReleaseDir"
           
               $jarFile = Get-ChildItem -Path $newReleaseDir -Filter "learnerchart-app-8101.jar" | Select-Object -First 1
               if ($jarFile) {
                 Write-Output "Starting Java application: $jarFile"
                 E:\server\nssm\latest\win64\nssm.exe start "MEDDN - Learnerchart App - 8101"
               } else {
                 Write-Error "No JAR file found in $newReleaseDir"
               }
             displayName: 'Deploy Learnerchart JAR Application to QA'

  # Stage 3: Deploy to UAT Servers (CD)
   - stage: DeployToUAT
     dependsOn: DeployToQA
     condition: succeeded()
     jobs:
     - job: ApprovalStepForUAT
       displayName: "Approval for UAT Deployment"
       pool: server 
       steps:
        - task: ManualValidation@1
          timeoutInMinutes: 1440 
          inputs:
            notifyUsers: |
              <EMAIL>
            approvers: |
              <EMAIL>
            instructions: | 
              🚀 **UAT Deployment Approval Required**

                Please review the QA deployment results and approve the release to UAT.
                Build ID: $(Build.BuildId)
                Branch: $(Build.SourceBranchName)
                Commit: $(Build.SourceVersion)
                Requested by: $(Build.RequestedFor)
                
                Click **Approve** or **Reject** below.

            allowApproversToApproveTheirOwnRuns: true
     - job: DeployToSelfHostedAgent
       displayName: 'Deploy Learnerchart JAR Application to UAT'
       strategy:
        matrix:
          MEDDNUAT-APPW01:
            agent: MEDDNUAT-APPW01
          MEDDNUAT-APPW02:
            agent: MEDDNUAT-APPW02
       pool:
        name: 'SDAPP-server-Pool-UAT'

       steps:
        - task: DownloadBuildArtifacts@0
          inputs:
            buildType: 'current'
            downloadType: 'single'
            artifactName: 'learnerchart-app-8101'
            downloadPath: '$(Pipeline.Workspace)/deploy'
          displayName: 'Download Build Artifacts for UAT'

        - powershell: |
            $artifactPath = "$(Pipeline.Workspace)/deploy/learnerchart-app-8101/learnerchart-app-8101.jar"
            $wwwRootDir = "F:\data\www"
            $releaseRootDir = "$wwwRootDir\learnerchart.uat.medicine.utoronto.ca\java-apps"
            $currentReleaseDir = "$releaseRootDir\current"
            $newReleaseDir = "$releaseRootDir\release-$(Build.BuildId)-$(BUILD.BUILDNUMBER)"

            if (-Not (Test-Path -Path $newReleaseDir)) {
                New-Item -ItemType Directory -Path $newReleaseDir
            }

            Write-Output "Copying JAR from $artifactPath to $newReleaseDir"
            Copy-Item -Path $artifactPath -Destination $newReleaseDir -Force

            Write-Output "Stopping the Learnerchart service (UAT)"
            E:\server\nssm\latest\win64\nssm.exe stop "MEDDN - Learnerchart App - 8101"

            Remove-Item "$releaseRootDir\current" -Recurse -Force
            New-Item -ItemType SymbolicLink -Path "$releaseRootDir\current" -Value "$newReleaseDir"

        
            # Set Spring profile to 'uat'
            E:\server\nssm\latest\win64\nssm.exe set "MEDDN - Learnerchart App - 8101" AppParameters " -Djava.library.path=E:\server\sqljdbc\x64 -jar `"$newReleaseDir\learnerchart-app-8101.jar`" --spring.profiles.active=uat"


            # Start the service
            $jarFile = Get-ChildItem -Path $newReleaseDir -Filter "learnerchart-app-8101.jar" | Select-Object -First 1
            if ($jarFile) {
              Write-Output "Starting Java application (UAT): $jarFile"
              E:\server\nssm\latest\win64\nssm.exe start "MEDDN - Learnerchart App - 8101"
            } else {
              Write-Error "No JAR file found in $newReleaseDir"
            }            