# Learnerchart Application

---

## 🚀 Overview

Learnerchart is a Java-based web application designed for tracking and managing learner assessments and evaluations within medical education. It primarily serves the University of Toronto's Faculty of Medicine, facilitating student performance tracking and evaluation management.

---

## ✨ Quality Assessment

* **Overall Code Quality Score:** 7/10
* **Test Coverage:** Unknown (No test metrics available in provided excerpts)
* **Technical Debt Level:** Medium
* **Architecture Maturity:** 3.5/5

---

## 💡 Core Features

* **Assessment Management:** Facilitates the import and processing of student evaluations.
* **Score Tracking:** Manages and records student performance scores.
* **Alert System:** Provides notifications to users based on significant assessment events.
* **Authentication:** Utilizes JWT (JSON Web Tokens) for secure user access.

### Business Domains

* **Assessments:** Central to student evaluations.
* **Scores:** Tracks performance metrics.
* **Users:** Manages user accounts and authentication.
* **Alerts:** Handles notifications for important events.

### User Stories

* Medical educators can import evaluation data.
* Students can view their performance metrics.
* Administrators can manage assessment events.
* Users receive alerts about important assessment updates.

### Data Flow

1. Evaluation data is imported via file uploads.
2. The Assessment service processes and stores this evaluation data.
3. The Score service links scores to the appropriate assessments.
4. The Alert service notifies users of relevant events.

---

## 🏛️ Technical Architecture

### Technology Stack

* **Backend:** Java with Spring Framework
* **Build System:** Maven
* **Authentication:** JWT (JSON Web Tokens)
* **CI/CD:** Azure Pipelines and GitHub Actions
* **Deployment:** Azure Web Apps and self-hosted servers
* **Frontend:** Likely JavaScript/jQuery (based on `.eslintrc.json`)

### System Architecture

#### Database Design

The application appears to use a relational database with key entities including:

* Assessments
* Scores
* Users
* Assessment Events

#### Integration Points

* **File system:** Used for evaluation data import.
* **MedSIS (Medical Student Information System):** Possible integration point.
* **Database:** For persistent data storage.

---

## 🔎 Code Quality Analysis

### Strengths

* **Service-Oriented Architecture:** Demonstrates clear separation of concerns with dedicated services for specific functionalities.
* **Asynchronous Processing:** Utilizes `@Async` for efficient background task execution.
* **Security Implementation:** Incorporates JWT-based authentication for secure access.
* **CI/CD Pipeline:** Features a comprehensive deployment pipeline for QA and UAT environments.

### Areas for Improvement

* **Hard-coded Secrets:** The JWT secret key is hard-coded in `JwtUtil.java`, which is a security risk.
* **Concurrent Task Management:** The simple map-based locking mechanism in `MedSISTaskServiceImpl` could be improved for robustness.
* **Exception Handling:** Some services employ generic exception handling, which can obscure specific error details.
* **Configuration Management:** Property files are excluded from version control (`.gitignore`), which can complicate environment-specific configurations.

### Technical Debt

* **Security Hardening:** Move secrets to a secure configuration management system.
* **Improved Concurrency:** Replace simple map-based locks with more robust concurrency solutions.
* **Configuration Management:** Implement proper configuration handling for different environments.
* **Documentation:** Enhance code documentation and JavaDocs for better maintainability.

### Security Considerations

* The current JWT implementation uses a hard-coded secret key.
* No visible input validation in the provided code excerpts.
* The deployment pipeline includes proper environment separation (QA, UAT).

---

## 💻 Development Information

### Prerequisites

* **JDK 21:** (Based on Azure pipeline configuration)
* **Maven:** For build management
* **Git:** For version control

### Setup Instructions

1. Clone the repository.
2. Ensure **JDK 21** is installed and configured.
3. Use the Maven wrapper (`./mvnw` or `mvnw.cmd`) to build the project.
4. Configure application properties for your specific environment.

### Build and Deploy

* **Local Development:** Use the Maven wrapper (`./mvnw spring-boot:run`).
* **CI/CD Pipeline:**
  * **Azure Pipelines:** For main deployment to QA and UAT environments.
  * **GitHub Actions:** For Azure Web App deployments.

### Configuration

* The application uses **Spring profiles** for environment-specific configurations.
* Different profiles exist for QA and UAT environments.
* Configuration properties are currently excluded from version control.

---

## 📊 Code Metrics and Statistics

### Codebase Statistics

* Limited metrics available from provided excerpts.
* Java-based backend with a JavaScript frontend.
* Spring Framework-based architecture.

### Test Coverage Report

* No test coverage information available in the provided excerpts.

### Dependency Analysis

* Spring Framework (implied by annotations)
* JUnit for testing (implied by pipeline configuration)
* JaCoCo for code coverage
* Log4j or similar for logging (based on `log.catching()` usage)
* JWT libraries for authentication

### Performance Metrics

* No specific performance metrics available in the provided excerpts.

---

## ✅ Recommendations

### Immediate Actions

* **Secure JWT Secret:** Move the JWT secret key from the code to a secure configuration management system.
* **Improve Exception Handling:** Enhance error handling in service implementations to provide more specific feedback.

### Short-term Improvements

* **Enhance Concurrency Management:** Replace map-based locks with more robust concurrency utilities.
* **Improve Configuration Management:** Implement proper configuration templates and documentation for various environments.
* **Add Comprehensive Logging:** Ensure consistent logging across all services for better debugging and monitoring.

### Long-term Strategic

* **API Documentation:** Implement Swagger/OpenAPI for clear and comprehensive REST API documentation.
* **Microservices Evaluation:** Consider breaking down the application into microservices if it aligns with future scaling needs.
* **Frontend Modernization:** Evaluate moving from jQuery to a more modern JavaScript framework.

### Best Practices

* **Code Reviews:** Implement mandatory code reviews for all code changes to maintain quality.
* **Testing Standards:** Establish minimum test coverage requirements to ensure application stability.
* **Security Scanning:** Integrate security scanning into the CI/CD pipeline to identify vulnerabilities early.
* **Documentation:** Maintain up-to-date documentation for all services and APIs.

---

## ⚙️ Deployment Architecture

The application is deployed to multiple environments:

* **QA Environment:** Self-hosted Windows servers utilizing NSSM service management.
* **UAT Environment:** Self-hosted Windows servers with a manual approval process.
* **Azure Web Apps:** Multiple instances for different purposes.

The deployment process includes:

1. Building with Maven.
2. Running tests and collecting code coverage.
3. Packaging the application as a JAR.
4. Deploying to the appropriate environment.
5. Service management via NSSM on Windows servers.

---

This README provides an overview of the Learnerchart application based on the available code excerpts. For more detailed information, please refer to specific documentation or contact the development team.
